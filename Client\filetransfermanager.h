#ifndef FILETRANSFERMANAGER_H
#define FILETRANSFERMANAGER_H

#include "../Common/common.h"
#include <QFile>
#include <QMap>
#include <QMutex>
#include <QMutexLocker>
#include <QObject>
#include <QTimer>

class NetworkManager;

// 文件传输任务结构
struct TransferTask {
  quint32 taskId;           // 任务ID
  quint32 fileId;           // 文件ID
  QString fileName;         // 文件名
  quint64 fileSize;         // 文件大小
  quint64 bytesTransferred; // 已传输字节数
  QString localPath;        // 本地路径
  QString remotePath;       // 远程路径
  TransferType type;        // 传输类型（上传/下载）
  TransferStatus status;    // 传输状态
  QString errorMessage;     // 错误信息
  QDateTime startTime;      // 开始时间
  QDateTime endTime;        // 结束时间
  quint32 speed;            // 传输速度（字节/秒）
  int remainingTime;        // 剩余时间（秒）

  TransferTask()
      : taskId(0), fileId(0), fileSize(0), bytesTransferred(0),
        type(TRANSFER_UPLOAD), status(TRANSFER_PENDING), speed(0),
        remainingTime(0) {}
};

// 文件传输管理器类（单例模式）
class FileTransferManager : public QObject {
  Q_OBJECT

public:
  // 获取单例实例
  static FileTransferManager *getInstance();

  // 添加上传任务
  quint32 addUploadTask(const QString &localPath, quint32 parentId);

  // 添加下载任务
  quint32 addDownloadTask(quint32 fileId, const QString &localPath);

  // 开始任务
  bool startTask(quint32 taskId);

  // 暂停任务
  bool pauseTask(quint32 taskId);

  // 继续任务
  bool resumeTask(quint32 taskId);

  // 取消任务
  bool cancelTask(quint32 taskId);

  // 获取任务列表
  QList<TransferTask> getTaskList() const;

  // 获取任务
  TransferTask getTask(quint32 taskId) const;

  // 更新任务进度
  bool updateTaskProgress(quint32 taskId, quint64 bytesTransferred);

  // 完成任务
  bool completeTask(quint32 taskId);

  // 任务失败
  bool failTask(quint32 taskId, const QString &errorMessage);

  // 清理已完成任务
  void cleanupCompletedTasks();

signals:
  // 任务状态改变信号
  void taskStatusChanged(quint32 taskId, TransferStatus status);

  // 任务进度更新信号
  void taskProgressUpdated(quint32 taskId, quint64 bytesTransferred,
                           quint32 fileSize);

  // 任务速度更新信号
  void taskSpeedUpdated(quint32 taskId, quint32 speed, int remainingTime);

  // 新任务添加信号
  void taskAdded(quint32 taskId);

  // 数据块准备好发送信号
  void chunkReadyToSend(quint32 taskId, quint64 offset, const QByteArray &data);

  // 请求下一个数据块信号
  void requestNextChunk(quint32 taskId, quint64 offset, quint32 size);

  // 任务重试信号
  void taskRetried(quint32 taskId);

private slots:
  // 速度计算定时器槽函数
  void onSpeedCalculationTimeout();

public slots:
  // 分块传输相关方法（公开为槽以便connect）
  void sendNextChunk(quint32 taskId);                 // 发送下一个数据块
  void handleChunkSent(quint32 taskId, bool success); // 处理数据块发送完成
  void handleChunkReceived(quint32 taskId,
                           const QByteArray &data); // 处理接收到的数据块
  void handleChunkError(quint32 taskId,
                        const QString &error); // 处理数据块传输错误
  void retryTransfer(quint32 taskId);          // 重试传输

private:
  // 私有构造函数
  FileTransferManager(QObject *parent = nullptr);

  // 私有析构函数
  ~FileTransferManager();

  // 禁止拷贝构造和赋值
  FileTransferManager(const FileTransferManager &) = delete;
  FileTransferManager &operator=(const FileTransferManager &) = delete;

  // 生成任务ID
  quint32 generateTaskId();

  // 计算传输速度
  void calculateTransferSpeed();

  // 更新任务状态
  void updateTaskStatus(quint32 taskId, TransferStatus status);

  // 获取块大小
  quint32 getChunkSize() const { return m_chunkSize; }
  // 设置块大小
  void setChunkSize(quint32 size) { m_chunkSize = size; }

  // 获取最大并发传输数
  int getMaxConcurrentTransfers() const { return m_maxConcurrentTransfers; }
  // 设置最大并发传输数
  void setMaxConcurrentTransfers(int count) {
    m_maxConcurrentTransfers = count;
  }

  static FileTransferManager *m_instance; // 单例实例
  static QMutex m_mutex;                  // 互斥锁

  QMap<quint32, TransferTask> m_tasks;    // 任务映射
  QMap<quint32, QFile *> m_uploadFiles;   // 上传文件映射
  QMap<quint32, QFile *> m_downloadFiles; // 下载文件映射
  QTimer *m_speedCalculationTimer;        // 速度计算定时器
  quint32 m_nextTaskId;                   // 下一个任务ID

  // 分块传输相关成员变量
  quint32 m_chunkSize;                   // 块大小（字节）
  int m_maxConcurrentTransfers;          // 最大并发传输数
  QMap<quint32, quint64> m_chunkOffsets; // 当前块的偏移量
  QMap<quint32, quint32> m_chunkCounts;  // 总块数
  QMap<quint32, quint32> m_sentChunks;   // 已发送/接收的块数

  NetworkManager *m_networkManager; // 网络管理器
};

#endif // FILETRANSFERMANAGER_H
