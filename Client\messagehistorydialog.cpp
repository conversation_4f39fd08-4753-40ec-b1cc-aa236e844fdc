#include "messagehistorydialog.h"
#include "logger.h"
#include "ui_messagehistorydialog.h"
#include <QDateTime>

// 构造函数
MessageHistoryDialog::MessageHistoryDialog(QWidget *parent)
    : QDialog(parent), ui(new Ui::MessageHistoryDialog), m_friendInfo(),
      m_offset(0), m_count(20) {
  // 初始化UI
  ui->setupUi(this);

  // 初始化UI
  initUI();
}

// 析构函数
MessageHistoryDialog::~MessageHistoryDialog() { delete ui; }

// 设置好友信息
void MessageHistoryDialog::setFriendInfo(const FriendInfo &friendInfo) {
  m_friendInfo = friendInfo;

  // 更新好友名称标签
  ui->label_friend_name->setText(
      QString("与 %s 的聊天记录").arg(friendInfo.username));
}

// 设置消息列表
void MessageHistoryDialog::setMessageList(
    const QList<MessageInfo> &messageList) {
  // 保存消息列表
  m_messageList = messageList;

  // 更新偏移量
  if (!messageList.isEmpty()) {
    m_offset = messageList.first().msgId;
  }

  // 加载消息列表
  loadMessageList(messageList);
}

// 加载更多按钮点击槽函数
void MessageHistoryDialog::on_pushButton_load_more_clicked() {
  // 发送获取历史消息请求信号
  emit getMessageHistoryRequested(m_friendInfo.friendId, m_offset, m_count);
}

// 关闭按钮点击槽函数
void MessageHistoryDialog::on_pushButton_close_clicked() {
  // 关闭对话框
  reject();
}

// 初始化UI
void MessageHistoryDialog::initUI() {
  // 设置窗口标题
  setWindowTitle("消息历史");

  // 设置窗口图标
  setWindowIcon(QIcon(":/icons/history.png"));

  // 设置窗口大小
  resize(600, 400);

  // 设置列表属性
  ui->listWidget_messages->setAlternatingRowColors(true);
  ui->listWidget_messages->setSelectionMode(QAbstractItemView::NoSelection);
  ui->listWidget_messages->setVerticalScrollMode(
      QAbstractItemView::ScrollPerPixel);

  // 连接信号槽
  connect(ui->pushButton_load_more, &QPushButton::clicked, this,
          &MessageHistoryDialog::on_pushButton_load_more_clicked);
  connect(ui->pushButton_close, &QPushButton::clicked, this,
          &MessageHistoryDialog::on_pushButton_close_clicked);
}

// 加载消息列表
void MessageHistoryDialog::loadMessageList(
    const QList<MessageInfo> &messageList) {
  // 清空列表
  ui->listWidget_messages->clear();

  // 添加消息到列表
  for (const MessageInfo &messageInfo : messageList) {
    // 创建列表项
    QListWidgetItem *item = new QListWidgetItem();

    // 创建布局
    QWidget *widget = new QWidget();
    QHBoxLayout *layout = new QHBoxLayout(widget);

    // 创建头像标签
    QLabel *avatarLabel = new QLabel();
    avatarLabel->setFixedSize(40, 40);
    avatarLabel->setStyleSheet(
        "border-radius: 20px; background-color: #f0f0f0;");

    // 创建消息内容布局
    QVBoxLayout *messageLayout = new QVBoxLayout();

    // 创建用户名标签
    QLabel *usernameLabel =
        new QLabel(isCurrentUserMessage(messageInfo) ? "我" : "好友");
    usernameLabel->setStyleSheet("font-weight: bold;");

    // 创建消息时间标签
    QLabel *timeLabel = new QLabel(formatMessageTime(messageInfo.sendTime));
    timeLabel->setStyleSheet("color: gray; font-size: 12px;");

    // 创建消息内容标签
    QLabel *contentLabel = new QLabel(messageInfo.content);
    contentLabel->setWordWrap(true);
    contentLabel->setStyleSheet(
        "background-color: #f0f0f0; padding: 5px; border-radius: 5px;");

    // 添加到消息内容布局
    messageLayout->addWidget(usernameLabel);
    messageLayout->addWidget(timeLabel);
    messageLayout->addWidget(contentLabel);

    // 根据消息来源设置布局方向
    if (isCurrentUserMessage(messageInfo)) {
      // 当前用户发送的消息，靠右显示
      layout->addStretch();
      layout->addWidget(avatarLabel);
      layout->addLayout(messageLayout);
    } else {
      // 好友发送的消息，靠左显示
      layout->addLayout(messageLayout);
      layout->addWidget(avatarLabel);
      layout->addStretch();
    }

    // 设置布局的边距
    layout->setContentsMargins(5, 5, 5, 5);

    // 设置列表项的大小
    item->setSizeHint(widget->sizeHint());

    // 添加到列表
    ui->listWidget_messages->addItem(item);
    ui->listWidget_messages->setItemWidget(item, widget);
  }

  // 滚动到顶部
  ui->listWidget_messages->scrollToTop();

  LOG_INFO("MessageHistory",
           QString("加载消息历史完成，共 %1 条消息").arg(messageList.size()));
}

// 格式化消息时间
QString MessageHistoryDialog::formatMessageTime(const QString &time) const {
  // 解析时间字符串
  QDateTime dateTime = QDateTime::fromString(time, "yyyy-MM-dd hh:mm:ss");

  // 如果时间无效，返回原始字符串
  if (!dateTime.isValid()) {
    return time;
  }

  // 获取当前时间
  QDateTime now = QDateTime::currentDateTime();

  // 计算时间差
  qint64 seconds = dateTime.secsTo(now);

  // 如果是今天，只显示时间
  if (seconds < 86400) {
    return dateTime.toString("hh:mm");
  }
  // 如果是昨天，显示"昨天 时间"
  else if (seconds < 172800) {
    return "昨天 " + dateTime.toString("hh:mm");
  }
  // 如果是本周，显示"星期几 时间"
  else if (seconds < 604800) {
    return "星期" + QString::number(dateTime.date().dayOfWeek()) + " " +
           dateTime.toString("hh:mm");
  }
  // 否则显示日期和时间
  else {
    return dateTime.toString("MM-dd hh:mm");
  }
}

// 判断是否为当前用户发送的消息
bool MessageHistoryDialog::isCurrentUserMessage(
    const MessageInfo &messageInfo) const {
  // 比较发送者ID
  return messageInfo.senderId == m_friendInfo.friendId;
}
