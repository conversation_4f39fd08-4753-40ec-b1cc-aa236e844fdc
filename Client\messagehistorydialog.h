#ifndef MESSAGEHISTORYDIALOG_H
#define MESSAGEHISTORYDIALOG_H

#include <QDialog>
#include "../Common/common.h"

namespace Ui {
class MessageHistoryDialog;
}

class MessageHistoryDialog : public QDialog
{
    Q_OBJECT

public:
    explicit MessageHistoryDialog(QWidget *parent = nullptr);
    ~MessageHistoryDialog();

    // 设置好友信息
    void setFriendInfo(const FriendInfo& friendInfo);

    // 设置消息列表
    void setMessageList(const QList<MessageInfo>& messageList);

signals:
    // 获取历史消息请求信号
    void getMessageHistoryRequested(quint32 friendId, quint32 offset, quint32 count);

private slots:
    // 加载更多按钮点击槽函数
    void on_pushButton_load_more_clicked();

    // 关闭按钮点击槽函数
    void on_pushButton_close_clicked();

private:
    // 初始化UI
    void initUI();

    // 加载消息列表
    void loadMessageList(const QList<MessageInfo>& messageList);

    // 格式化消息时间
    QString formatMessageTime(const QString& time) const;

    // 判断是否为当前用户发送的消息
    bool isCurrentUserMessage(const MessageInfo& messageInfo) const;

    Ui::MessageHistoryDialog *ui;           // UI对象
    FriendInfo m_friendInfo;                // 好友信息
    QList<MessageInfo> m_messageList;      // 消息列表
    quint32 m_offset;                       // 消息偏移量
    quint32 m_count;                       // 消息数量
};

#endif // MESSAGEHISTORYDIALOG_H
