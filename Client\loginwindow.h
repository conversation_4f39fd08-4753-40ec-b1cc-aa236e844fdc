#ifndef LOGINWINDOW_H
#define LOGINWINDOW_H

#include <QMainWindow>
#include "ui_loginwindow.h"
#include "networkmanager.h"
#include "configmanager.h"
#include "serversettingsdialog.h"
#include "registerdialog.h"
#include "mainwindow.h"

class LoginWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit LoginWindow(QWidget *parent = nullptr);
    ~LoginWindow();

private slots:
    // 登录按钮点击槽函数
    void on_pushButton_login_clicked();

    // 注册按钮点击槽函数
    void on_pushButton_register_clicked();

    // 服务器设置按钮点击槽函数
    void on_pushButton_settings_clicked();

    // 网络连接状态改变槽函数
    void onConnectionStateChanged(bool connected);

    // 登录响应槽函数
    void onLoginResponse(bool success, const QString& message, const UserInfo& userInfo);

    // 注册响应槽函数
    void onRegisterResponse(bool success, const QString& message);

    // 错误处理槽函数
    void onErrorOccurred(const QString& error);

private:
    // 初始化UI
    void initUI();

    // 加载配置
    void loadConfig();

    // 保存配置
    void saveConfig();

    // 验证输入
    bool validateInput();

    Ui::LoginWindow *ui;                // UI对象
    NetworkManager* m_networkManager;    // 网络管理器
    ConfigManager* m_configManager;      // 配置管理器
    ServerSettingsDialog* m_settingsDialog; // 服务器设置对话框
    RegisterDialog* m_registerDialog;    // 注册对话框
    MainWindow* m_mainWindow;            // 主窗口
};

#endif // LOGINWINDOW_H
