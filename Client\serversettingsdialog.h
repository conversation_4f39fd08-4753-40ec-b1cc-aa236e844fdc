#ifndef SERVERSETTINGSDIALOG_H
#define SERVERSETTINGSDIALOG_H

#include <QDialog>
#include "ui_serversettingsdialog.h"
#include "configmanager.h"

class ServerSettingsDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ServerSettingsDialog(QWidget *parent = nullptr);
    ~ServerSettingsDialog();

private slots:
    // 对话框接受槽函数
    void accept() override;

private:
    // 初始化UI
    void initUI();

    // 加载配置
    void loadConfig();

    // 保存配置
    void saveConfig();

    // 验证输入
    bool validateInput();

    Ui::ServerSettingsDialog *ui;     // UI对象
    ConfigManager* m_configManager;   // 配置管理器
};

#endif // SERVERSETTINGSDIALOG_H
