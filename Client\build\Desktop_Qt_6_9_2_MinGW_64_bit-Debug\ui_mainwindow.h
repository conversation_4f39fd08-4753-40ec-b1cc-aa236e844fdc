/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtGui/QAction>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenu>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QTreeWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QAction *action_upload;
    QAction *action_new_folder;
    QAction *action_refresh;
    QAction *action_exit;
    QAction *action_select_all;
    QAction *action_copy;
    QAction *action_cut;
    QAction *action_paste;
    QAction *action_delete;
    QAction *action_rename;
    QAction *action_list_view;
    QAction *action_icon_view;
    QAction *action_search;
    QAction *action_share;
    QAction *action_settings;
    QAction *action_about;
    QWidget *centralwidget;
    QHBoxLayout *horizontalLayout;
    QSplitter *splitter_main;
    QWidget *layout_widget_left;
    QVBoxLayout *verticalLayout_left;
    QTabWidget *tabWidget_left;
    QWidget *tab_files;
    QVBoxLayout *verticalLayout_files;
    QTreeWidget *treeWidget_files;
    QWidget *tab_friends;
    QVBoxLayout *verticalLayout_friends;
    QLineEdit *lineEdit_search_friends;
    QListWidget *listWidget_friends;
    QWidget *layout_widget_center;
    QVBoxLayout *verticalLayout_center;
    QTabWidget *tabWidget_center;
    QWidget *tab_home;
    QVBoxLayout *verticalLayout_home;
    QLabel *label_welcome;
    QSpacerItem *verticalSpacer;
    QWidget *layout_widget_right;
    QVBoxLayout *verticalLayout_right;
    QTabWidget *tabWidget_right;
    QWidget *tab_file_info;
    QFormLayout *formLayout_file_info;
    QLabel *label_file_name_title;
    QLabel *label_file_name;
    QLabel *label_file_size_title;
    QLabel *label_file_size;
    QLabel *label_file_type_title;
    QLabel *label_file_type;
    QLabel *label_file_modify_time_title;
    QLabel *label_file_modify_time;
    QLabel *label_file_path_title;
    QLabel *label_file_path;
    QSpacerItem *verticalSpacer_file_info;
    QWidget *tab_messages;
    QVBoxLayout *verticalLayout_messages;
    QListWidget *listWidget_messages;
    QMenuBar *menubar;
    QMenu *menu_file;
    QMenu *menu_edit;
    QMenu *menu_view;
    QMenu *menu_tools;
    QMenu *menu_help;
    QStatusBar *statusbar;
    QToolBar *toolBar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName("MainWindow");
        MainWindow->resize(1000, 700);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/cloud.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        MainWindow->setWindowIcon(icon);
        action_upload = new QAction(MainWindow);
        action_upload->setObjectName("action_upload");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/upload.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        action_upload->setIcon(icon1);
        action_new_folder = new QAction(MainWindow);
        action_new_folder->setObjectName("action_new_folder");
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/new_folder.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        action_new_folder->setIcon(icon2);
        action_refresh = new QAction(MainWindow);
        action_refresh->setObjectName("action_refresh");
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/icons/refresh.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        action_refresh->setIcon(icon3);
        action_exit = new QAction(MainWindow);
        action_exit->setObjectName("action_exit");
        action_select_all = new QAction(MainWindow);
        action_select_all->setObjectName("action_select_all");
        action_copy = new QAction(MainWindow);
        action_copy->setObjectName("action_copy");
        action_cut = new QAction(MainWindow);
        action_cut->setObjectName("action_cut");
        action_paste = new QAction(MainWindow);
        action_paste->setObjectName("action_paste");
        action_delete = new QAction(MainWindow);
        action_delete->setObjectName("action_delete");
        QIcon icon4;
        icon4.addFile(QString::fromUtf8(":/icons/delete.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        action_delete->setIcon(icon4);
        action_rename = new QAction(MainWindow);
        action_rename->setObjectName("action_rename");
        QIcon icon5;
        icon5.addFile(QString::fromUtf8(":/icons/rename.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        action_rename->setIcon(icon5);
        action_list_view = new QAction(MainWindow);
        action_list_view->setObjectName("action_list_view");
        action_list_view->setCheckable(true);
        action_list_view->setChecked(true);
        action_icon_view = new QAction(MainWindow);
        action_icon_view->setObjectName("action_icon_view");
        action_icon_view->setCheckable(true);
        action_search = new QAction(MainWindow);
        action_search->setObjectName("action_search");
        QIcon icon6;
        icon6.addFile(QString::fromUtf8(":/icons/search.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        action_search->setIcon(icon6);
        action_share = new QAction(MainWindow);
        action_share->setObjectName("action_share");
        QIcon icon7;
        icon7.addFile(QString::fromUtf8(":/icons/share.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        action_share->setIcon(icon7);
        action_settings = new QAction(MainWindow);
        action_settings->setObjectName("action_settings");
        action_about = new QAction(MainWindow);
        action_about->setObjectName("action_about");
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName("centralwidget");
        horizontalLayout = new QHBoxLayout(centralwidget);
        horizontalLayout->setObjectName("horizontalLayout");
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        splitter_main = new QSplitter(centralwidget);
        splitter_main->setObjectName("splitter_main");
        splitter_main->setOrientation(Qt::Horizontal);
        layout_widget_left = new QWidget(splitter_main);
        layout_widget_left->setObjectName("layout_widget_left");
        verticalLayout_left = new QVBoxLayout(layout_widget_left);
        verticalLayout_left->setObjectName("verticalLayout_left");
        verticalLayout_left->setContentsMargins(0, 0, 0, 0);
        tabWidget_left = new QTabWidget(layout_widget_left);
        tabWidget_left->setObjectName("tabWidget_left");
        tab_files = new QWidget();
        tab_files->setObjectName("tab_files");
        verticalLayout_files = new QVBoxLayout(tab_files);
        verticalLayout_files->setObjectName("verticalLayout_files");
        verticalLayout_files->setContentsMargins(5, 5, 5, 5);
        treeWidget_files = new QTreeWidget(tab_files);
        QTreeWidgetItem *__qtreewidgetitem = new QTreeWidgetItem();
        __qtreewidgetitem->setText(0, QString::fromUtf8("1"));
        treeWidget_files->setHeaderItem(__qtreewidgetitem);
        treeWidget_files->setObjectName("treeWidget_files");
        treeWidget_files->setHeaderHidden(true);

        verticalLayout_files->addWidget(treeWidget_files);

        tabWidget_left->addTab(tab_files, QString());
        tab_friends = new QWidget();
        tab_friends->setObjectName("tab_friends");
        verticalLayout_friends = new QVBoxLayout(tab_friends);
        verticalLayout_friends->setObjectName("verticalLayout_friends");
        verticalLayout_friends->setContentsMargins(5, 5, 5, 5);
        lineEdit_search_friends = new QLineEdit(tab_friends);
        lineEdit_search_friends->setObjectName("lineEdit_search_friends");

        verticalLayout_friends->addWidget(lineEdit_search_friends);

        listWidget_friends = new QListWidget(tab_friends);
        listWidget_friends->setObjectName("listWidget_friends");

        verticalLayout_friends->addWidget(listWidget_friends);

        tabWidget_left->addTab(tab_friends, QString());

        verticalLayout_left->addWidget(tabWidget_left);

        splitter_main->addWidget(layout_widget_left);
        layout_widget_center = new QWidget(splitter_main);
        layout_widget_center->setObjectName("layout_widget_center");
        verticalLayout_center = new QVBoxLayout(layout_widget_center);
        verticalLayout_center->setObjectName("verticalLayout_center");
        verticalLayout_center->setContentsMargins(0, 0, 0, 0);
        tabWidget_center = new QTabWidget(layout_widget_center);
        tabWidget_center->setObjectName("tabWidget_center");
        tabWidget_center->setTabsClosable(true);
        tabWidget_center->setMovable(true);
        tab_home = new QWidget();
        tab_home->setObjectName("tab_home");
        verticalLayout_home = new QVBoxLayout(tab_home);
        verticalLayout_home->setObjectName("verticalLayout_home");
        label_welcome = new QLabel(tab_home);
        label_welcome->setObjectName("label_welcome");
        label_welcome->setAlignment(Qt::AlignCenter);

        verticalLayout_home->addWidget(label_welcome);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout_home->addItem(verticalSpacer);

        tabWidget_center->addTab(tab_home, QString());

        verticalLayout_center->addWidget(tabWidget_center);

        splitter_main->addWidget(layout_widget_center);
        layout_widget_right = new QWidget(splitter_main);
        layout_widget_right->setObjectName("layout_widget_right");
        verticalLayout_right = new QVBoxLayout(layout_widget_right);
        verticalLayout_right->setObjectName("verticalLayout_right");
        verticalLayout_right->setContentsMargins(0, 0, 0, 0);
        tabWidget_right = new QTabWidget(layout_widget_right);
        tabWidget_right->setObjectName("tabWidget_right");
        tab_file_info = new QWidget();
        tab_file_info->setObjectName("tab_file_info");
        formLayout_file_info = new QFormLayout(tab_file_info);
        formLayout_file_info->setObjectName("formLayout_file_info");
        label_file_name_title = new QLabel(tab_file_info);
        label_file_name_title->setObjectName("label_file_name_title");

        formLayout_file_info->setWidget(0, QFormLayout::ItemRole::LabelRole, label_file_name_title);

        label_file_name = new QLabel(tab_file_info);
        label_file_name->setObjectName("label_file_name");

        formLayout_file_info->setWidget(0, QFormLayout::ItemRole::FieldRole, label_file_name);

        label_file_size_title = new QLabel(tab_file_info);
        label_file_size_title->setObjectName("label_file_size_title");

        formLayout_file_info->setWidget(1, QFormLayout::ItemRole::LabelRole, label_file_size_title);

        label_file_size = new QLabel(tab_file_info);
        label_file_size->setObjectName("label_file_size");

        formLayout_file_info->setWidget(1, QFormLayout::ItemRole::FieldRole, label_file_size);

        label_file_type_title = new QLabel(tab_file_info);
        label_file_type_title->setObjectName("label_file_type_title");

        formLayout_file_info->setWidget(2, QFormLayout::ItemRole::LabelRole, label_file_type_title);

        label_file_type = new QLabel(tab_file_info);
        label_file_type->setObjectName("label_file_type");

        formLayout_file_info->setWidget(2, QFormLayout::ItemRole::FieldRole, label_file_type);

        label_file_modify_time_title = new QLabel(tab_file_info);
        label_file_modify_time_title->setObjectName("label_file_modify_time_title");

        formLayout_file_info->setWidget(3, QFormLayout::ItemRole::LabelRole, label_file_modify_time_title);

        label_file_modify_time = new QLabel(tab_file_info);
        label_file_modify_time->setObjectName("label_file_modify_time");

        formLayout_file_info->setWidget(3, QFormLayout::ItemRole::FieldRole, label_file_modify_time);

        label_file_path_title = new QLabel(tab_file_info);
        label_file_path_title->setObjectName("label_file_path_title");

        formLayout_file_info->setWidget(4, QFormLayout::ItemRole::LabelRole, label_file_path_title);

        label_file_path = new QLabel(tab_file_info);
        label_file_path->setObjectName("label_file_path");
        label_file_path->setWordWrap(true);

        formLayout_file_info->setWidget(4, QFormLayout::ItemRole::FieldRole, label_file_path);

        verticalSpacer_file_info = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        formLayout_file_info->setItem(5, QFormLayout::ItemRole::SpanningRole, verticalSpacer_file_info);

        tabWidget_right->addTab(tab_file_info, QString());
        tab_messages = new QWidget();
        tab_messages->setObjectName("tab_messages");
        verticalLayout_messages = new QVBoxLayout(tab_messages);
        verticalLayout_messages->setObjectName("verticalLayout_messages");
        listWidget_messages = new QListWidget(tab_messages);
        listWidget_messages->setObjectName("listWidget_messages");

        verticalLayout_messages->addWidget(listWidget_messages);

        tabWidget_right->addTab(tab_messages, QString());

        verticalLayout_right->addWidget(tabWidget_right);

        splitter_main->addWidget(layout_widget_right);

        horizontalLayout->addWidget(splitter_main);

        MainWindow->setCentralWidget(centralwidget);
        menubar = new QMenuBar(MainWindow);
        menubar->setObjectName("menubar");
        menubar->setGeometry(QRect(0, 0, 1000, 22));
        menu_file = new QMenu(menubar);
        menu_file->setObjectName("menu_file");
        menu_edit = new QMenu(menubar);
        menu_edit->setObjectName("menu_edit");
        menu_view = new QMenu(menubar);
        menu_view->setObjectName("menu_view");
        menu_tools = new QMenu(menubar);
        menu_tools->setObjectName("menu_tools");
        menu_help = new QMenu(menubar);
        menu_help->setObjectName("menu_help");
        MainWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName("statusbar");
        MainWindow->setStatusBar(statusbar);
        toolBar = new QToolBar(MainWindow);
        toolBar->setObjectName("toolBar");
        MainWindow->addToolBar(Qt::ToolBarArea::TopToolBarArea, toolBar);

        menubar->addAction(menu_file->menuAction());
        menubar->addAction(menu_edit->menuAction());
        menubar->addAction(menu_view->menuAction());
        menubar->addAction(menu_tools->menuAction());
        menubar->addAction(menu_help->menuAction());
        menu_file->addAction(action_upload);
        menu_file->addAction(action_new_folder);
        menu_file->addSeparator();
        menu_file->addAction(action_refresh);
        menu_file->addSeparator();
        menu_file->addAction(action_exit);
        menu_edit->addAction(action_select_all);
        menu_edit->addSeparator();
        menu_edit->addAction(action_copy);
        menu_edit->addAction(action_cut);
        menu_edit->addAction(action_paste);
        menu_edit->addSeparator();
        menu_edit->addAction(action_delete);
        menu_edit->addAction(action_rename);
        menu_view->addAction(action_list_view);
        menu_view->addAction(action_icon_view);
        menu_tools->addAction(action_search);
        menu_tools->addAction(action_share);
        menu_tools->addSeparator();
        menu_tools->addAction(action_settings);
        menu_help->addAction(action_about);
        toolBar->addAction(action_upload);
        toolBar->addAction(action_new_folder);
        toolBar->addSeparator();
        toolBar->addAction(action_refresh);
        toolBar->addSeparator();
        toolBar->addAction(action_delete);
        toolBar->addAction(action_rename);
        toolBar->addSeparator();
        toolBar->addAction(action_share);

        retranslateUi(MainWindow);

        tabWidget_left->setCurrentIndex(0);
        tabWidget_right->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "Cloud7", nullptr));
        action_upload->setText(QCoreApplication::translate("MainWindow", "\344\270\212\344\274\240", nullptr));
#if QT_CONFIG(shortcut)
        action_upload->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+U", nullptr));
#endif // QT_CONFIG(shortcut)
        action_new_folder->setText(QCoreApplication::translate("MainWindow", "\346\226\260\345\273\272\346\226\207\344\273\266\345\244\271", nullptr));
#if QT_CONFIG(shortcut)
        action_new_folder->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+N", nullptr));
#endif // QT_CONFIG(shortcut)
        action_refresh->setText(QCoreApplication::translate("MainWindow", "\345\210\267\346\226\260", nullptr));
#if QT_CONFIG(shortcut)
        action_refresh->setShortcut(QCoreApplication::translate("MainWindow", "F5", nullptr));
#endif // QT_CONFIG(shortcut)
        action_exit->setText(QCoreApplication::translate("MainWindow", "\351\200\200\345\207\272", nullptr));
#if QT_CONFIG(shortcut)
        action_exit->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+Q", nullptr));
#endif // QT_CONFIG(shortcut)
        action_select_all->setText(QCoreApplication::translate("MainWindow", "\345\205\250\351\200\211", nullptr));
#if QT_CONFIG(shortcut)
        action_select_all->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+A", nullptr));
#endif // QT_CONFIG(shortcut)
        action_copy->setText(QCoreApplication::translate("MainWindow", "\345\244\215\345\210\266", nullptr));
#if QT_CONFIG(shortcut)
        action_copy->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+C", nullptr));
#endif // QT_CONFIG(shortcut)
        action_cut->setText(QCoreApplication::translate("MainWindow", "\345\211\252\345\210\207", nullptr));
#if QT_CONFIG(shortcut)
        action_cut->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+X", nullptr));
#endif // QT_CONFIG(shortcut)
        action_paste->setText(QCoreApplication::translate("MainWindow", "\347\262\230\350\264\264", nullptr));
#if QT_CONFIG(shortcut)
        action_paste->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+V", nullptr));
#endif // QT_CONFIG(shortcut)
        action_delete->setText(QCoreApplication::translate("MainWindow", "\345\210\240\351\231\244", nullptr));
#if QT_CONFIG(shortcut)
        action_delete->setShortcut(QCoreApplication::translate("MainWindow", "Delete", nullptr));
#endif // QT_CONFIG(shortcut)
        action_rename->setText(QCoreApplication::translate("MainWindow", "\351\207\215\345\221\275\345\220\215", nullptr));
#if QT_CONFIG(shortcut)
        action_rename->setShortcut(QCoreApplication::translate("MainWindow", "F2", nullptr));
#endif // QT_CONFIG(shortcut)
        action_list_view->setText(QCoreApplication::translate("MainWindow", "\345\210\227\350\241\250\350\247\206\345\233\276", nullptr));
        action_icon_view->setText(QCoreApplication::translate("MainWindow", "\345\233\276\346\240\207\350\247\206\345\233\276", nullptr));
        action_search->setText(QCoreApplication::translate("MainWindow", "\346\220\234\347\264\242", nullptr));
#if QT_CONFIG(shortcut)
        action_search->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+F", nullptr));
#endif // QT_CONFIG(shortcut)
        action_share->setText(QCoreApplication::translate("MainWindow", "\345\210\206\344\272\253", nullptr));
#if QT_CONFIG(shortcut)
        action_share->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+S", nullptr));
#endif // QT_CONFIG(shortcut)
        action_settings->setText(QCoreApplication::translate("MainWindow", "\350\256\276\347\275\256", nullptr));
        action_about->setText(QCoreApplication::translate("MainWindow", "\345\205\263\344\272\216", nullptr));
        tabWidget_left->setTabText(tabWidget_left->indexOf(tab_files), QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266", nullptr));
        lineEdit_search_friends->setPlaceholderText(QCoreApplication::translate("MainWindow", "\346\220\234\347\264\242\345\245\275\345\217\213...", nullptr));
        tabWidget_left->setTabText(tabWidget_left->indexOf(tab_friends), QCoreApplication::translate("MainWindow", "\345\245\275\345\217\213", nullptr));
        label_welcome->setText(QCoreApplication::translate("MainWindow", "\346\254\242\350\277\216\344\275\277\347\224\250Cloud7\347\275\221\347\233\230\347\263\273\347\273\237", nullptr));
        tabWidget_center->setTabText(tabWidget_center->indexOf(tab_home), QCoreApplication::translate("MainWindow", "\344\270\273\351\241\265", nullptr));
        label_file_name_title->setText(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266\345\220\215\357\274\232", nullptr));
        label_file_name->setText(QCoreApplication::translate("MainWindow", "-", nullptr));
        label_file_size_title->setText(QCoreApplication::translate("MainWindow", "\345\244\247\345\260\217\357\274\232", nullptr));
        label_file_size->setText(QCoreApplication::translate("MainWindow", "-", nullptr));
        label_file_type_title->setText(QCoreApplication::translate("MainWindow", "\347\261\273\345\236\213\357\274\232", nullptr));
        label_file_type->setText(QCoreApplication::translate("MainWindow", "-", nullptr));
        label_file_modify_time_title->setText(QCoreApplication::translate("MainWindow", "\344\277\256\346\224\271\346\227\266\351\227\264\357\274\232", nullptr));
        label_file_modify_time->setText(QCoreApplication::translate("MainWindow", "-", nullptr));
        label_file_path_title->setText(QCoreApplication::translate("MainWindow", "\350\267\257\345\276\204\357\274\232", nullptr));
        label_file_path->setText(QCoreApplication::translate("MainWindow", "-", nullptr));
        tabWidget_right->setTabText(tabWidget_right->indexOf(tab_file_info), QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266\344\277\241\346\201\257", nullptr));
        tabWidget_right->setTabText(tabWidget_right->indexOf(tab_messages), QCoreApplication::translate("MainWindow", "\346\266\210\346\201\257", nullptr));
        menu_file->setTitle(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266", nullptr));
        menu_edit->setTitle(QCoreApplication::translate("MainWindow", "\347\274\226\350\276\221", nullptr));
        menu_view->setTitle(QCoreApplication::translate("MainWindow", "\350\247\206\345\233\276", nullptr));
        menu_tools->setTitle(QCoreApplication::translate("MainWindow", "\345\267\245\345\205\267", nullptr));
        menu_help->setTitle(QCoreApplication::translate("MainWindow", "\345\270\256\345\212\251", nullptr));
        toolBar->setWindowTitle(QCoreApplication::translate("MainWindow", "\345\267\245\345\205\267\346\240\217", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
