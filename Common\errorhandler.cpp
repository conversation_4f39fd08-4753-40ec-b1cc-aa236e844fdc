#include "errorhandler.h"
#include <QAbstractSocket>
#include <QSqlError>
#include <QFile>

// 初始化静态成员变量
ErrorHandler* ErrorHandler::m_instance = nullptr;
QMutex ErrorHandler::m_mutex;

// 获取单例实例
ErrorHandler* ErrorHandler::getInstance()
{
    if (m_instance == nullptr) {
        QMutexLocker locker(&m_mutex);
        if (m_instance == nullptr) {
            m_instance = new ErrorHandler();
        }
    }
    return m_instance;
}

// 私有构造函数
ErrorHandler::ErrorHandler(QObject *parent) : QObject(parent)
{
    // 初始化错误消息映射
    initErrorMessageMap();
}

// 私有析构函数
ErrorHandler::~ErrorHandler()
{
    // 清理资源
}

// 获取错误消息
QString ErrorHandler::getErrorMessage(ErrorCode errorCode) const
{
    // 检查错误消息映射中是否存在该错误码
    if (m_errorMessageMap.contains(errorCode)) {
        return m_errorMessageMap[errorCode];
    }

    // 如果不存在，返回默认消息
    return QString("未知错误 (错误码: %1)").arg(errorCode);
}

// 获取网络错误消息
QString ErrorHandler::getNetworkErrorMessage(QAbstractSocket::SocketError socketError) const
{
    switch (socketError) {
    case QAbstractSocket::ConnectionRefusedError:
        return "连接被服务器拒绝";
    case QAbstractSocket::RemoteHostClosedError:
        return "远程主机关闭了连接";
    case QAbstractSocket::HostNotFoundError:
        return "找不到主机";
    case QAbstractSocket::SocketAccessError:
        return "套接字访问错误";
    case QAbstractSocket::SocketResourceError:
        return "套接字资源错误";
    case QAbstractSocket::SocketTimeoutError:
        return "套接字超时";
    case QAbstractSocket::DatagramTooLargeError:
        return "数据报过大";
    case QAbstractSocket::NetworkError:
        return "网络错误";
    case QAbstractSocket::AddressInUseError:
        return "地址已在使用";
    case QAbstractSocket::SocketAddressNotAvailableError:
        return "地址不可用";
    case QAbstractSocket::UnsupportedSocketOperationError:
        return "不支持的套接字操作";
    case QAbstractSocket::ProxyAuthenticationRequiredError:
        return "需要代理认证";
    case QAbstractSocket::SslHandshakeFailedError:
        return "SSL握手失败";
    case QAbstractSocket::UnfinishedSocketOperationError:
        return "未完成的套接字操作";
    case QAbstractSocket::ProxyConnectionRefusedError:
        return "代理连接被拒绝";
    case QAbstractSocket::ProxyConnectionClosedError:
        return "代理连接关闭";
    case QAbstractSocket::ProxyConnectionTimeoutError:
        return "代理连接超时";
    case QAbstractSocket::ProxyNotFoundError:
        return "找不到代理";
    case QAbstractSocket::ProxyProtocolError:
        return "代理协议错误";
    case QAbstractSocket::OperationError:
        return "操作错误";
    case QAbstractSocket::SslInternalError:
        return "SSL内部错误";
    case QAbstractSocket::SslInvalidUserDataError:
        return "SSL无效用户数据";
    case QAbstractSocket::TemporaryError:
        return "临时错误";
    default:
        return "未知网络错误";
    }
}

// 获取数据库错误消息
QString ErrorHandler::getDatabaseErrorMessage(const QSqlError& sqlError) const
{
    switch (sqlError.type()) {
    case QSqlError::NoError:
        return "没有错误";
    case QSqlError::ConnectionError:
        return QString("数据库连接错误: %1").arg(sqlError.text());
    case QSqlError::StatementError:
        return QString("SQL语句错误: %1").arg(sqlError.text());
    case QSqlError::TransactionError:
        return QString("事务错误: %1").arg(sqlError.text());
    case QSqlError::UnknownError:
    default:
        return QString("未知数据库错误: %1").arg(sqlError.text());
    }
}

// 获取文件错误消息
QString ErrorHandler::getFileErrorMessage(QFile::FileError fileError) const
{
    switch (fileError) {
    case QFile::NoError:
        return "没有错误";
    case QFile::ReadError:
        return "读取文件错误";
    case QFile::WriteError:
        return "写入文件错误";
    case QFile::FatalError:
        return "致命错误";
    case QFile::ResourceError:
        return "资源错误";
    case QFile::OpenError:
        return "打开文件错误";
    case QFile::AbortError:
        return "操作被中止";
    case QFile::TimeOutError:
        return "操作超时";
    case QFile::UnspecifiedError:
        return "未指定错误";
    case QFile::RemoveError:
        return "删除文件错误";
    case QFile::RenameError:
        return "重命名文件错误";
    case QFile::PositionError:
        return "位置错误";
    case QFile::ResizeError:
        return "调整大小错误";
    case QFile::PermissionsError:
        return "权限错误";
    case QFile::CopyError:
        return "复制文件错误";
    default:
        return "未知文件错误";
    }
}

// 注册自定义错误消息
void ErrorHandler::registerCustomErrorMessage(ErrorCode errorCode, const QString& message)
{
    // 添加或更新错误消息
    m_errorMessageMap[errorCode] = message;
}

// 初始化错误消息映射
void ErrorHandler::initErrorMessageMap()
{
    // 初始化错误消息映射
    m_errorMessageMap[ERROR_NONE] = "没有错误";
    m_errorMessageMap[ERROR_NETWORK] = "网络错误";
    m_errorMessageMap[ERROR_TIMEOUT] = "操作超时";
    m_errorMessageMap[ERROR_SERVER] = "服务器错误";
    m_errorMessageMap[ERROR_DATABASE] = "数据库错误";
    m_errorMessageMap[ERROR_AUTH] = "认证失败";
    m_errorMessageMap[ERROR_PARAM] = "参数错误";
    m_errorMessageMap[ERROR_FILE_NOT_FOUND] = "文件不存在";
    m_errorMessageMap[ERROR_FILE_EXISTS] = "文件已存在";
    m_errorMessageMap[ERROR_PERMISSION] = "权限不足";
    m_errorMessageMap[ERROR_STORAGE_FULL] = "存储空间已满";
    m_errorMessageMap[ERROR_USER_EXISTS] = "用户已存在";
    m_errorMessageMap[ERROR_USER_NOT_FOUND] = "用户不存在";
    m_errorMessageMap[ERROR_FRIEND_EXISTS] = "好友关系已存在";
    m_errorMessageMap[ERROR_FRIEND_NOT_FOUND] = "好友不存在";
    m_errorMessageMap[ERROR_SHARE_NOT_FOUND] = "分享不存在";
    m_errorMessageMap[ERROR_SHARE_EXPIRED] = "分享已过期";
}
