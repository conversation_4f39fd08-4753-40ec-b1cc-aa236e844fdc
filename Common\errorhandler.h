#ifndef ERRORHANDLER_H
#define ERRORHANDLER_H

#include "../Common/common.h"
#include <QAbstractSocket>
#include <QFile>
#include <QMap>
#include <QMutex>
#include <QObject>
#include <QSqlError>
#include <QtSql>
#include <QString>


// 错误处理类（单例模式）
class ErrorHandler : public QObject {
  Q_OBJECT

public:
  // 获取单例实例
  static ErrorHandler *getInstance();

  // 获取错误消息
  QString getErrorMessage(ErrorCode errorCode) const;

  // 获取网络错误消息
  QString
  getNetworkErrorMessage(QAbstractSocket::SocketError socketError) const;

  // 获取数据库错误消息
  QString getDatabaseErrorMessage(const QSqlError &sqlError) const;

  // 获取文件错误消息
  QString getFileErrorMessage(QFile::FileError fileError) const;

  // 注册自定义错误消息
  void registerCustomErrorMessage(ErrorCode errorCode, const QString &message);

private:
  // 私有构造函数
  ErrorHandler(QObject *parent = nullptr);

  // 私有析构函数
  ~ErrorHandler();

  // 禁止拷贝构造和赋值
  ErrorHandler(const ErrorHandler &) = delete;
  ErrorHandler &operator=(const ErrorHandler &) = delete;

  // 初始化错误消息映射
  void initErrorMessageMap();

  static ErrorHandler *m_instance; // 单例实例
  static QMutex m_mutex;           // 互斥锁

  QMap<ErrorCode, QString> m_errorMessageMap; // 错误消息映射
};

#endif // ERRORHANDLER_H
