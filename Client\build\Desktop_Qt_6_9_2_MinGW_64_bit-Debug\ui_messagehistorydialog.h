/********************************************************************************
** Form generated from reading UI file 'messagehistorydialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MESSAGEHISTORYDIALOG_H
#define UI_MESSAGEHISTORYDIALOG_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_MessageHistoryDialog
{
public:
    QVBoxLayout *verticalLayout;
    QLabel *label_friend_name;
    QListWidget *listWidget_messages;
    QHBoxLayout *horizontalLayout_buttons;
    QSpacerItem *horizontalSpacer;
    QPushButton *pushButton_load_more;
    QPushButton *pushButton_close;

    void setupUi(QDialog *MessageHistoryDialog)
    {
        if (MessageHistoryDialog->objectName().isEmpty())
            MessageHistoryDialog->setObjectName("MessageHistoryDialog");
        MessageHistoryDialog->resize(600, 400);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/history.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        MessageHistoryDialog->setWindowIcon(icon);
        verticalLayout = new QVBoxLayout(MessageHistoryDialog);
        verticalLayout->setObjectName("verticalLayout");
        label_friend_name = new QLabel(MessageHistoryDialog);
        label_friend_name->setObjectName("label_friend_name");
        label_friend_name->setAlignment(Qt::AlignCenter);

        verticalLayout->addWidget(label_friend_name);

        listWidget_messages = new QListWidget(MessageHistoryDialog);
        listWidget_messages->setObjectName("listWidget_messages");
        listWidget_messages->setSelectionMode(QAbstractItemView::NoSelection);

        verticalLayout->addWidget(listWidget_messages);

        horizontalLayout_buttons = new QHBoxLayout();
        horizontalLayout_buttons->setObjectName("horizontalLayout_buttons");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer);

        pushButton_load_more = new QPushButton(MessageHistoryDialog);
        pushButton_load_more->setObjectName("pushButton_load_more");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/load_more.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_load_more->setIcon(icon1);

        horizontalLayout_buttons->addWidget(pushButton_load_more);

        pushButton_close = new QPushButton(MessageHistoryDialog);
        pushButton_close->setObjectName("pushButton_close");
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/close.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_close->setIcon(icon2);

        horizontalLayout_buttons->addWidget(pushButton_close);


        verticalLayout->addLayout(horizontalLayout_buttons);


        retranslateUi(MessageHistoryDialog);

        QMetaObject::connectSlotsByName(MessageHistoryDialog);
    } // setupUi

    void retranslateUi(QDialog *MessageHistoryDialog)
    {
        MessageHistoryDialog->setWindowTitle(QCoreApplication::translate("MessageHistoryDialog", "\346\266\210\346\201\257\345\216\206\345\217\262", nullptr));
        label_friend_name->setText(QCoreApplication::translate("MessageHistoryDialog", "\345\245\275\345\217\213\345\220\215\347\247\260\357\274\232", nullptr));
        pushButton_load_more->setText(QCoreApplication::translate("MessageHistoryDialog", "\345\212\240\350\275\275\346\233\264\345\244\232", nullptr));
        pushButton_close->setText(QCoreApplication::translate("MessageHistoryDialog", "\345\205\263\351\227\255", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MessageHistoryDialog: public Ui_MessageHistoryDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MESSAGEHISTORYDIALOG_H
