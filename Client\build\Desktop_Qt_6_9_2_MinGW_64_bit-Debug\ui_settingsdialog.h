/********************************************************************************
** Form generated from reading UI file 'settingsdialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SETTINGSDIALOG_H
#define UI_SETTINGSDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QAbstractButton>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDialogButtonBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QLabel>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_SettingsDialog
{
public:
    QVBoxLayout *verticalLayout;
    QTabWidget *tabWidget;
    QWidget *tab_general;
    QVBoxLayout *verticalLayout_2;
    QGroupBox *groupBox_general;
    QFormLayout *formLayout;
    QLabel *label_info;
    QSpacerItem *verticalSpacer;
    QWidget *tab_network;
    QVBoxLayout *verticalLayout_3;
    QGroupBox *groupBox_network;
    QFormLayout *formLayout_2;
    QLabel *label_timeout;
    QSpinBox *spinBox_timeout;
    QSpacerItem *verticalSpacer_2;
    QDialogButtonBox *buttonBox;

    void setupUi(QDialog *SettingsDialog)
    {
        if (SettingsDialog->objectName().isEmpty())
            SettingsDialog->setObjectName("SettingsDialog");
        SettingsDialog->resize(400, 300);
        verticalLayout = new QVBoxLayout(SettingsDialog);
        verticalLayout->setObjectName("verticalLayout");
        tabWidget = new QTabWidget(SettingsDialog);
        tabWidget->setObjectName("tabWidget");
        tab_general = new QWidget();
        tab_general->setObjectName("tab_general");
        verticalLayout_2 = new QVBoxLayout(tab_general);
        verticalLayout_2->setObjectName("verticalLayout_2");
        groupBox_general = new QGroupBox(tab_general);
        groupBox_general->setObjectName("groupBox_general");
        formLayout = new QFormLayout(groupBox_general);
        formLayout->setObjectName("formLayout");
        label_info = new QLabel(groupBox_general);
        label_info->setObjectName("label_info");

        formLayout->setWidget(0, QFormLayout::ItemRole::LabelRole, label_info);


        verticalLayout_2->addWidget(groupBox_general);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout_2->addItem(verticalSpacer);

        tabWidget->addTab(tab_general, QString());
        tab_network = new QWidget();
        tab_network->setObjectName("tab_network");
        verticalLayout_3 = new QVBoxLayout(tab_network);
        verticalLayout_3->setObjectName("verticalLayout_3");
        groupBox_network = new QGroupBox(tab_network);
        groupBox_network->setObjectName("groupBox_network");
        formLayout_2 = new QFormLayout(groupBox_network);
        formLayout_2->setObjectName("formLayout_2");
        label_timeout = new QLabel(groupBox_network);
        label_timeout->setObjectName("label_timeout");

        formLayout_2->setWidget(0, QFormLayout::ItemRole::LabelRole, label_timeout);

        spinBox_timeout = new QSpinBox(groupBox_network);
        spinBox_timeout->setObjectName("spinBox_timeout");
        spinBox_timeout->setMinimum(5);
        spinBox_timeout->setMaximum(60);
        spinBox_timeout->setValue(30);

        formLayout_2->setWidget(0, QFormLayout::ItemRole::FieldRole, spinBox_timeout);


        verticalLayout_3->addWidget(groupBox_network);

        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout_3->addItem(verticalSpacer_2);

        tabWidget->addTab(tab_network, QString());

        verticalLayout->addWidget(tabWidget);

        buttonBox = new QDialogButtonBox(SettingsDialog);
        buttonBox->setObjectName("buttonBox");
        buttonBox->setOrientation(Qt::Horizontal);
        buttonBox->setStandardButtons(QDialogButtonBox::Cancel|QDialogButtonBox::Ok);

        verticalLayout->addWidget(buttonBox);


        retranslateUi(SettingsDialog);
        QObject::connect(buttonBox, &QDialogButtonBox::accepted, SettingsDialog, qOverload<>(&QDialog::accept));
        QObject::connect(buttonBox, &QDialogButtonBox::rejected, SettingsDialog, qOverload<>(&QDialog::reject));

        tabWidget->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(SettingsDialog);
    } // setupUi

    void retranslateUi(QDialog *SettingsDialog)
    {
        SettingsDialog->setWindowTitle(QCoreApplication::translate("SettingsDialog", "\350\256\276\347\275\256", nullptr));
        groupBox_general->setTitle(QCoreApplication::translate("SettingsDialog", "\345\270\270\350\247\204\350\256\276\347\275\256", nullptr));
        label_info->setText(QCoreApplication::translate("SettingsDialog", "\345\237\272\347\241\200\350\256\276\347\275\256\345\267\262\347\256\200\345\214\226\357\274\214\344\273\205\344\277\235\347\225\231\347\275\221\347\273\234\350\277\236\346\216\245\351\205\215\347\275\256\343\200\202", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_general), QCoreApplication::translate("SettingsDialog", "\345\270\270\350\247\204", nullptr));
        groupBox_network->setTitle(QCoreApplication::translate("SettingsDialog", "\347\275\221\347\273\234\350\256\276\347\275\256", nullptr));
        label_timeout->setText(QCoreApplication::translate("SettingsDialog", "\350\277\236\346\216\245\350\266\205\346\227\266(\347\247\222):", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_network), QCoreApplication::translate("SettingsDialog", "\347\275\221\347\273\234", nullptr));
    } // retranslateUi

};

namespace Ui {
    class SettingsDialog: public Ui_SettingsDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SETTINGSDIALOG_H
