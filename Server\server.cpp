#include "server.h"
#include "configmanager.h"
#include "logger.h"
#include <QDateTime>
#include <QHostAddress>
#include <QJsonDocument>
#include <QJsonObject>
#include <QRandomGenerator>
#include <QTcpServer>
#include <QTcpSocket>
#include <QTimer>

// Server类实现
Server::Server(QObject *parent)
    : QObject(parent), m_tcpServer(nullptr), m_configManager(nullptr),
      m_dbManager(nullptr), m_running(false) {

  m_tcpServer = new QTcpServer(this);
  connect(m_tcpServer, &QTcpServer::newConnection, this,
          &Server::onNewConnection);

  m_sessionTimeoutTimer = new QTimer(this);
  connect(m_sessionTimeoutTimer, &QTimer::timeout, this,
          &Server::onSessionTimeoutCheck);
  m_sessionTimeoutTimer->start(30000);
}

Server::~Server() { stop(); }

bool Server::start() {
  // 从配置管理器获取端口
  quint16 port = ConfigManager::getInstance()->getPort();
  QString host = ConfigManager::getInstance()->getHost();

  QHostAddress address;
  if (host == "0.0.0.0") {
    address = QHostAddress::Any;
  } else {
    address = QHostAddress(host);
  }

  if (!m_tcpServer->listen(address, port)) {
    LOG_ERROR("Server", QString("无法在 %1:%2 上启动服务器: %3")
                            .arg(host)
                            .arg(port)
                            .arg(m_tcpServer->errorString()));
    return false;
  }

  LOG_INFO("Server",
           QString("服务器已启动，监听地址: %1:%2").arg(host).arg(port));
  m_running = true;
  return true;
}

void Server::stop() {
  if (m_tcpServer && m_tcpServer->isListening()) {
    m_tcpServer->close();
    m_running = false;
  }

  for (auto it = m_sessions.begin(); it != m_sessions.end(); ++it) {
    it.value()->deleteLater();
  }
  m_sessions.clear();
  m_userSessions.clear();
}

void Server::onNewConnection() {
  while (m_tcpServer->hasPendingConnections()) {
    QTcpSocket *socket = m_tcpServer->nextPendingConnection();
    ClientSession *session = new ClientSession(socket, this);

    QString sessionId = generateSessionId();
    m_sessions[sessionId] = session;

    connect(session, &ClientSession::disconnected, this,
            &Server::handleClientDisconnected);
    connect(session, &ClientSession::messageReceived, this,
            &Server::handleClientMessage);
  }
}

void Server::onSessionTimeoutCheck() {
  // TODO: 实现会话超时检查
}

void Server::handleClientDisconnected(ClientSession *session) {
  QString sessionId;
  for (auto it = m_sessions.begin(); it != m_sessions.end(); ++it) {
    if (it.value() == session) {
      sessionId = it.key();
      m_sessions.erase(it);
      break;
    }
  }

  for (auto it = m_userSessions.begin(); it != m_userSessions.end(); ++it) {
    if (it.value() == session) {
      m_userSessions.erase(it);
      break;
    }
  }

  session->deleteLater();
}

QString Server::generateSessionId() {
  return QString::number(QRandomGenerator::global()->generate64(), 16);
}

void Server::sendErrorResponse(ClientSession *session, quint32 originalMsgType,
                               const QString &message) {
  QJsonObject responseObj;
  responseObj["success"] = false;
  responseObj["message"] = message;
  session->sendJsonMessage(originalMsgType, responseObj);
}

// 所有处理函数的最小实现
void Server::handleClientMessage(ClientSession *session, const Packet &packet) {
  Q_UNUSED(session)
  Q_UNUSED(packet)
}

void Server::handleLoginRequest(ClientSession *session,
                                const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleLogoutRequest(ClientSession *session,
                                 const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleRegisterRequest(ClientSession *session,
                                   const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleFileData(ClientSession *session, const QByteArray &data) {
  Q_UNUSED(session)
  Q_UNUSED(data)
}

void Server::handleCompleteFileUpload(ClientSession *session,
                                      const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleDownloadFileRequest(ClientSession *session,
                                       const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleCreateDirectoryRequest(ClientSession *session,
                                          const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleDeleteFileRequest(ClientSession *session,
                                     const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleRenameFileRequest(ClientSession *session,
                                     const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleMoveFileRequest(ClientSession *session,
                                   const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleCopyFileRequest(ClientSession *session,
                                   const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleShareFileRequest(ClientSession *session,
                                    const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleSearchFileRequest(ClientSession *session,
                                     const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleGetFileByShareCodeRequest(ClientSession *session,
                                             const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleSearchUserRequest(ClientSession *session,
                                     const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleAddFriendRequest(ClientSession *session,
                                    const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleAgreeAddFriend(ClientSession *session,
                                  const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleRejectAddFriend(ClientSession *session,
                                   const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleDeleteFriendRequest(ClientSession *session,
                                       const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleGetFriendListRequest(ClientSession *session,
                                        const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleSendMessage(ClientSession *session,
                               const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleGetMessageHistoryRequest(ClientSession *session,
                                            const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

void Server::handleGetOfflineMessageRequest(ClientSession *session,
                                            const QJsonObject &jsonObj) {
  Q_UNUSED(session)
  Q_UNUSED(jsonObj)
}

// ClientSession类实现
ClientSession::ClientSession(QTcpSocket *socket, QObject *parent)
    : QObject(parent), m_socket(socket), m_expectedSize(0), m_userId(0) {

  connect(m_socket, &QTcpSocket::readyRead, this, &ClientSession::onReadyRead);
  connect(m_socket, &QTcpSocket::disconnected, this,
          &ClientSession::onDisconnected);
  connect(m_socket, &QTcpSocket::errorOccurred, this, &ClientSession::onError);

  m_lastHeartbeat = QDateTime::currentDateTime();
}

ClientSession::~ClientSession() {
  if (m_socket) {
    m_socket->deleteLater();
  }
}

bool ClientSession::sendPacket(const Packet &packet) {
  Q_UNUSED(packet)
  return false;
}

bool ClientSession::sendJsonMessage(quint32 msgType,
                                    const QJsonObject &jsonObj) {
  Q_UNUSED(msgType)
  Q_UNUSED(jsonObj)
  return false;
}

bool ClientSession::sendFileData(quint32 msgType, const QByteArray &fileData) {
  Q_UNUSED(msgType)
  Q_UNUSED(fileData)
  return false;
}

void ClientSession::onReadyRead() {
  // TODO: 实现数据读取处理
}

void ClientSession::onDisconnected() { emit disconnected(this); }

void ClientSession::onError(QAbstractSocket::SocketError socketError) {
  Q_UNUSED(socketError)
}

void ClientSession::processData(const QByteArray &data) { Q_UNUSED(data) }

void ClientSession::processPacket(const Packet &packet) { Q_UNUSED(packet) }

quint32 ClientSession::calculateChecksum(const QByteArray &data) const {
  Q_UNUSED(data)
  return 0;
}
