#ifndef UTILS_H
#define UTILS_H

#include <QString>
#include <QDateTime>
#include <QJsonObject>
#include <QJsonDocument>
#include <QCryptographicHash>
#include <QRandomGenerator>
#include <QStandardPaths>
#include <QDir>
#include "../Common/common.h"

// 工具类（静态方法类）
class Utils
{
public:
    // 计算密码哈希
    static QString calculatePasswordHash(const QString& password, const QString& salt);

    // 生成随机盐值
    static QString generateSalt(int length = 16);

    // 生成随机字符串
    static QString generateRandomString(int length);

    // 计算文件哈希
    static QString calculateFileHash(const QString& filePath);

    // 格式化文件大小
    static QString formatFileSize(quint64 size);

    // 格式化日期时间
    static QString formatDateTime(const QDateTime& dateTime);

    // 解析日期时间
    static QDateTime parseDateTime(const QString& dateTimeStr);

    // 格式化持续时间
    static QString formatDuration(int seconds);

    // 检查邮箱格式
    static bool isValidEmail(const QString& email);

    // 检查密码强度
    static bool isStrongPassword(const QString& password);

    // 获取应用数据目录
    static QString getAppDataDir();

    // 创建目录
    static bool createDirectory(const QString& path);

    // 删除文件或目录
    static bool deleteFile(const QString& path);

    // 复制文件
    static bool copyFile(const QString& srcPath, const QString& dstPath);

    // 移动文件
    static bool moveFile(const QString& srcPath, const QString& dstPath);

    // 获取文件扩展名
    static QString getFileExtension(const QString& fileName);

    // 获取文件类型
    static QString getFileType(const QString& fileName);

    // JSON对象转字符串
    static QString jsonToString(const QJsonObject& jsonObj);

    // 字符串转JSON对象
    static QJsonObject stringToJson(const QString& str);

    // URL编码
    static QString urlEncode(const QString& str);

    // URL解码
    static QString urlDecode(const QString& str);

    // Base64编码
    static QByteArray base64Encode(const QByteArray& data);

    // Base64解码
    static QByteArray base64Decode(const QByteArray& data);

    // 获取本机IP地址
    static QString getLocalIpAddress();

    // 检查端口是否被占用
    static bool isPortInUse(quint16 port);

    // 生成分享码
    static QString generateShareCode();

    // 验证分享码
    static bool validateShareCode(const QString& shareCode);
};

#endif // UTILS_H
