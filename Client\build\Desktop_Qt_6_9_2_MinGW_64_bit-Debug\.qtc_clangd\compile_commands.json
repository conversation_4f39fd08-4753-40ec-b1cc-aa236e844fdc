[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\errorhandler.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/errorhandler.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\exception.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/exception.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\utils.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/utils.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\changepassworddialog.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/changepassworddialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\chatwindow.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/chatwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\configmanager.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/configmanager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\filepropertiesdialog.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/filepropertiesdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\filesearchdialog.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/filesearchdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\filesharedialog.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/filesharedialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\filetransferdialog.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/filetransferdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\filetransfermanager.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/filetransfermanager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\fileviewwidget.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/fileviewwidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\friendapplydialog.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/friendapplydialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\friendsearchdialog.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/friendsearchdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\logger.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/logger.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\loginwindow.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/loginwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\main.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\mainwindow.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\messagehistorydialog.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/messagehistorydialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\networkmanager.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/networkmanager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\registerdialog.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/registerdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\serversettingsdialog.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/serversettingsdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\settingsdialog.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/settingsdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\usersettingsdialog.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/usersettingsdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\common.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/common.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\errorhandler.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/errorhandler.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\exception.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/exception.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\utils.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/utils.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\changepassworddialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/changepassworddialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\chatwindow.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/chatwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\configmanager.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/configmanager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\filepropertiesdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/filepropertiesdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\filesearchdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/filesearchdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\filesharedialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/filesharedialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\filetransferdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/filetransferdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\filetransfermanager.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/filetransfermanager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\fileviewwidget.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/fileviewwidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\friendapplydialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/friendapplydialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\friendsearchdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/friendsearchdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\logger.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/logger.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\loginwindow.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/loginwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\mainwindow.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\messagehistorydialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/messagehistorydialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\networkmanager.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/networkmanager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\registerdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/registerdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\serversettingsdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/serversettingsdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\settingsdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/settingsdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\usersettingsdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/usersettingsdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_friendsearchdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_friendsearchdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_registerdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_registerdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_filepropertiesdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_filepropertiesdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_mainwindow.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_messagehistorydialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_messagehistorydialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_filesharedialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_filesharedialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_friendapplydialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_friendapplydialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_filesearchdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_filesearchdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_chatwindow.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_chatwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_filetransferdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_filetransferdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_usersettingsdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_usersettingsdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_loginwindow.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_loginwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_settingsdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_settingsdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_serversettingsdialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_serversettingsdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_changepassworddialog.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_changepassworddialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Client\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\ui_fileviewwidget.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Client/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/ui_fileviewwidget.h"}]