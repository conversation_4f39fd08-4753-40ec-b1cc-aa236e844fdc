#ifndef REGISTERDIALOG_H
#define REGISTERDIALOG_H

#include <QDialog>
#include "ui_registerdialog.h"

class RegisterDialog : public QDialog
{
    Q_OBJECT

public:
    explicit RegisterDialog(QWidget *parent = nullptr);
    ~RegisterDialog();

signals:
    // 注册请求信号
    void registerRequested(const QString& username, const QString& password, const QString& email);

private slots:
    // 对话框接受槽函数
    void accept() override;

private:
    // 初始化UI
    void initUI();

    // 验证输入
    bool validateInput();

    Ui::RegisterDialog *ui;     // UI对象
};

#endif // REGISTERDIALOG_H
