QT       += core gui network sql

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    ../Common/errorhandler.cpp \
    ../Common/exception.cpp \
    ../Common/utils.cpp \
    chatwindow.cpp \
    configmanager.cpp \
    filepropertiesdialog.cpp \
    filesearchdialog.cpp \
    filesharedialog.cpp \
    filetransferdialog.cpp \
    filetransfermanager.cpp \
    fileviewwidget.cpp \
    friendapplydialog.cpp \
    friendsearchdialog.cpp \
    logger.cpp \
    loginwindow.cpp \
    main.cpp \
    mainwindow.cpp \
    messagehistorydialog.cpp \
    networkmanager.cpp \
    registerdialog.cpp \
    serversettingsdialog.cpp \
    settingsdialog.cpp \
    usersettingsdialog.cpp

HEADERS += \
    ../Common/common.h \
    ../Common/errorhandler.h \
    ../Common/exception.h \
    ../Common/utils.h \
    chatwindow.h \
    configmanager.h \
    filepropertiesdialog.h \
    filesearchdialog.h \
    filesharedialog.h \
    filetransferdialog.h \
    filetransfermanager.h \
    fileviewwidget.h \
    friendapplydialog.h \
    friendsearchdialog.h \
    logger.h \
    loginwindow.h \
    mainwindow.h \
    messagehistorydialog.h \
    networkmanager.h \
    registerdialog.h \
    serversettingsdialog.h \
    settingsdialog.h \
    usersettingsdialog.h

FORMS += \
    chatwindow.ui \
    filepropertiesdialog.ui \
    filesearchdialog.ui \
    filesharedialog.ui \
    filetransferdialog.ui \
    fileviewwidget.ui \
    friendapplydialog.ui \
    friendsearchdialog.ui \
    loginwindow.ui \
    mainwindow.ui \
    messagehistorydialog.ui \
    registerdialog.ui \
    serversettingsdialog.ui \
    settingsdialog.ui \
    usersettingsdialog.ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

RESOURCES += \
    Resource.qrc
