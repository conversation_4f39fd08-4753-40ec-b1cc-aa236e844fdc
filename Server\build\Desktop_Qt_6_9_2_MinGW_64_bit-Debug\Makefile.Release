#############################################################################
# Makefile for building: Server
# Generated by qmake (3.1) (Qt 6.9.2)
# Project:  ../../Server.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_QML_DEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_SQL_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -O2 -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -O2 -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I../../../Server -I. -IF:/QT/QT6.9.2/6.9.2/mingw_64/include -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtWidgets -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtGui -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore -Irelease -I/include -IF:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-s -Wl,-subsystem,windows -mthreads
LIBS        =        F:/QT/QT6.9.2/6.9.2/mingw_64/lib/libQt6Widgets.a F:/QT/QT6.9.2/6.9.2/mingw_64/lib/libQt6Gui.a F:/QT/QT6.9.2/6.9.2/mingw_64/lib/libQt6Network.a F:/QT/QT6.9.2/6.9.2/mingw_64/lib/libQt6Sql.a F:/QT/QT6.9.2/6.9.2/mingw_64/lib/libQt6Core.a -lmingw32 F:/QT/QT6.9.2/6.9.2/mingw_64/lib/libQt6EntryPoint.a -lshell32  
QMAKE         = F:/QT/QT6.9.2/6.9.2/mingw_64/bin/qmake.exe
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = cp -f
INSTALL_PROGRAM = cp -f
INSTALL_DIR   = cp -f -R
QINSTALL      = F:/QT/QT6.9.2/6.9.2/mingw_64/bin/qmake.exe -install qinstall
QINSTALL_PROGRAM = F:/QT/QT6.9.2/6.9.2/mingw_64/bin/qmake.exe -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
IDC           = idc
IDL           = midl
ZIP           = 
DEF_FILE      = 
RES_FILE      = 
SED           = sed
MOVE          = mv -f

####### Output directory

OBJECTS_DIR   = release/

####### Files

SOURCES       = ../../../Common/errorhandler.cpp \
		../../../Common/exception.cpp \
		../../../Common/utils.cpp \
		../../configmanager.cpp \
		../../databasemanager.cpp \
		../../filehandler.cpp \
		../../logger.cpp \
		../../main.cpp \
		../../server.cpp release/moc_errorhandler.cpp \
		release/moc_configmanager.cpp \
		release/moc_databasemanager.cpp \
		release/moc_filehandler.cpp \
		release/moc_logger.cpp \
		release/moc_server.cpp
OBJECTS       = release/errorhandler.o \
		release/exception.o \
		release/utils.o \
		release/configmanager.o \
		release/databasemanager.o \
		release/filehandler.o \
		release/logger.o \
		release/main.o \
		release/server.o \
		release/moc_errorhandler.o \
		release/moc_configmanager.o \
		release/moc_databasemanager.o \
		release/moc_filehandler.o \
		release/moc_logger.o \
		release/moc_server.o

DIST          = server.config ../../../Common/common.h \
		../../../Common/errorhandler.h \
		../../../Common/exception.h \
		../../../Common/utils.h \
		../../configmanager.h \
		../../databasemanager.h \
		../../filehandler.h \
		../../logger.h \
		../../server.h ../../../Common/errorhandler.cpp \
		../../../Common/exception.cpp \
		../../../Common/utils.cpp \
		../../configmanager.cpp \
		../../databasemanager.cpp \
		../../filehandler.cpp \
		../../logger.cpp \
		../../main.cpp \
		../../server.cpp
QMAKE_TARGET  = Server
DESTDIR        = release/ #avoid trailing-slash linebreak
TARGET         = Server.exe
DESTDIR_TARGET = release/Server.exe

####### Build rules

first: all
all: Makefile.Release  release/Server.exe

release/Server.exe: F:/QT/QT6.9.2/6.9.2/mingw_64/lib/libQt6Widgets.a F:/QT/QT6.9.2/6.9.2/mingw_64/lib/libQt6Gui.a F:/QT/QT6.9.2/6.9.2/mingw_64/lib/libQt6Network.a F:/QT/QT6.9.2/6.9.2/mingw_64/lib/libQt6Sql.a F:/QT/QT6.9.2/6.9.2/mingw_64/lib/libQt6Core.a F:/QT/QT6.9.2/6.9.2/mingw_64/lib/libQt6EntryPoint.a $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) @release/object_script.Server.Release $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Release ../../Server.pro -spec win32-g++ CONFIG+=debug CONFIG+=qml_debug

qmake_all: FORCE

dist:
	$(ZIP) Server.zip $(SOURCES) $(DIST) ../../Server.pro F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/spec_pre.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/device_config.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/sanitize.conf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/gcc-base.conf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/g++-base.conf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/win32/windows_vulkan_sdk.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/windows-vulkan.conf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/g++-win32.conf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/windows-desktop.conf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/qconfig.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_ext_freetype.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_ext_libjpeg.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_ext_libpng.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_ext_openxr_loader.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_charts.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_charts_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_chartsqml.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_chartsqml_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_concurrent.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_concurrent_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_core.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_core_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_dbus.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_dbus_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_designer.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_designer_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_designercomponents_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_entrypoint_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_example_icons_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_examples_asset_downloader_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_fb_support_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_ffmpegmediapluginimpl_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_freetype_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_gui.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_gui_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_harfbuzz_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_help.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_help_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_jpeg_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsanimation.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsanimation_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsfolderlistmodel.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsfolderlistmodel_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsplatform.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsplatform_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsqmlmodels.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsqmlmodels_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labssettings.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labssettings_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labssharedimage.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labssharedimage_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labswavefrontmesh.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labswavefrontmesh_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_linguist.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimedia.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimedia_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimediaquick_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimediatestlibprivate_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimediawidgets.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_network.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_network_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_networkauth.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_networkauth_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_opengl.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_opengl_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_openglwidgets.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_openglwidgets_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_packetprotocol_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_png_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_printsupport.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_printsupport_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qdoccatch_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qdoccatchconversions_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qdoccatchgenerators_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qml.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qml_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlassetdownloader.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlassetdownloader_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlcompiler.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlcompiler_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlcore.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlcore_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmldebug_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmldom_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlformat_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlintegration.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlintegration_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmllocalstorage.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmllocalstorage_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlls_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlmeta.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlmeta_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlmodels.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlmodels_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlnetwork.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlnetwork_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmltest.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmltest_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmltoolingsettings_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmltyperegistrar_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlworkerscript.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlworkerscript_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlxmllistmodel.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlxmllistmodel_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3d.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3d_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dassetimport.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dassetimport_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dassetutils.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dassetutils_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3deffects.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3deffects_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dglslparser_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dhelpers.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dhelpers_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dhelpersimpl.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dhelpersimpl_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3diblbaker.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3diblbaker_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dparticleeffects.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dparticleeffects_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dparticles.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dparticles_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3druntimerender.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3druntimerender_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dspatialaudio_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dutils.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dutils_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dxr.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dxr_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basic.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basic_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusion.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusion_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imagine.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imagine_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2impl.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2impl_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2material.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2material_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universal.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universal_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2windowsstyleimpl.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2windowsstyleimpl_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrolstestutilsprivate_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2quickimpl.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2quickimpl_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2utils.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2utils_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickeffects.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickeffects_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicklayouts.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicklayouts_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickparticles_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickshapes_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktemplates2.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktestutilsprivate_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktimeline.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktimeline_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktimelineblendtrees.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktimelineblendtrees_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickvectorimage.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickvectorimage_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickvectorimagegenerator_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickwidgets.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickwidgets_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_shadertools.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_shadertools_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_spatialaudio.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_spatialaudio_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_sql.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_sql_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_svg.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_svg_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_svgwidgets.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_svgwidgets_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_testinternals_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_testlib.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_testlib_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_tools_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_uiplugin.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_uitools.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_uitools_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_widgets.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_widgets_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_xml.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_xml_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_zlib_private.pri F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qt_functions.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qt_config.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/win32-g++/qmake.conf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/spec_post.prf .qmake.stash F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/exclusive_builds.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/toolchain.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/default_pre.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/win32/default_pre.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/resolve_config.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/exclusive_builds_post.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/default_post.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/build_pass.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qml_debug.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/precompile_header.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/warn_on.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/permissions.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qt.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/resources_functions.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/resources.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/moc.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/win32/opengl.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/uic.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qmake_use.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/file_copies.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/win32/windows.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/testcase_targets.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/exceptions.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/yacc.prf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/lex.prf ../../Server.pro F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Widgets.prl F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Gui.prl F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Network.prl F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Sql.prl F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Core.prl F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6EntryPoint.prl    F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/data/dummy.cpp ../../../Common/common.h ../../../Common/errorhandler.h ../../../Common/exception.h ../../../Common/utils.h ../../configmanager.h ../../databasemanager.h ../../filehandler.h ../../logger.h ../../server.h  ../../../Common/errorhandler.cpp ../../../Common/exception.cpp ../../../Common/utils.cpp ../../configmanager.cpp ../../databasemanager.cpp ../../filehandler.cpp ../../logger.cpp ../../main.cpp ../../server.cpp     

clean: compiler_clean 
	-$(DEL_FILE) release/errorhandler.o release/exception.o release/utils.o release/configmanager.o release/databasemanager.o release/filehandler.o release/logger.o release/main.o release/server.o release/moc_errorhandler.o release/moc_configmanager.o release/moc_databasemanager.o release/moc_filehandler.o release/moc_logger.o release/moc_server.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: release/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) release/moc_predefs.h
release/moc_predefs.h: F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -O2 -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o release/moc_predefs.h F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: release/moc_errorhandler.cpp release/moc_configmanager.cpp release/moc_databasemanager.cpp release/moc_filehandler.cpp release/moc_logger.cpp release/moc_server.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) release/moc_errorhandler.cpp release/moc_configmanager.cpp release/moc_databasemanager.cpp release/moc_filehandler.cpp release/moc_logger.cpp release/moc_server.cpp
release/moc_errorhandler.cpp: ../../../Common/errorhandler.h \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/QAbstractSocket \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qabstractsocket.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetwork-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qhostaddress.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QFile \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfile.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfiledevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMap \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutex \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmutex.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtsan_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/QSqlError \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qsqlerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsqlglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsql-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsqlexports.h \
		release/moc_predefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/bin/moc.exe
	'F:\QT\QT6.9.2\6.9.2\mingw_64\bin\moc.exe' $(DEFINES) --include 'D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/release/moc_predefs.h' -IF:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/win32-g++ -I'D:/XINGWANG LIU/Desktop/Cloud/Server' -IF:/QT/QT6.9.2/6.9.2/mingw_64/include -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtWidgets -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtGui -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore -I. -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IF:/QT/QT6.9.2/Tools/mingw1310_64/x86_64-w64-mingw32/include ../../../Common/errorhandler.h -o release/moc_errorhandler.cpp

release/moc_configmanager.cpp: ../../configmanager.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QSettings \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsettings.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutex \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmutex.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtsan_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutexLocker \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal \
		release/moc_predefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/bin/moc.exe
	'F:\QT\QT6.9.2\6.9.2\mingw_64\bin\moc.exe' $(DEFINES) --include 'D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/release/moc_predefs.h' -IF:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/win32-g++ -I'D:/XINGWANG LIU/Desktop/Cloud/Server' -IF:/QT/QT6.9.2/6.9.2/mingw_64/include -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtWidgets -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtGui -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore -I. -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IF:/QT/QT6.9.2/Tools/mingw1310_64/x86_64-w64-mingw32/include ../../configmanager.h -o release/moc_configmanager.cpp

release/moc_databasemanager.cpp: ../../databasemanager.h \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QCryptographicHash \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcryptographichash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDateTime \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDir \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdir.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdirlisting.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfiledevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfile.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfileinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtimezone.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QList \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutex \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmutex.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtsan_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutexLocker \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/QSqlDatabase \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qsqldatabase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsqlglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsql-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsqlexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetaobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/QSqlError \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qsqlerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/QSqlQuery \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qsqlquery.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QVariant \
		release/moc_predefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/bin/moc.exe
	'F:\QT\QT6.9.2\6.9.2\mingw_64\bin\moc.exe' $(DEFINES) --include 'D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/release/moc_predefs.h' -IF:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/win32-g++ -I'D:/XINGWANG LIU/Desktop/Cloud/Server' -IF:/QT/QT6.9.2/6.9.2/mingw_64/include -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtWidgets -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtGui -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore -I. -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IF:/QT/QT6.9.2/Tools/mingw1310_64/x86_64-w64-mingw32/include ../../databasemanager.h -o release/moc_databasemanager.cpp

release/moc_filehandler.cpp: ../../filehandler.h \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QCryptographicHash \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcryptographichash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDateTime \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDir \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdir.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdirlisting.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfiledevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfile.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfileinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtimezone.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QFile \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QFileInfo \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonarray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutex \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmutex.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtsan_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutexLocker \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QObject \
		release/moc_predefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/bin/moc.exe
	'F:\QT\QT6.9.2\6.9.2\mingw_64\bin\moc.exe' $(DEFINES) --include 'D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/release/moc_predefs.h' -IF:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/win32-g++ -I'D:/XINGWANG LIU/Desktop/Cloud/Server' -IF:/QT/QT6.9.2/6.9.2/mingw_64/include -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtWidgets -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtGui -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore -I. -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IF:/QT/QT6.9.2/Tools/mingw1310_64/x86_64-w64-mingw32/include ../../filehandler.h -o release/moc_filehandler.cpp

release/moc_logger.cpp: ../../logger.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDateTime \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDebug \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutex \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmutex.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtsan_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutexLocker \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal \
		release/moc_predefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/bin/moc.exe
	'F:\QT\QT6.9.2\6.9.2\mingw_64\bin\moc.exe' $(DEFINES) --include 'D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/release/moc_predefs.h' -IF:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/win32-g++ -I'D:/XINGWANG LIU/Desktop/Cloud/Server' -IF:/QT/QT6.9.2/6.9.2/mingw_64/include -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtWidgets -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtGui -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore -I. -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IF:/QT/QT6.9.2/Tools/mingw1310_64/x86_64-w64-mingw32/include ../../logger.h -o release/moc_logger.cpp

release/moc_server.cpp: ../../server.h \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QFile \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfile.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfiledevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonarray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMap \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QSet \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/QTcpServer \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtcpserver.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetwork-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qabstractsocket.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qhostaddress.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/QTcpSocket \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtcpsocket.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QTimer \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtimer.h \
		release/moc_predefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/bin/moc.exe
	'F:\QT\QT6.9.2\6.9.2\mingw_64\bin\moc.exe' $(DEFINES) --include 'D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/release/moc_predefs.h' -IF:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/win32-g++ -I'D:/XINGWANG LIU/Desktop/Cloud/Server' -IF:/QT/QT6.9.2/6.9.2/mingw_64/include -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtWidgets -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtGui -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql -IF:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore -I. -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IF:/QT/QT6.9.2/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IF:/QT/QT6.9.2/Tools/mingw1310_64/x86_64-w64-mingw32/include ../../server.h -o release/moc_server.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all:
compiler_uic_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean 



####### Compile

release/errorhandler.o: ../../../Common/errorhandler.cpp ../../../Common/errorhandler.h \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/QAbstractSocket \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qabstractsocket.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetwork-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qhostaddress.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QFile \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfile.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfiledevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMap \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutex \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmutex.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtsan_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/QSqlError \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qsqlerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsqlglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsql-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsqlexports.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/errorhandler.o ../../../Common/errorhandler.cpp

release/exception.o: ../../../Common/exception.cpp ../../../Common/exception.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QException \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexception.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/exception.o ../../../Common/exception.cpp

release/utils.o: ../../../Common/utils.cpp ../../../Common/utils.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDateTime \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QCryptographicHash \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcryptographichash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QStandardPaths \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstandardpaths.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDir \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdir.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdirlisting.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfiledevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfile.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfileinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtimezone.h \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QFile \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QFileInfo \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/QNetworkInterface \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qnetworkinterface.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetwork-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qhostaddress.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qabstractsocket.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRegularExpression \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/QTcpSocket \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtcpsocket.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QTextStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QUrl
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/utils.o ../../../Common/utils.cpp

release/configmanager.o: ../../configmanager.cpp ../../configmanager.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QSettings \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsettings.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutex \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmutex.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtsan_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutexLocker \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QCoreApplication \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreapplication.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnativeinterface.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfuture.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfutureinterface.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qresultstore.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfuture_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qthreadpool.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qthread.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrunnable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexception.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpromise.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDir \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdir.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdirlisting.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfiledevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfile.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfileinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtimezone.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/configmanager.o ../../configmanager.cpp

release/databasemanager.o: ../../databasemanager.cpp ../../databasemanager.h \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QCryptographicHash \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcryptographichash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDateTime \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDir \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdir.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdirlisting.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfiledevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfile.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfileinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtimezone.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QList \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutex \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmutex.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtsan_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutexLocker \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/QSqlDatabase \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qsqldatabase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsqlglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsql-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsqlexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetaobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/QSqlError \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qsqlerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/QSqlQuery \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qsqlquery.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QVariant \
		../../logger.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDebug \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/QSqlRecord \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qsqlrecord.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/databasemanager.o ../../databasemanager.cpp

release/filehandler.o: ../../filehandler.cpp ../../filehandler.h \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QCryptographicHash \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcryptographichash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDateTime \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDir \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdir.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdirlisting.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfiledevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfile.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfileinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtimezone.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QFile \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QFileInfo \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonarray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutex \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmutex.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtsan_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutexLocker \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QStandardPaths \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstandardpaths.h \
		../../../Common/errorhandler.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/QAbstractSocket \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qabstractsocket.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetwork-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qhostaddress.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMap \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/QSqlError \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qsqlerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsqlglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsql-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtSql/qtsqlexports.h \
		../../../Common/exception.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QException \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexception.h \
		../../../Common/utils.h \
		../../logger.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/filehandler.o ../../filehandler.cpp

release/logger.o: ../../logger.cpp ../../logger.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDateTime \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDebug \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutex \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmutex.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtsan_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutexLocker \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/logger.o ../../logger.cpp

release/main.o: ../../main.cpp F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QCoreApplication \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreapplication.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnativeinterface.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfuture.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfutureinterface.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmutex.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtsan_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qresultstore.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfuture_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qthreadpool.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qthread.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrunnable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexception.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpromise.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		../../server.h \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QFile \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfile.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfiledevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonarray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMap \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QSet \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/QTcpServer \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtcpserver.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetwork-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qabstractsocket.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qhostaddress.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/QTcpSocket \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtcpsocket.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QTimer \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtimer.h \
		../../configmanager.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QSettings \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsettings.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutex \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutexLocker \
		../../logger.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDateTime \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/main.o ../../main.cpp

release/server.o: ../../server.cpp ../../server.h \
		../../../Common/common.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QByteArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrefcount.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasicatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qgenericatomic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompilerdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qprocessordetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsystemdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfiginclude.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconfig.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcore-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtconfigmacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversionchecks.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypes.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qassert.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtnoop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qyieldcpu.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnamespace.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtcoreglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtversion.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtypeinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsysinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlogging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qflags.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qconstructormacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qexceptionhandling.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qforeach.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttypetraits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qglobalstatic.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmalloc.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qminmax.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qnumeric.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qoverload.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qswap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtresource.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qttranslation.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qversiontagging.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcompare.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstdlibdetection.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcomparehelpers.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20type_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtmetamacros.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qpair.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydatapointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qarraydataops.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qxptype_traits.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20functional.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q17memory.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearrayview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringfwd.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDataStream \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatastream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevicebase.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QIODevice \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiodevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstring.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qchar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringliteral.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlatin1stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qanystringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qutf8stringview.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringtokenizer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringbuilder.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringconverter_base.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhashfunctions.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbytearraylist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringlist.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qalgorithms.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qstringmatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcoreevent.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbasictimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qeventloop.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qelapsedtimer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetatype.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfloat16.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmath.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtformat_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qiterable.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmetacontainer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontainerinfo.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtaggedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qscopeguard.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qobject_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qbindingstorage.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qspan.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20iterator.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonDocument \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsondocument.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonparseerror.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborvalue.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcborcommon.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdatetime.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcalendar.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qlocale.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvariant.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qdebug.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtextstream.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qcontiguouscache.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmap.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qshareddata_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qset.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qhash.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qvarlengtharray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q23utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/q20utility.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qregularexpression.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qurl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/quuid.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qendian.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonobject.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QRandomGenerator \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qrandom.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QString \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QtGlobal \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QFile \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfile.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qfiledevice.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QJsonArray \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qjsonarray.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMap \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QObject \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QSet \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/QTcpServer \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtcpserver.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetwork-config.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtnetworkexports.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qabstractsocket.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qhostaddress.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/QTcpSocket \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/qtcpsocket.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QTimer \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtimer.h \
		../../configmanager.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QSettings \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qsettings.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutex \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qmutex.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/qtsan_impl.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QMutexLocker \
		../../logger.h \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDateTime \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtCore/QDebug \
		F:/QT/QT6.9.2/6.9.2/mingw_64/include/QtNetwork/QHostAddress
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/server.o ../../server.cpp

release/moc_errorhandler.o: release/moc_errorhandler.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/moc_errorhandler.o release/moc_errorhandler.cpp

release/moc_configmanager.o: release/moc_configmanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/moc_configmanager.o release/moc_configmanager.cpp

release/moc_databasemanager.o: release/moc_databasemanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/moc_databasemanager.o release/moc_databasemanager.cpp

release/moc_filehandler.o: release/moc_filehandler.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/moc_filehandler.o release/moc_filehandler.cpp

release/moc_logger.o: release/moc_logger.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/moc_logger.o release/moc_logger.cpp

release/moc_server.o: release/moc_server.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release/moc_server.o release/moc_server.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

.SUFFIXES:

