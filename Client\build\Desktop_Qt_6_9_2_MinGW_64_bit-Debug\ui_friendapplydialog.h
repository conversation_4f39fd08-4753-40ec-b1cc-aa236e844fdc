/********************************************************************************
** Form generated from reading UI file 'friendapplydialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FRIENDAPPLYDIALOG_H
#define UI_FRIENDAPPLYDIALOG_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_FriendApplyDialog
{
public:
    QVBoxLayout *verticalLayout;
    QTableWidget *tableWidget_applies;
    QHBoxLayout *horizontalLayout_buttons;
    QSpacerItem *horizontalSpacer;
    QPushButton *pushButton_agree;
    QPushButton *pushButton_reject;
    QPushButton *pushButton_close;

    void setupUi(QDialog *FriendApplyDialog)
    {
        if (FriendApplyDialog->objectName().isEmpty())
            FriendApplyDialog->setObjectName("FriendApplyDialog");
        FriendApplyDialog->resize(500, 400);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/friend_apply.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        FriendApplyDialog->setWindowIcon(icon);
        verticalLayout = new QVBoxLayout(FriendApplyDialog);
        verticalLayout->setObjectName("verticalLayout");
        tableWidget_applies = new QTableWidget(FriendApplyDialog);
        if (tableWidget_applies->columnCount() < 4)
            tableWidget_applies->setColumnCount(4);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        tableWidget_applies->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        tableWidget_applies->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        tableWidget_applies->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        tableWidget_applies->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        tableWidget_applies->setObjectName("tableWidget_applies");
        tableWidget_applies->setSelectionMode(QAbstractItemView::SingleSelection);
        tableWidget_applies->setSelectionBehavior(QAbstractItemView::SelectRows);

        verticalLayout->addWidget(tableWidget_applies);

        horizontalLayout_buttons = new QHBoxLayout();
        horizontalLayout_buttons->setObjectName("horizontalLayout_buttons");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer);

        pushButton_agree = new QPushButton(FriendApplyDialog);
        pushButton_agree->setObjectName("pushButton_agree");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/agree.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_agree->setIcon(icon1);

        horizontalLayout_buttons->addWidget(pushButton_agree);

        pushButton_reject = new QPushButton(FriendApplyDialog);
        pushButton_reject->setObjectName("pushButton_reject");
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/reject.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_reject->setIcon(icon2);

        horizontalLayout_buttons->addWidget(pushButton_reject);

        pushButton_close = new QPushButton(FriendApplyDialog);
        pushButton_close->setObjectName("pushButton_close");
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/icons/close.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_close->setIcon(icon3);

        horizontalLayout_buttons->addWidget(pushButton_close);


        verticalLayout->addLayout(horizontalLayout_buttons);


        retranslateUi(FriendApplyDialog);

        QMetaObject::connectSlotsByName(FriendApplyDialog);
    } // setupUi

    void retranslateUi(QDialog *FriendApplyDialog)
    {
        FriendApplyDialog->setWindowTitle(QCoreApplication::translate("FriendApplyDialog", "\345\245\275\345\217\213\347\224\263\350\257\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem = tableWidget_applies->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("FriendApplyDialog", "\347\224\263\350\257\267\344\272\272", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget_applies->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("FriendApplyDialog", "\347\224\263\350\257\267\346\227\266\351\227\264", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = tableWidget_applies->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QCoreApplication::translate("FriendApplyDialog", "\347\224\263\350\257\267\344\277\241\346\201\257", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = tableWidget_applies->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QCoreApplication::translate("FriendApplyDialog", "\346\223\215\344\275\234", nullptr));
        pushButton_agree->setText(QCoreApplication::translate("FriendApplyDialog", "\345\220\214\346\204\217", nullptr));
        pushButton_reject->setText(QCoreApplication::translate("FriendApplyDialog", "\346\213\222\347\273\235", nullptr));
        pushButton_close->setText(QCoreApplication::translate("FriendApplyDialog", "\345\205\263\351\227\255", nullptr));
    } // retranslateUi

};

namespace Ui {
    class FriendApplyDialog: public Ui_FriendApplyDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FRIENDAPPLYDIALOG_H
