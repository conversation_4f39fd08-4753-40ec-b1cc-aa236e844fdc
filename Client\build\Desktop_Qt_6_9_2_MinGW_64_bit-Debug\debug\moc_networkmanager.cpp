/****************************************************************************
** Meta object code from reading C++ file 'networkmanager.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../networkmanager.h"
#include <QtCore/qmetatype.h>
#include <QtCore/QList>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'networkmanager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN14NetworkManagerE_t {};
} // unnamed namespace

template <> constexpr inline auto NetworkManager::qt_create_metaobjectdata<qt_meta_tag_ZN14NetworkManagerE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "NetworkManager",
        "connectionStateChanged",
        "",
        "connected",
        "registerResponse",
        "success",
        "message",
        "loginResponse",
        "UserInfo",
        "userInfo",
        "logoutResponse",
        "fileListResponse",
        "QList<FileInfo>",
        "fileList",
        "fileTransferProgress",
        "fileId",
        "bytesSent",
        "bytesTotal",
        "fileTransferStatus",
        "status",
        "uploadFileResponse",
        "offset",
        "downloadFileResponse",
        "fileName",
        "fileSize",
        "fileDataReceived",
        "msgId",
        "data",
        "createDirectoryResponse",
        "deleteFileResponse",
        "renameFileResponse",
        "moveFileResponse",
        "copyFileResponse",
        "newFileName",
        "shareFileResponse",
        "shareCode",
        "searchFileResponse",
        "getFileByShareCodeResponse",
        "FileInfo",
        "fileInfo",
        "searchUserResponse",
        "QList<UserInfo>",
        "userList",
        "addFriendResponse",
        "addFriendNotify",
        "agreeAddFriendResponse",
        "rejectAddFriendResponse",
        "deleteFriendResponse",
        "friendListResponse",
        "QList<FriendInfo>",
        "friendList",
        "friendStatusNotify",
        "friendId",
        "online",
        "messageReceived",
        "MessageInfo",
        "changePasswordResponse",
        "updateUserInfoResponse",
        "fileDataSent",
        "taskId",
        "messageHistoryResponse",
        "QList<MessageInfo>",
        "messageList",
        "offlineMessageResponse",
        "errorOccurred",
        "error",
        "onConnected",
        "onDisconnected",
        "onReadyRead",
        "onError",
        "QAbstractSocket::SocketError",
        "socketError",
        "onHeartbeatTimeout",
        "onChunkReadyToSend",
        "onRequestNextChunk",
        "size",
        "onTaskRetried",
        "retryRequest",
        "requestData",
        "MessageType",
        "msgType",
        "handleFileData",
        "handleCompleteFileUpload",
        "jsonObj",
        "handleDownloadFileResponse"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'connectionStateChanged'
        QtMocHelpers::SignalData<void(bool)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 },
        }}),
        // Signal 'registerResponse'
        QtMocHelpers::SignalData<void(bool, const QString &)>(4, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'loginResponse'
        QtMocHelpers::SignalData<void(bool, const QString &, const UserInfo &)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 }, { 0x80000000 | 8, 9 },
        }}),
        // Signal 'logoutResponse'
        QtMocHelpers::SignalData<void(bool, const QString &)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'fileListResponse'
        QtMocHelpers::SignalData<void(bool, const QString &, const QList<FileInfo> &)>(11, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 }, { 0x80000000 | 12, 13 },
        }}),
        // Signal 'fileTransferProgress'
        QtMocHelpers::SignalData<void(quint32, qint64, qint64)>(14, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 15 }, { QMetaType::LongLong, 16 }, { QMetaType::LongLong, 17 },
        }}),
        // Signal 'fileTransferStatus'
        QtMocHelpers::SignalData<void(quint32, const QString &)>(18, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 15 }, { QMetaType::QString, 19 },
        }}),
        // Signal 'uploadFileResponse'
        QtMocHelpers::SignalData<void(bool, const QString &, quint32, quint64)>(20, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 }, { QMetaType::UInt, 15 }, { QMetaType::ULongLong, 21 },
        }}),
        // Signal 'downloadFileResponse'
        QtMocHelpers::SignalData<void(bool, const QString &, quint32, const QString &, quint64)>(22, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 }, { QMetaType::UInt, 15 }, { QMetaType::QString, 23 },
            { QMetaType::ULongLong, 24 },
        }}),
        // Signal 'fileDataReceived'
        QtMocHelpers::SignalData<void(quint32, const QByteArray &)>(25, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 26 }, { QMetaType::QByteArray, 27 },
        }}),
        // Signal 'createDirectoryResponse'
        QtMocHelpers::SignalData<void(bool, const QString &)>(28, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'deleteFileResponse'
        QtMocHelpers::SignalData<void(bool, const QString &)>(29, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'renameFileResponse'
        QtMocHelpers::SignalData<void(bool, const QString &)>(30, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'moveFileResponse'
        QtMocHelpers::SignalData<void(bool, const QString &)>(31, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'copyFileResponse'
        QtMocHelpers::SignalData<void(bool, const QString &, const QString &)>(32, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 }, { QMetaType::QString, 33 },
        }}),
        // Signal 'shareFileResponse'
        QtMocHelpers::SignalData<void(bool, const QString &, const QString &)>(34, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 }, { QMetaType::QString, 35 },
        }}),
        // Signal 'searchFileResponse'
        QtMocHelpers::SignalData<void(bool, const QString &, const QList<FileInfo> &)>(36, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 }, { 0x80000000 | 12, 13 },
        }}),
        // Signal 'getFileByShareCodeResponse'
        QtMocHelpers::SignalData<void(bool, const QString &, const FileInfo &)>(37, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 }, { 0x80000000 | 38, 39 },
        }}),
        // Signal 'searchUserResponse'
        QtMocHelpers::SignalData<void(bool, const QString &, const QList<UserInfo> &)>(40, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 }, { 0x80000000 | 41, 42 },
        }}),
        // Signal 'addFriendResponse'
        QtMocHelpers::SignalData<void(bool, const QString &)>(43, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'addFriendNotify'
        QtMocHelpers::SignalData<void(const UserInfo &)>(44, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 8, 9 },
        }}),
        // Signal 'agreeAddFriendResponse'
        QtMocHelpers::SignalData<void(bool, const QString &)>(45, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'rejectAddFriendResponse'
        QtMocHelpers::SignalData<void(bool, const QString &)>(46, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'deleteFriendResponse'
        QtMocHelpers::SignalData<void(bool, const QString &)>(47, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'friendListResponse'
        QtMocHelpers::SignalData<void(bool, const QString &, const QList<FriendInfo> &)>(48, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 }, { 0x80000000 | 49, 50 },
        }}),
        // Signal 'friendStatusNotify'
        QtMocHelpers::SignalData<void(quint32, bool)>(51, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 52 }, { QMetaType::Bool, 53 },
        }}),
        // Signal 'messageReceived'
        QtMocHelpers::SignalData<void(const MessageInfo &)>(54, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 55, 6 },
        }}),
        // Signal 'changePasswordResponse'
        QtMocHelpers::SignalData<void(bool, const QString &)>(56, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'updateUserInfoResponse'
        QtMocHelpers::SignalData<void(bool, const QString &)>(57, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'fileDataSent'
        QtMocHelpers::SignalData<void(quint32, bool)>(58, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 59 }, { QMetaType::Bool, 5 },
        }}),
        // Signal 'messageHistoryResponse'
        QtMocHelpers::SignalData<void(bool, const QString &, const QList<MessageInfo> &)>(60, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 }, { 0x80000000 | 61, 62 },
        }}),
        // Signal 'offlineMessageResponse'
        QtMocHelpers::SignalData<void(bool, const QString &, const QList<MessageInfo> &)>(63, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 }, { QMetaType::QString, 6 }, { 0x80000000 | 61, 62 },
        }}),
        // Signal 'errorOccurred'
        QtMocHelpers::SignalData<void(const QString &)>(64, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 65 },
        }}),
        // Slot 'onConnected'
        QtMocHelpers::SlotData<void()>(66, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onDisconnected'
        QtMocHelpers::SlotData<void()>(67, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onReadyRead'
        QtMocHelpers::SlotData<void()>(68, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onError'
        QtMocHelpers::SlotData<void(QAbstractSocket::SocketError)>(69, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 70, 71 },
        }}),
        // Slot 'onHeartbeatTimeout'
        QtMocHelpers::SlotData<void()>(72, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onChunkReadyToSend'
        QtMocHelpers::SlotData<void(quint32, quint64, const QByteArray &)>(73, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::UInt, 59 }, { QMetaType::ULongLong, 21 }, { QMetaType::QByteArray, 27 },
        }}),
        // Slot 'onRequestNextChunk'
        QtMocHelpers::SlotData<void(quint32, quint64, quint32)>(74, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::UInt, 59 }, { QMetaType::ULongLong, 21 }, { QMetaType::UInt, 75 },
        }}),
        // Slot 'onTaskRetried'
        QtMocHelpers::SlotData<void(quint32)>(76, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::UInt, 59 },
        }}),
        // Slot 'retryRequest'
        QtMocHelpers::SlotData<void(quint32, const QJsonObject &, MessageType)>(77, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::UInt, 26 }, { QMetaType::QJsonObject, 78 }, { 0x80000000 | 79, 80 },
        }}),
        // Slot 'handleFileData'
        QtMocHelpers::SlotData<void(const QByteArray &)>(81, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QByteArray, 27 },
        }}),
        // Slot 'handleCompleteFileUpload'
        QtMocHelpers::SlotData<void(const QJsonObject &)>(82, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QJsonObject, 83 },
        }}),
        // Slot 'handleDownloadFileResponse'
        QtMocHelpers::SlotData<void(const QJsonObject &)>(84, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QJsonObject, 83 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<NetworkManager, qt_meta_tag_ZN14NetworkManagerE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject NetworkManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14NetworkManagerE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14NetworkManagerE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN14NetworkManagerE_t>.metaTypes,
    nullptr
} };

void NetworkManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<NetworkManager *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->connectionStateChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 1: _t->registerResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 2: _t->loginResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<UserInfo>>(_a[3]))); break;
        case 3: _t->logoutResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 4: _t->fileListResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QList<FileInfo>>>(_a[3]))); break;
        case 5: _t->fileTransferProgress((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[3]))); break;
        case 6: _t->fileTransferStatus((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 7: _t->uploadFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<quint32>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<quint64>>(_a[4]))); break;
        case 8: _t->downloadFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<quint32>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[4])),(*reinterpret_cast< std::add_pointer_t<quint64>>(_a[5]))); break;
        case 9: _t->fileDataReceived((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QByteArray>>(_a[2]))); break;
        case 10: _t->createDirectoryResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 11: _t->deleteFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 12: _t->renameFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 13: _t->moveFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 14: _t->copyFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 15: _t->shareFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 16: _t->searchFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QList<FileInfo>>>(_a[3]))); break;
        case 17: _t->getFileByShareCodeResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<FileInfo>>(_a[3]))); break;
        case 18: _t->searchUserResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QList<UserInfo>>>(_a[3]))); break;
        case 19: _t->addFriendResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 20: _t->addFriendNotify((*reinterpret_cast< std::add_pointer_t<UserInfo>>(_a[1]))); break;
        case 21: _t->agreeAddFriendResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 22: _t->rejectAddFriendResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 23: _t->deleteFriendResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 24: _t->friendListResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QList<FriendInfo>>>(_a[3]))); break;
        case 25: _t->friendStatusNotify((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 26: _t->messageReceived((*reinterpret_cast< std::add_pointer_t<MessageInfo>>(_a[1]))); break;
        case 27: _t->changePasswordResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 28: _t->updateUserInfoResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 29: _t->fileDataSent((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 30: _t->messageHistoryResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QList<MessageInfo>>>(_a[3]))); break;
        case 31: _t->offlineMessageResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QList<MessageInfo>>>(_a[3]))); break;
        case 32: _t->errorOccurred((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 33: _t->onConnected(); break;
        case 34: _t->onDisconnected(); break;
        case 35: _t->onReadyRead(); break;
        case 36: _t->onError((*reinterpret_cast< std::add_pointer_t<QAbstractSocket::SocketError>>(_a[1]))); break;
        case 37: _t->onHeartbeatTimeout(); break;
        case 38: _t->onChunkReadyToSend((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<quint64>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QByteArray>>(_a[3]))); break;
        case 39: _t->onRequestNextChunk((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<quint64>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<quint32>>(_a[3]))); break;
        case 40: _t->onTaskRetried((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1]))); break;
        case 41: _t->retryRequest((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<MessageType>>(_a[3]))); break;
        case 42: _t->handleFileData((*reinterpret_cast< std::add_pointer_t<QByteArray>>(_a[1]))); break;
        case 43: _t->handleCompleteFileUpload((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 44: _t->handleDownloadFileResponse((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 36:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QAbstractSocket::SocketError >(); break;
            }
            break;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool )>(_a, &NetworkManager::connectionStateChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & )>(_a, &NetworkManager::registerResponse, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & , const UserInfo & )>(_a, &NetworkManager::loginResponse, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & )>(_a, &NetworkManager::logoutResponse, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & , const QList<FileInfo> & )>(_a, &NetworkManager::fileListResponse, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(quint32 , qint64 , qint64 )>(_a, &NetworkManager::fileTransferProgress, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(quint32 , const QString & )>(_a, &NetworkManager::fileTransferStatus, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & , quint32 , quint64 )>(_a, &NetworkManager::uploadFileResponse, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & , quint32 , const QString & , quint64 )>(_a, &NetworkManager::downloadFileResponse, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(quint32 , const QByteArray & )>(_a, &NetworkManager::fileDataReceived, 9))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & )>(_a, &NetworkManager::createDirectoryResponse, 10))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & )>(_a, &NetworkManager::deleteFileResponse, 11))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & )>(_a, &NetworkManager::renameFileResponse, 12))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & )>(_a, &NetworkManager::moveFileResponse, 13))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & , const QString & )>(_a, &NetworkManager::copyFileResponse, 14))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & , const QString & )>(_a, &NetworkManager::shareFileResponse, 15))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & , const QList<FileInfo> & )>(_a, &NetworkManager::searchFileResponse, 16))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & , const FileInfo & )>(_a, &NetworkManager::getFileByShareCodeResponse, 17))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & , const QList<UserInfo> & )>(_a, &NetworkManager::searchUserResponse, 18))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & )>(_a, &NetworkManager::addFriendResponse, 19))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(const UserInfo & )>(_a, &NetworkManager::addFriendNotify, 20))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & )>(_a, &NetworkManager::agreeAddFriendResponse, 21))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & )>(_a, &NetworkManager::rejectAddFriendResponse, 22))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & )>(_a, &NetworkManager::deleteFriendResponse, 23))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & , const QList<FriendInfo> & )>(_a, &NetworkManager::friendListResponse, 24))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(quint32 , bool )>(_a, &NetworkManager::friendStatusNotify, 25))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(const MessageInfo & )>(_a, &NetworkManager::messageReceived, 26))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & )>(_a, &NetworkManager::changePasswordResponse, 27))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & )>(_a, &NetworkManager::updateUserInfoResponse, 28))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(quint32 , bool )>(_a, &NetworkManager::fileDataSent, 29))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & , const QList<MessageInfo> & )>(_a, &NetworkManager::messageHistoryResponse, 30))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(bool , const QString & , const QList<MessageInfo> & )>(_a, &NetworkManager::offlineMessageResponse, 31))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkManager::*)(const QString & )>(_a, &NetworkManager::errorOccurred, 32))
            return;
    }
}

const QMetaObject *NetworkManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *NetworkManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14NetworkManagerE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int NetworkManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 45)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 45;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 45)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 45;
    }
    return _id;
}

// SIGNAL 0
void NetworkManager::connectionStateChanged(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void NetworkManager::registerResponse(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1, _t2);
}

// SIGNAL 2
void NetworkManager::loginResponse(bool _t1, const QString & _t2, const UserInfo & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1, _t2, _t3);
}

// SIGNAL 3
void NetworkManager::logoutResponse(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1, _t2);
}

// SIGNAL 4
void NetworkManager::fileListResponse(bool _t1, const QString & _t2, const QList<FileInfo> & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1, _t2, _t3);
}

// SIGNAL 5
void NetworkManager::fileTransferProgress(quint32 _t1, qint64 _t2, qint64 _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1, _t2, _t3);
}

// SIGNAL 6
void NetworkManager::fileTransferStatus(quint32 _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1, _t2);
}

// SIGNAL 7
void NetworkManager::uploadFileResponse(bool _t1, const QString & _t2, quint32 _t3, quint64 _t4)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 7, nullptr, _t1, _t2, _t3, _t4);
}

// SIGNAL 8
void NetworkManager::downloadFileResponse(bool _t1, const QString & _t2, quint32 _t3, const QString & _t4, quint64 _t5)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 8, nullptr, _t1, _t2, _t3, _t4, _t5);
}

// SIGNAL 9
void NetworkManager::fileDataReceived(quint32 _t1, const QByteArray & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 9, nullptr, _t1, _t2);
}

// SIGNAL 10
void NetworkManager::createDirectoryResponse(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 10, nullptr, _t1, _t2);
}

// SIGNAL 11
void NetworkManager::deleteFileResponse(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 11, nullptr, _t1, _t2);
}

// SIGNAL 12
void NetworkManager::renameFileResponse(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 12, nullptr, _t1, _t2);
}

// SIGNAL 13
void NetworkManager::moveFileResponse(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 13, nullptr, _t1, _t2);
}

// SIGNAL 14
void NetworkManager::copyFileResponse(bool _t1, const QString & _t2, const QString & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 14, nullptr, _t1, _t2, _t3);
}

// SIGNAL 15
void NetworkManager::shareFileResponse(bool _t1, const QString & _t2, const QString & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 15, nullptr, _t1, _t2, _t3);
}

// SIGNAL 16
void NetworkManager::searchFileResponse(bool _t1, const QString & _t2, const QList<FileInfo> & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 16, nullptr, _t1, _t2, _t3);
}

// SIGNAL 17
void NetworkManager::getFileByShareCodeResponse(bool _t1, const QString & _t2, const FileInfo & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 17, nullptr, _t1, _t2, _t3);
}

// SIGNAL 18
void NetworkManager::searchUserResponse(bool _t1, const QString & _t2, const QList<UserInfo> & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 18, nullptr, _t1, _t2, _t3);
}

// SIGNAL 19
void NetworkManager::addFriendResponse(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 19, nullptr, _t1, _t2);
}

// SIGNAL 20
void NetworkManager::addFriendNotify(const UserInfo & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 20, nullptr, _t1);
}

// SIGNAL 21
void NetworkManager::agreeAddFriendResponse(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 21, nullptr, _t1, _t2);
}

// SIGNAL 22
void NetworkManager::rejectAddFriendResponse(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 22, nullptr, _t1, _t2);
}

// SIGNAL 23
void NetworkManager::deleteFriendResponse(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 23, nullptr, _t1, _t2);
}

// SIGNAL 24
void NetworkManager::friendListResponse(bool _t1, const QString & _t2, const QList<FriendInfo> & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 24, nullptr, _t1, _t2, _t3);
}

// SIGNAL 25
void NetworkManager::friendStatusNotify(quint32 _t1, bool _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 25, nullptr, _t1, _t2);
}

// SIGNAL 26
void NetworkManager::messageReceived(const MessageInfo & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 26, nullptr, _t1);
}

// SIGNAL 27
void NetworkManager::changePasswordResponse(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 27, nullptr, _t1, _t2);
}

// SIGNAL 28
void NetworkManager::updateUserInfoResponse(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 28, nullptr, _t1, _t2);
}

// SIGNAL 29
void NetworkManager::fileDataSent(quint32 _t1, bool _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 29, nullptr, _t1, _t2);
}

// SIGNAL 30
void NetworkManager::messageHistoryResponse(bool _t1, const QString & _t2, const QList<MessageInfo> & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 30, nullptr, _t1, _t2, _t3);
}

// SIGNAL 31
void NetworkManager::offlineMessageResponse(bool _t1, const QString & _t2, const QList<MessageInfo> & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 31, nullptr, _t1, _t2, _t3);
}

// SIGNAL 32
void NetworkManager::errorOccurred(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 32, nullptr, _t1);
}
QT_WARNING_POP
