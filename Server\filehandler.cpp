#include "filehandler.h"
#include <QDataStream>
#include <QStandardPaths>
#include "../Common/errorhandler.h"
#include "../Common/exception.h"
#include "../Common/utils.h"
#include "logger.h"

// 初始化静态成员变量
FileHandler *FileHandler::m_instance = nullptr;
QMutex FileHandler::m_mutex;

// 获取单例实例
FileHandler *FileHandler::getInstance()
{
    if (m_instance == nullptr) {
        QMutexLocker locker(&m_mutex);
        if (m_instance == nullptr) {
            m_instance = new FileHandler();
        }
    }
    return m_instance;
}

// 私有构造函数
FileHandler::FileHandler(QObject *parent)
    : QObject(parent)
{}

// 私有析构函数
FileHandler::~FileHandler() {}

// 初始化文件处理器
bool FileHandler::initialize(const QString &fileDir)
{
    // 保存文件目录
    m_fileDir = fileDir;

    // 创建文件目录结构
    if (!createFileDirectoryStructure()) {
        LOG_ERROR("FileHandler", "创建文件目录结构失败");
        return false;
    }

    LOG_INFO("FileHandler",
             QString("文件处理器初始化成功，目录: %1").arg(fileDir));
    return true;
}

// 保存文件
bool FileHandler::saveFile(quint32 fileId, const QByteArray &data,
                           quint64 offset)
{
    try {
        // 生成文件路径
        QString filePath = generateFilePath(fileId);

        // 确保文件目录存在
        QString dirPath = QFileInfo(filePath).absolutePath();
        if (!QDir().exists(dirPath)) {
            if (!QDir().mkpath(dirPath)) {
                throw FileException("无法创建文件目录: " + dirPath);
            }
        }

        // 打开文件
        QFile file(filePath);
        QIODevice::OpenMode mode = QIODevice::WriteOnly;
        if (offset > 0) {
            mode |= QIODevice::Append;
        }

        if (!file.open(mode)) {
            throw FileException("无法打开文件: " + file.errorString());
        }

        // 如果是断点续传，设置文件位置
        if (offset > 0) {
            if (!file.seek(offset)) {
                file.close();
                throw FileException("无法设置文件位置");
            }
        }

        // 写入数据
        qint64 bytesWritten = file.write(data);
        if (bytesWritten == -1) {
            file.close();
            throw FileException("写入文件失败: " + file.errorString());
        }

        // 关闭文件
        file.close();

        // 添加到映射
        m_filePathMap[fileId] = filePath;

        LOG_DEBUG("FileHandler",
                  QString("保存文件成功，文件ID: %1，大小: %2 字节，偏移量: %3")
                      .arg(fileId)
                      .arg(data.size())
                      .arg(offset));

        return true;

    } catch (const Cloud7Exception &e) {
        LOG_ERROR("FileHandler", QString("保存文件失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.message()));
        return false;
    } catch (const std::exception &e) {
        LOG_ERROR("FileHandler", QString("保存文件失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.what()));
        return false;
    }
}

// 读取文件
QByteArray FileHandler::readFile(quint32 fileId, quint64 offset, qint64 size)
{
    try {
        // 获取文件路径
        QString filePath = getFilePath(fileId);

        // 检查文件是否存在
        if (!QFile::exists(filePath)) {
            throw FileException("文件不存在: " + filePath);
        }

        // 打开文件
        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly)) {
            throw FileException("无法打开文件: " + file.errorString());
        }

        // 设置文件位置
        if (offset > 0) {
            if (!file.seek(offset)) {
                file.close();
                throw FileException("无法设置文件位置");
            }
        }

        // 读取数据
        QByteArray data;
        if (size < 0) {
            // 读取全部数据
            data = file.readAll();
        } else {
            // 读取指定大小的数据
            data = file.read(size);
        }

        // 关闭文件
        file.close();

        LOG_DEBUG("FileHandler",
                  QString("读取文件成功，文件ID: %1，大小: %2 字节，偏移量: %3")
                      .arg(fileId)
                      .arg(data.size())
                      .arg(offset));

        return data;

    } catch (const Cloud7Exception &e) {
        LOG_ERROR("FileHandler", QString("读取文件失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.message()));
        return QByteArray();
    } catch (const std::exception &e) {
        LOG_ERROR("FileHandler", QString("读取文件失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.what()));
        return QByteArray();
    }
}

// 删除文件
bool FileHandler::deleteFile(quint32 fileId)
{
    try {
        // 获取文件路径
        QString filePath = getFilePath(fileId);

        // 检查文件是否存在
        if (!QFile::exists(filePath)) {
            throw FileException("文件不存在: " + filePath);
        }

        // 删除文件
        QFile file(filePath);
        if (!file.remove()) {
            throw FileException("删除文件失败: " + file.errorString());
        }

        // 从映射中移除
        m_filePathMap.remove(fileId);

        LOG_INFO("FileHandler", QString("删除文件成功，文件ID: %1").arg(fileId));

        return true;

    } catch (const Cloud7Exception &e) {
        LOG_ERROR("FileHandler", QString("删除文件失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.message()));
        return false;
    } catch (const std::exception &e) {
        LOG_ERROR("FileHandler", QString("删除文件失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.what()));
        return false;
    }
}

// 移动文件
bool FileHandler::moveFile(quint32 fileId, quint32 newParentId)
{
    try {
        // 获取源文件路径
        QString srcPath = getFilePath(fileId);

        // 检查源文件是否存在
        if (!QFile::exists(srcPath)) {
            throw FileException("文件不存在: " + srcPath);
        }

        // 生成新文件路径
        QString newPath = generateFilePath(fileId);

        // 确保目标目录存在
        QString dirPath = QFileInfo(newPath).absolutePath();
        if (!QDir().exists(dirPath)) {
            if (!QDir().mkpath(dirPath)) {
                throw FileException("无法创建文件目录: " + dirPath);
            }
        }

        // 移动文件
        QFile file(srcPath);
        if (!file.rename(newPath)) {
            throw FileException("移动文件失败: " + file.errorString());
        }

        // 更新映射
        m_filePathMap[fileId] = newPath;

        LOG_INFO("FileHandler", QString("移动文件成功，文件ID: %1，新路径: %2")
                                    .arg(fileId)
                                    .arg(newPath));

        return true;

    } catch (const Cloud7Exception &e) {
        LOG_ERROR("FileHandler", QString("移动文件失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.message()));
        return false;
    } catch (const std::exception &e) {
        LOG_ERROR("FileHandler", QString("移动文件失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.what()));
        return false;
    }
}

// 复制文件
bool FileHandler::copyFile(quint32 fileId, quint32 newParentId)
{
    try {
        // 获取源文件路径
        QString srcPath = getFilePath(fileId);

        // 检查源文件是否存在
        if (!QFile::exists(srcPath)) {
            throw FileException("文件不存在: " + srcPath);
        }

        // 生成新文件ID（需要从数据库获取，这里使用临时方案）
        quint32 newFileId = generateNewFileId();

        // 生成新文件路径
        QString newPath = generateFilePath(newFileId);

        // 确保目标目录存在
        QString dirPath = QFileInfo(newPath).absolutePath();
        if (!QDir().exists(dirPath)) {
            if (!QDir().mkpath(dirPath)) {
                throw FileException("无法创建文件目录: " + dirPath);
            }
        }

        // 复制文件
        QFile srcFile(srcPath);
        QFile dstFile(newPath);

        if (!srcFile.open(QIODevice::ReadOnly)) {
            throw FileException("无法打开源文件: " + srcFile.errorString());
        }

        if (!dstFile.open(QIODevice::WriteOnly)) {
            srcFile.close();
            throw FileException("无法创建目标文件: " + dstFile.errorString());
        }

        // 复制数据
        QByteArray buffer = srcFile.readAll();
        qint64 bytesWritten = dstFile.write(buffer);
        if (bytesWritten == -1) {
            srcFile.close();
            dstFile.close();
            throw FileException("写入目标文件失败: " + dstFile.errorString());
        }

        // 关闭文件
        srcFile.close();
        dstFile.close();

        // 添加到映射
        m_filePathMap[newFileId] = newPath;

        LOG_INFO("FileHandler", QString("复制文件成功，源文件ID: %1，新文件ID: %2")
                                    .arg(fileId)
                                    .arg(newFileId));

        return true;

    } catch (const Cloud7Exception &e) {
        LOG_ERROR("FileHandler", QString("复制文件失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.message()));
        return false;
    } catch (const std::exception &e) {
        LOG_ERROR("FileHandler", QString("复制文件失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.what()));
        return false;
    }
}

// 重命名文件
bool FileHandler::renameFile(quint32 fileId, const QString &newName)
{
    try {
        // 获取文件路径
        QString filePath = getFilePath(fileId);

        // 检查文件是否存在
        if (!QFile::exists(filePath)) {
            throw FileException("文件不存在: " + filePath);
        }

        // 获取文件信息
        QFileInfo fileInfo(filePath);

        // 构建新路径
        QString newPath = fileInfo.absolutePath() + "/" + newName;

        // 重命名文件
        QFile file(filePath);
        if (!file.rename(newPath)) {
            throw FileException("重命名文件失败: " + file.errorString());
        }

        // 更新映射
        m_filePathMap[fileId] = newPath;

        LOG_INFO("FileHandler", QString("重命名文件成功，文件ID: %1，新名称: %2")
                                    .arg(fileId)
                                    .arg(newName));

        return true;

    } catch (const Cloud7Exception &e) {
        LOG_ERROR("FileHandler", QString("重命名文件失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.message()));
        return false;
    } catch (const std::exception &e) {
        LOG_ERROR("FileHandler", QString("重命名文件失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.what()));
        return false;
    }
}

// 计算文件哈希
QString FileHandler::calculateFileHash(quint32 fileId)
{
    try {
        // 获取文件路径
        QString filePath = getFilePath(fileId);

        // 检查文件是否存在
        if (!QFile::exists(filePath)) {
            throw FileException("文件不存在: " + filePath);
        }

        // 计算文件哈希
        QString hash = Utils::calculateFileHash(filePath);

        LOG_DEBUG("FileHandler", QString("计算文件哈希成功，文件ID: %1，哈希: %2")
                                     .arg(fileId)
                                     .arg(hash));

        return hash;

    } catch (const Cloud7Exception &e) {
        LOG_ERROR("FileHandler", QString("计算文件哈希失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.message()));
        return QString();
    } catch (const std::exception &e) {
        LOG_ERROR("FileHandler", QString("计算文件哈希失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.what()));
        return QString();
    }
}

// 获取文件大小
quint64 FileHandler::getFileSize(quint32 fileId)
{
    try {
        // 获取文件路径
        QString filePath = getFilePath(fileId);

        // 检查文件是否存在
        if (!QFile::exists(filePath)) {
            throw FileException("文件不存在: " + filePath);
        }

        // 获取文件大小
        QFileInfo fileInfo(filePath);
        quint64 size = fileInfo.size();

        LOG_DEBUG("FileHandler",
                  QString("获取文件大小成功，文件ID: %1，大小: %2 字节")
                      .arg(fileId)
                      .arg(size));

        return size;

    } catch (const Cloud7Exception &e) {
        LOG_ERROR("FileHandler", QString("获取文件大小失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.message()));
        return 0;
    } catch (const std::exception &e) {
        LOG_ERROR("FileHandler", QString("获取文件大小失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.what()));
        return 0;
    }
}

// 检查文件是否存在
bool FileHandler::fileExists(quint32 fileId)
{
    try {
        // 获取文件路径
        QString filePath = getFilePath(fileId);

        // 检查文件是否存在
        bool exists = QFile::exists(filePath);

        LOG_DEBUG("FileHandler", QString("检查文件存在性，文件ID: %1，存在: %2")
                                     .arg(fileId)
                                     .arg(exists));

        return exists;

    } catch (const Cloud7Exception &e) {
        LOG_ERROR("FileHandler", QString("检查文件存在性失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.message()));
        return false;
    } catch (const std::exception &e) {
        LOG_ERROR("FileHandler", QString("检查文件存在性失败，文件ID: %1，错误: %2")
                                     .arg(fileId)
                                     .arg(e.what()));
        return false;
    }
}

// 创建目录
quint32 FileHandler::createDirectory(const QString &dirName, quint32 parentId)
{
    try {
        // 生成目录ID（需要从数据库获取，这里使用临时方案）
        quint32 dirId = generateNewFileId();

        // 生成目录路径
        QString dirPath = generateFilePath(dirId);

        // 确保父目录存在
        QString parentPath = QFileInfo(dirPath).absolutePath();
        if (!QDir().exists(parentPath)) {
            if (!QDir().mkpath(parentPath)) {
                throw FileException("无法创建父目录: " + parentPath);
            }
        }

        // 创建目录
        QDir dir;
        if (!dir.mkdir(dirPath)) {
            throw FileException("创建目录失败: " + dirPath);
        }

        // 添加到映射
        m_filePathMap[dirId] = dirPath;

        LOG_INFO("FileHandler",
                 QString("创建目录成功，目录ID: %1，名称: %2，父目录ID: %3")
                     .arg(dirId)
                     .arg(dirName)
                     .arg(parentId));

        return dirId;

    } catch (const Cloud7Exception &e) {
        LOG_ERROR("FileHandler",
                  QString("创建目录失败，名称: %1，父目录ID: %2，错误: %3")
                      .arg(dirName)
                      .arg(parentId)
                      .arg(e.message()));
        return 0;
    } catch (const std::exception &e) {
        LOG_ERROR("FileHandler",
                  QString("创建目录失败，名称: %1，父目录ID: %2，错误: %3")
                      .arg(dirName)
                      .arg(parentId)
                      .arg(e.what()));
        return 0;
    }
}

// 获取文件路径
QString FileHandler::getFilePath(quint32 fileId)
{
    // 检查映射中是否存在
    if (m_filePathMap.contains(fileId)) {
        return m_filePathMap[fileId];
    }

    // 生成文件路径
    QString filePath = generateFilePath(fileId);

    // 添加到映射
    m_filePathMap[fileId] = filePath;

    return filePath;
}

// 生成文件路径
QString FileHandler::generateFilePath(quint32 fileId)
{
    // 将文件ID转换为十六进制字符串
    QString hexId = QString("%1").arg(fileId, 8, 16, QLatin1Char('0')).toUpper();

    // 构建路径：文件目录/前两位/中间两位/后四位
    QString filePath = m_fileDir + "/" + hexId.left(2) + "/" + hexId.mid(2, 2) + "/" + hexId.right(4);

    return filePath;
}

// 清理未使用的文件
void FileHandler::cleanupUnusedFiles()
{
    // 这里应该从数据库获取所有有效的文件ID
    QList<quint32> validFileIds; // 这里应该从数据库获取

    // 获取所有文件
    QDir dir(m_fileDir);
    QStringList files = dir.entryList(
        QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot, QDir::Name);

    // 遍历所有文件
    for (const QString &file : files) {
        QString filePath = m_fileDir + "/" + file;
        QFileInfo fileInfo(filePath);

        if (fileInfo.isDir()) {
            // 递归处理子目录
            QDir subDir(filePath);
            QStringList subFiles = subDir.entryList(QDir::Files | QDir::NoDotAndDotDot, QDir::Name);

            for (const QString &subFile : subFiles) {
                QString subFilePath = filePath + "/" + subFile;
                QFileInfo subFileInfo(subFilePath);

                if (subFileInfo.isFile()) {
                    // 从文件名中提取文件ID
                    QString fileName = subFileInfo.completeBaseName();
                    bool ok;
                    quint32 fileId = fileName.toUInt(&ok, 16);

                    // 如果文件ID无效或不在有效列表中，则删除文件
                    if (!ok || !validFileIds.contains(fileId)) {
                        QFile::remove(subFilePath);
                        LOG_INFO("FileHandler",
                                 QString("清理未使用的文件: %1").arg(subFilePath));
                    }
                }
            }

            // 如果目录为空，则删除目录
            if (subDir.entryList(QDir::AllEntries | QDir::NoDotAndDotDot).isEmpty()) {
                subDir.rmdir(filePath);
                LOG_INFO("FileHandler", QString("清理空目录: %1").arg(filePath));
            }
        } else if (fileInfo.isFile()) {
            // 从文件名中提取文件ID
            QString fileName = fileInfo.completeBaseName();
            bool ok;
            quint32 fileId = fileName.toUInt(&ok, 16);

            // 如果文件ID无效或不在有效列表中，则删除文件
            if (!ok || !validFileIds.contains(fileId)) {
                QFile::remove(filePath);
                LOG_INFO("FileHandler", QString("清理未使用的文件: %1").arg(filePath));
            }
        }
    }
}

// 创建文件目录结构
bool FileHandler::createFileDirectoryStructure()
{
    // 创建主目录
    QDir dir(m_fileDir);
    if (!dir.exists()) {
        if (!dir.mkpath(m_fileDir)) {
            return false;
        }
    }

    // 创建子目录结构
    for (int i = 0; i < 256; i++) {
        QString firstLevel = QString("%1").arg(i, 2, 16, QLatin1Char('0')).toUpper();
        QString firstLevelPath = m_fileDir + "/" + firstLevel;

        if (!dir.exists(firstLevelPath)) {
            if (!dir.mkpath(firstLevelPath)) {
                return false;
            }
        }

        for (int j = 0; j < 256; j++) {
            QString secondLevel = QString("%1").arg(j, 2, 16, QLatin1Char('0')).toUpper();
            QString secondLevelPath = firstLevelPath + "/" + secondLevel;

            if (!dir.exists(secondLevelPath)) {
                if (!dir.mkpath(secondLevelPath)) {
                    return false;
                }
            }
        }
    }

    return true;
}

// 获取文件相对路径
QString FileHandler::getFileRelativePath(quint32 fileId)
{
    // 将文件ID转换为十六进制字符串
    QString hexId = QString("%1").arg(fileId, 8, 16, QLatin1Char('0')).toUpper();

    // 构建相对路径：前两位/中间两位/后四位
    QString relativePath = hexId.left(2) + "/" + hexId.mid(2, 2) + "/" + hexId.right(4);

    return relativePath;
}

// 生成新文件ID（临时方案，应该从数据库获取）
quint32 FileHandler::generateNewFileId()
{
    // 这是一个临时方案，使用递增ID生成
    // 在实际应用中，应该从数据库的自增字段获取
    static quint32 lastId = 1000; // 起始ID
    return ++lastId;
}
