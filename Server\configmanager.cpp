#include "configmanager.h"
#include <QCoreApplication>
#include <QDir>
#include <QDebug>

// 初始化静态成员变量
ConfigManager* ConfigManager::m_instance = nullptr;
QMutex ConfigManager::m_mutex;

// 获取单例实例
ConfigManager* ConfigManager::getInstance()
{
    if (m_instance == nullptr) {
        QMutexLocker locker(&m_mutex);
        if (m_instance == nullptr) {
            m_instance = new ConfigManager();
        }
    }
    return m_instance;
}

// 私有构造函数
ConfigManager::ConfigManager(QObject *parent) : QObject(parent), m_settings(nullptr)
{
    // 设置配置文件路径
    QString appDir = QCoreApplication::applicationDirPath();
    m_configPath = QDir(appDir).filePath("server.config");

    // 初始化QSettings对象
    m_settings = new QSettings(m_configPath, QSettings::IniFormat, this);

    // 设置默认配置
    setDefaultConfig();
}

// 私有析构函数
ConfigManager::~ConfigManager()
{
    // 保存配置
    saveConfig();

    // 清理资源
    if (m_settings) {
        delete m_settings;
        m_settings = nullptr;
    }
}

// 设置默认配置
void ConfigManager::setDefaultConfig()
{
    // 服务器默认配置
    m_config.host = "0.0.0.0";
    m_config.port = 8888;

    // 数据库默认配置
    QString appDir = QCoreApplication::applicationDirPath();
    m_config.dbPath = QDir(appDir).filePath("cloud7.db");

    // 文件存储默认配置
    m_config.fileDir = QDir(appDir).filePath("files");

    // 连接默认配置
    m_config.maxConnections = 1000;
    m_config.heartbeatInterval = 30;    // 心跳间隔30秒
    m_config.sessionTimeout = 300;      // 会话超时时间300秒(5分钟)
    m_config.defaultStorage = 1073741824; // 默认存储空间1GB
}

// 加载配置
void ConfigManager::loadConfig()
{
    // 从配置文件读取配置
    m_settings->beginGroup("Server");
    m_config.host = m_settings->value("Host", m_config.host).toString();
    m_config.port = m_settings->value("Port", m_config.port).toUInt();
    m_settings->endGroup();

    m_settings->beginGroup("Database");
    m_config.dbPath = m_settings->value("Path", m_config.dbPath).toString();
    m_settings->endGroup();

    m_settings->beginGroup("File");
    m_config.fileDir = m_settings->value("Directory", m_config.fileDir).toString();
    m_settings->endGroup();

    m_settings->beginGroup("Connection");
    m_config.maxConnections = m_settings->value("MaxConnections", m_config.maxConnections).toInt();
    m_config.heartbeatInterval = m_settings->value("HeartbeatInterval", m_config.heartbeatInterval).toInt();
    m_config.sessionTimeout = m_settings->value("SessionTimeout", m_config.sessionTimeout).toInt();
    m_settings->endGroup();

    m_settings->beginGroup("Storage");
    m_config.defaultStorage = m_settings->value("DefaultStorage", m_config.defaultStorage).toULongLong();
    m_settings->endGroup();
}

// 保存配置
void ConfigManager::saveConfig()
{
    // 保存配置到文件
    m_settings->beginGroup("Server");
    m_settings->setValue("Host", m_config.host);
    m_settings->setValue("Port", m_config.port);
    m_settings->endGroup();

    m_settings->beginGroup("Database");
    m_settings->setValue("Path", m_config.dbPath);
    m_settings->endGroup();

    m_settings->beginGroup("File");
    m_settings->setValue("Directory", m_config.fileDir);
    m_settings->endGroup();

    m_settings->beginGroup("Connection");
    m_settings->setValue("MaxConnections", m_config.maxConnections);
    m_settings->setValue("HeartbeatInterval", m_config.heartbeatInterval);
    m_settings->setValue("SessionTimeout", m_config.sessionTimeout);
    m_settings->endGroup();

    m_settings->beginGroup("Storage");
    m_settings->setValue("DefaultStorage", m_config.defaultStorage);
    m_settings->endGroup();

    // 立即写入文件
    m_settings->sync();
}

// 获取服务器配置
ServerConfig ConfigManager::getServerConfig() const
{
    return m_config;
}

// 设置服务器配置
void ConfigManager::setServerConfig(const ServerConfig& config)
{
    m_config = config;
    saveConfig();
}

// 获取服务器主机地址
QString ConfigManager::getHost() const
{
    return m_config.host;
}

// 获取服务器端口
quint16 ConfigManager::getPort() const
{
    return m_config.port;
}

// 获取数据库路径
QString ConfigManager::getDbPath() const
{
    return m_config.dbPath;
}

// 获取文件存储目录
QString ConfigManager::getFileDir() const
{
    return m_config.fileDir;
}

// 获取最大连接数
int ConfigManager::getMaxConnections() const
{
    return m_config.maxConnections;
}

// 获取心跳间隔
int ConfigManager::getHeartbeatInterval() const
{
    return m_config.heartbeatInterval;
}

// 获取会话超时时间
int ConfigManager::getSessionTimeout() const
{
    return m_config.sessionTimeout;
}

// 获取默认存储空间大小
quint64 ConfigManager::getDefaultStorage() const
{
    return m_config.defaultStorage;
}

// 设置服务器主机地址
void ConfigManager::setHost(const QString& host)
{
    m_config.host = host;
    saveConfig();
}

// 设置服务器端口
void ConfigManager::setPort(quint16 port)
{
    m_config.port = port;
    saveConfig();
}

// 设置数据库路径
void ConfigManager::setDbPath(const QString& path)
{
    m_config.dbPath = path;
    saveConfig();
}

// 设置文件存储目录
void ConfigManager::setFileDir(const QString& dir)
{
    m_config.fileDir = dir;
    saveConfig();
}

// 设置最大连接数
void ConfigManager::setMaxConnections(int max)
{
    m_config.maxConnections = max;
    saveConfig();
}

// 设置心跳间隔
void ConfigManager::setHeartbeatInterval(int interval)
{
    m_config.heartbeatInterval = interval;
    saveConfig();
}

// 设置会话超时时间
void ConfigManager::setSessionTimeout(int timeout)
{
    m_config.sessionTimeout = timeout;
    saveConfig();
}

// 设置默认存储空间大小
void ConfigManager::setDefaultStorage(quint64 storage)
{
    m_config.defaultStorage = storage;
    saveConfig();
}
