#ifndef EXCEPTION_H
#define EXCEPTION_H

#include <QString>
#include <QException>
#include "../Common/common.h"

// 基础异常类
class Cloud7Exception : public QException
{
public:
    // 构造函数
    Cloud7Exception(const QString& message, ErrorCode errorCode = ERROR_NONE);

    // 析构函数
    ~Cloud7Exception() override;

    // 克隆异常
    Cloud7Exception* clone() const override;

    // 抛出异常
    void raise() const override;

    // 获取错误消息
    QString message() const;

    // 获取错误码
    ErrorCode errorCode() const;

private:
    QString m_message;    // 错误消息
    ErrorCode m_errorCode; // 错误码
};

// 网络异常类
class NetworkException : public Cloud7Exception
{
public:
    // 构造函数
    NetworkException(const QString& message, ErrorCode errorCode = ERROR_NETWORK);
};

// 数据库异常类
class DatabaseException : public Cloud7Exception
{
public:
    // 构造函数
    DatabaseException(const QString& message, ErrorCode errorCode = ERROR_DATABASE);
};

// 文件异常类
class FileException : public Cloud7Exception
{
public:
    // 构造函数
    FileException(const QString& message, ErrorCode errorCode = ERROR_FILE_NOT_FOUND);
};

// 认证异常类
class AuthException : public Cloud7Exception
{
public:
    // 构造函数
    AuthException(const QString& message, ErrorCode errorCode = ERROR_AUTH);
};

// 权限异常类
class PermissionException : public Cloud7Exception
{
public:
    // 构造函数
    PermissionException(const QString& message, ErrorCode errorCode = ERROR_PERMISSION);
};

// 参数异常类
class ParameterException : public Cloud7Exception
{
public:
    // 构造函数
    ParameterException(const QString& message, ErrorCode errorCode = ERROR_PARAM);
};

#endif // EXCEPTION_H
