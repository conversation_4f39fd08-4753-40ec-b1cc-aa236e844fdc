#include "filesharedialog.h"
#include "ui_filesharedialog.h"
#include "logger.h"
#include "../Common/utils.h"
#include <QMessageBox>
#include <QClipboard>

// 构造函数
FileShareDialog::FileShareDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::FileShareDialog),
    m_fileInfo()
{
    // 初始化UI
    ui->setupUi(this);

    // 初始化UI
    initUI();
}

// 析构函数
FileShareDialog::~FileShareDialog()
{
    delete ui;
}

// 设置文件信息
void FileShareDialog::setFileInfo(const FileInfo& fileInfo)
{
    // 保存文件信息
    m_fileInfo = fileInfo;

    // 更新UI
    ui->label_file_name_value->setText(fileInfo.fileName);
    ui->label_file_size_value->setText(Utils::formatFileSize(fileInfo.fileSize));

    // 生成分享码
    QString shareCode = generateShareCode();
    ui->lineEdit_share_code->setText(shareCode);
}

// 获取文件信息
FileInfo FileShareDialog::getFileInfo() const
{
    return m_fileInfo;
}

// 分享按钮点击槽函数
void FileShareDialog::on_pushButton_share_clicked()
{
    // 获取分享码
    QString shareCode = ui->lineEdit_share_code->text();

    // 验证分享码
    if (!validateShareCode(shareCode)) {
        QMessageBox::warning(this, "分享提示", "分享码无效");
        return;
    }

    // 获取有效期
    QString expireTime;
    QString selectedText = ui->comboBox_expire_time->currentText();

    if (selectedText == "1天") {
        expireTime = QDateTime::currentDateTime().addDays(1).toString("yyyy-MM-dd hh:mm:ss");
    } else if (selectedText == "3天") {
        expireTime = QDateTime::currentDateTime().addDays(3).toString("yyyy-MM-dd hh:mm:ss");
    } else if (selectedText == "7天") {
        expireTime = QDateTime::currentDateTime().addDays(7).toString("yyyy-MM-dd hh:mm:ss");
    } else if (selectedText == "30天") {
        expireTime = QDateTime::currentDateTime().addDays(30).toString("yyyy-MM-dd hh:mm:ss");
    }

    // 发送分享文件请求信号
    emit shareFileRequested(m_fileInfo.fileId, expireTime);

    // 显示成功消息
    QMessageBox::information(this, "分享成功", QString("文件分享成功，分享码：%1").arg(shareCode));
}

// 复制按钮点击槽函数
void FileShareDialog::on_pushButton_copy_clicked()
{
    // 获取分享码
    QString shareCode = ui->lineEdit_share_code->text();

    // 验证分享码
    if (!validateShareCode(shareCode)) {
        QMessageBox::warning(this, "复制提示", "分享码无效");
        return;
    }

    // 复制到剪贴板
    QClipboard* clipboard = QApplication::clipboard();
    clipboard->setText(shareCode);

    // 显示成功消息
    QMessageBox::information(this, "复制成功", "分享码已复制到剪贴板");
}

// 关闭按钮点击槽函数
void FileShareDialog::on_pushButton_close_clicked()
{
    // 关闭对话框
    reject();
}

// 初始化UI
void FileShareDialog::initUI()
{
    // 设置窗口标题
    setWindowTitle("文件分享");

    // 设置窗口图标
    setWindowIcon(QIcon(":/icons/share.png"));

    // 设置窗口大小
    resize(500, 300);

    // 连接信号槽
    connect(ui->pushButton_share, &QPushButton::clicked, this, &FileShareDialog::on_pushButton_share_clicked);
    connect(ui->pushButton_copy, &QPushButton::clicked, this, &FileShareDialog::on_pushButton_copy_clicked);
    connect(ui->pushButton_close, &QPushButton::clicked, this, &FileShareDialog::on_pushButton_close_clicked);
}

// 生成分享码
QString FileShareDialog::generateShareCode() const
{
    // 生成8位随机字符串
    return Utils::generateRandomString(8);
}

// 验证分享码
bool FileShareDialog::validateShareCode(const QString& shareCode) const
{
    // 分享码必须是8位字母数字
    QRegularExpression regex("^[a-zA-Z0-9]{8}$");
    return regex.match(shareCode).hasMatch();
}
