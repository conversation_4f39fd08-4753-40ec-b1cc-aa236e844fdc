#ifndef NETWORKMANAGER_H
#define NETWORKMANAGER_H

#include "../Common/common.h"
#include "filetransfermanager.h"
#include <QByteArray>
#include <QFile>
#include <QHostAddress>
#include <QJsonDocument>
#include <QJsonObject>
#include <QMap>
#include <QMutex>
#include <QMutexLocker>
#include <QObject>
#include <QTcpSocket>
#include <QTimer>

// 文件操作类型枚举
enum FileOperationType {
  UploadOperation,
  DownloadOperation,
  DeleteOperation,
  RenameOperation,
  MoveOperation
};

// 文件操作结构
struct FileOperation {
  FileOperationType type;
  quint32 fileId;
  QString filePath;    // 仅上传操作需要
  quint32 parentId;    // 仅上传操作需要
  QString newName;     // 仅重命名操作需要
  quint32 newParentId; // 仅移动操作需要
  bool completed;
  QString errorMessage;
  quint64 progress = 0; // 进度（字节数）

  bool operator==(const FileOperation &other) const {
    return type == other.type && fileId == other.fileId &&
           filePath == other.filePath && parentId == other.parentId &&
           newName == other.newName && newParentId == other.newParentId &&
           completed == other.completed && errorMessage == other.errorMessage &&
           progress == other.progress;
  }
};

// 网络管理器类（单例模式）
class NetworkManager : public QObject {
  Q_OBJECT

public:
  // 获取单例实例
  static NetworkManager *getInstance();

  // 连接服务器
  bool connectToServer(const QString &host, quint16 port);

  // 断开连接
  void disconnectFromServer();

  // 检查是否已连接
  bool isConnected() const;

  // 发送数据包
  bool sendPacket(const Packet &packet);

  // 发送JSON消息
  bool sendJsonMessage(quint32 msgType, const QJsonObject &jsonObj);

  // 发送文件数据
  bool sendFileData(quint32 msgType, const QByteArray &fileData);

  // 注册请求
  void registerRequest(const QString &username, const QString &password,
                       const QString &email);

  // 登录请求
  void loginRequest(const QString &username, const QString &password);

  // 登出请求
  void logoutRequest();

  // 获取文件列表请求
  void getFileListRequest(quint32 parentId = 0);

  // 上传文件请求
  void uploadFileRequest(const QString &filePath, quint32 parentId);

  // 发送文件数据块
  void sendFileDataChunk(const QByteArray &data, quint32 msgId);

  // 完成文件上传
  void completeFileUpload(quint32 fileId, const QString &fileHash);

  // 下载文件请求
  void downloadFileRequest(quint32 fileId, quint64 offset = 0);

  // 创建目录请求
  void createDirectoryRequest(const QString &dirName, quint32 parentId);

  // 删除文件请求
  void deleteFileRequest(quint32 fileId);

  // 重命名文件请求
  void renameFileRequest(quint32 fileId, const QString &newName);

  // 移动文件请求
  void moveFileRequest(quint32 fileId, quint32 newParentId);

  // 复制文件请求
  void copyFileRequest(quint32 fileId, quint32 newParentId);

  // 分享文件请求
  void shareFileRequest(quint32 fileId, const QString &expireTime = "");

  // 搜索文件请求
  void searchFileRequest(const QString &keyword, quint32 parentId = 0);

  // 通过分享码获取文件请求
  void getFileByShareCodeRequest(const QString &shareCode);

  // 搜索用户请求
  void searchUserRequest(const QString &keyword);

  // 添加好友请求
  void addFriendRequest(quint32 userId);

  // 同意添加好友
  void agreeAddFriend(quint32 userId);

  // 拒绝添加好友
  void rejectAddFriend(quint32 userId);

  // 删除好友请求
  void deleteFriendRequest(quint32 userId);

  // 获取好友列表请求
  void getFriendListRequest();

  // 发送消息
  void sendMessage(quint32 receiverId, const QString &content);

  // 获取历史消息请求
  void getMessageHistoryRequest(quint32 friendId, quint32 offset = 0,
                                quint32 count = 20);

  // 获取离线消息请求
  void getOfflineMessageRequest();

  // 修改密码请求
  void changePasswordRequest(const QString &oldPassword,
                             const QString &newPassword);

  // 更新用户信息请求
  void updateUserInfoRequest(const UserInfo &userInfo);

  // 发送文件数据（供FileTransferManager调用）
  void sendFileData(quint32 taskId, quint64 offset, const QByteArray &data);

  // 文件传输相关方法
  // 发送文件数据块
  void sendFileChunk(quint32 taskId, quint64 offset, const QByteArray &data);
  // 请求下一个文件数据块
  void requestNextFileChunk(quint32 taskId, quint64 offset, quint32 size);
  // 处理文件数据块发送完成
  void handleFileChunkSent(quint32 taskId, bool success);
  // 处理接收到的文件数据块
  void handleFileChunkReceived(quint32 taskId, const QByteArray &data);
  // 处理文件数据块传输错误
  void handleFileChunkError(quint32 taskId, const QString &error);
  // 重试文件传输
  void retryFileTransfer(quint32 taskId);

signals:
  // 连接状态改变信号
  void connectionStateChanged(bool connected);

  // 注册响应信号
  void registerResponse(bool success, const QString &message);

  // 登录响应信号
  void loginResponse(bool success, const QString &message,
                     const UserInfo &userInfo);

  // 登出响应信号
  void logoutResponse(bool success, const QString &message);

  // 文件列表响应信号
  void fileListResponse(bool success, const QString &message,
                        const QList<FileInfo> &fileList);

  // 文件传输进度信号
  void fileTransferProgress(quint32 fileId, qint64 bytesSent,
                            qint64 bytesTotal);

  // 文件传输状态信号
  void fileTransferStatus(quint32 fileId, const QString &status);

  // 上传文件响应信号
  void uploadFileResponse(bool success, const QString &message, quint32 fileId,
                          quint64 offset);

  // 下载文件响应信号
  void downloadFileResponse(bool success, const QString &message,
                            quint32 fileId, const QString &fileName,
                            quint64 fileSize);

  // 文件数据信号
  void fileDataReceived(quint32 msgId, const QByteArray &data);

  // 创建目录响应信号
  void createDirectoryResponse(bool success, const QString &message);

  // 删除文件响应信号
  void deleteFileResponse(bool success, const QString &message);

  // 重命名文件响应信号
  void renameFileResponse(bool success, const QString &message);

  // 移动文件响应信号
  void moveFileResponse(bool success, const QString &message);

  // 复制文件响应信号
  void copyFileResponse(bool success, const QString &message,
                        const QString &newFileName);

  // 分享文件响应信号
  void shareFileResponse(bool success, const QString &message,
                         const QString &shareCode);

  // 搜索文件响应信号
  void searchFileResponse(bool success, const QString &message,
                          const QList<FileInfo> &fileList);

  // 通过分享码获取文件响应信号
  void getFileByShareCodeResponse(bool success, const QString &message,
                                  const FileInfo &fileInfo);

  // 搜索用户响应信号
  void searchUserResponse(bool success, const QString &message,
                          const QList<UserInfo> &userList);

  // 添加好友响应信号
  void addFriendResponse(bool success, const QString &message);

  // 添加好友通知信号
  void addFriendNotify(const UserInfo &userInfo);

  // 同意添加好友响应信号
  void agreeAddFriendResponse(bool success, const QString &message);

  // 拒绝添加好友响应信号
  void rejectAddFriendResponse(bool success, const QString &message);

  // 删除好友响应信号
  void deleteFriendResponse(bool success, const QString &message);

  // 好友列表响应信号
  void friendListResponse(bool success, const QString &message,
                          const QList<FriendInfo> &friendList);

  // 好友状态变更通知信号
  void friendStatusNotify(quint32 friendId, bool online);

  // 接收消息信号
  void messageReceived(const MessageInfo &message);

  // 修改密码响应信号
  void changePasswordResponse(bool success, const QString &message);

  // 更新用户信息响应信号
  void updateUserInfoResponse(bool success, const QString &message);

  // 文件数据发送完成信号
  void fileDataSent(quint32 taskId, bool success);

  // 历史消息响应信号
  void messageHistoryResponse(bool success, const QString &message,
                              const QList<MessageInfo> &messageList);

  // 离线消息响应信号
  void offlineMessageResponse(bool success, const QString &message,
                              const QList<MessageInfo> &messageList);

  // 错误信号
  void errorOccurred(const QString &error);

private slots:
  // 连接成功槽函数
  void onConnected();

  // 断开连接槽函数
  void onDisconnected();

  // 接收数据槽函数
  void onReadyRead();

  // 错误处理槽函数
  void onError(QAbstractSocket::SocketError socketError);

  // 心跳超时槽函数
  void onHeartbeatTimeout();

  // 文件传输相关槽函数
  // 数据块准备好发送槽函数
  void onChunkReadyToSend(quint32 taskId, quint64 offset,
                          const QByteArray &data);
  // 请求下一个数据块槽函数
  void onRequestNextChunk(quint32 taskId, quint64 offset, quint32 size);
  // 任务重试槽函数
  void onTaskRetried(quint32 taskId);

  // 请求重试方法
  void retryRequest(quint32 msgId, const QJsonObject &requestData,
                    MessageType msgType);

  // 处理文件数据
  void handleFileData(const QByteArray &data);
  // 处理文件上传完成
  void handleCompleteFileUpload(const QJsonObject &jsonObj);
  // 处理文件下载响应
  void handleDownloadFileResponse(const QJsonObject &jsonObj);

private:
  // 私有构造函数
  NetworkManager(QObject *parent = nullptr);

  // 私有析构函数
  ~NetworkManager();

  // 禁止拷贝构造和赋值
  NetworkManager(const NetworkManager &) = delete;
  NetworkManager &operator=(const NetworkManager &) = delete;

  // 处理接收到的数据包
  void processPacket(const Packet &packet);

  // 解析JSON消息
  void parseJsonMessage(quint32 msgType, const QJsonObject &jsonObj);

  // 计算密码哈希
  QString calculatePasswordHash(const QString &password,
                                const QString &salt = "");

  // 生成盐值
  QString generateSalt();

  // 计算文件哈希
  QString calculateFileHash(const QString &filePath);

  // 验证文件哈希
  bool verifyFileHash(const QString &filePath, const QString &expectedHash);

  // 启动心跳定时器
  void startHeartbeatTimer();

  // 停止心跳定时器
  void stopHeartbeatTimer();

  // 发送心跳包
  void sendHeartbeat();

  // 处理文件传输完成
  void handleFileTransferComplete(quint32 fileId);

  // 添加文件操作到队列
  void addFileOperation(FileOperationType type, quint32 fileId,
                        const QString &filePath = "", quint32 parentId = 0,
                        const QString &newName = "", quint32 newParentId = 0);

  // 处理文件操作队列
  void processFileOperationQueue();

  // 处理单个文件操作
  void processFileOperation(const FileOperation &operation);

  // 完成文件操作
  void completeFileOperation(const FileOperation &operation, bool success,
                             const QString &message = "");

public:
  // 取消文件操作
  void cancelFileOperation(quint32 fileId);

  // 获取文件操作进度
  quint64 getFileOperationProgress(quint32 fileId) const;

  // 获取文件操作状态
  FileOperationType getFileOperationType(quint32 fileId) const;

  // 获取文件操作队列大小
  int getFileOperationQueueSize() const;

private:
  static NetworkManager *m_instance; // 单例实例
  static QMutex m_mutex;             // 互斥锁

  QTcpSocket *m_socket;     // TCP套接字
  QByteArray m_buffer;      // 接收缓冲区
  quint32 m_expectedSize;   // 期望接收的数据大小
  QTimer *m_heartbeatTimer; // 心跳定时器
  QTimer *m_reconnectTimer; // 重连定时器
  bool m_connected;         // 连接状态
  QString m_host;           // 服务器地址
  quint16 m_port;           // 服务器端口

  // 文件传输进度跟踪
  QMap<quint32, qint64> m_uploadProgress;   // 上传进度映射
  QMap<quint32, qint64> m_downloadProgress; // 下载进度映射
  QMap<quint32, qint64> m_fileSizes;        // 文件大小映射

  // 文件操作队列

  QList<FileOperation> m_fileOperationQueue; // 文件操作队列
  bool m_isProcessingFileOperation;          // 是否正在处理文件操作
  quint32 m_msgIdCounter;                    // 消息ID计数器
  QMap<quint32, QFile *> m_uploadFiles;      // 上传文件映射
  QMap<quint32, QFile *> m_downloadFiles;    // 下载文件映射
  UserInfo m_currentUser;                    // 当前用户信息

  // 重试机制相关
  int m_maxRetries;                    // 最大重试次数
  int m_currentRetries;                // 当前重试次数
  int m_reconnectInterval;             // 重连间隔（秒）
  QMap<quint32, int> m_requestRetries; // 请求重试次数映射
};

#endif // NETWORKMANAGER_H
