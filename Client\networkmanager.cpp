#include "networkmanager.h"
#include "configmanager.h"
#include "logger.h"
#include <QCryptographicHash>
#include <QDataStream>
#include <QDateTime>
#include <QDir>
#include <QFileInfo>
#include <QJsonArray>
#include <QMutex>
#include <QMutexLocker>
#include <QRandomGenerator>
#include <QStandardPaths>

// 初始化静态成员变量
NetworkManager *NetworkManager::m_instance = nullptr;
QMutex NetworkManager::m_mutex;

// 获取单例实例
NetworkManager *NetworkManager::getInstance() {
  if (m_instance == nullptr) {
    QMutexLocker locker(&m_mutex);
    if (m_instance == nullptr) {
      m_instance = new NetworkManager();
    }
  }
  return m_instance;
}

// 私有构造函数
NetworkManager::NetworkManager(QObject *parent)
    : QObject(parent), m_socket(nullptr), m_expectedSize(0),
      m_heartbeatTimer(nullptr), m_reconnectTimer(nullptr), m_connected(false),
      m_port(0), m_msgIdCounter(0), m_isProcessingFileOperation(false),
      m_maxRetries(3), m_currentRetries(0), m_reconnectInterval(5) {
  // 创建TCP套接字
  m_socket = new QTcpSocket(this);

  // 连接信号槽
  connect(m_socket, &QTcpSocket::connected, this, &NetworkManager::onConnected);
  connect(m_socket, &QTcpSocket::disconnected, this,
          &NetworkManager::onDisconnected);
  connect(m_socket, &QTcpSocket::readyRead, this, &NetworkManager::onReadyRead);
  connect(m_socket, &QAbstractSocket::errorOccurred, this,
          &NetworkManager::onError);

  // 创建心跳定时器
  m_heartbeatTimer = new QTimer(this);
  connect(m_heartbeatTimer, &QTimer::timeout, this,
          &NetworkManager::onHeartbeatTimeout);

  // 创建重连定时器
  m_reconnectTimer = new QTimer(this);
  m_reconnectTimer->setSingleShot(true);

  // 获取FileTransferManager实例并连接信号槽
  FileTransferManager *fileTransferManager = FileTransferManager::getInstance();
  connect(this, &NetworkManager::sendFileChunk, fileTransferManager,
          &FileTransferManager::handleChunkSent);
  connect(this, &NetworkManager::requestNextFileChunk, fileTransferManager,
          &FileTransferManager::sendNextChunk);
  connect(this, &NetworkManager::handleFileChunkSent, fileTransferManager,
          &FileTransferManager::handleChunkSent);
  connect(this, &NetworkManager::handleFileChunkReceived, fileTransferManager,
          &FileTransferManager::handleChunkReceived);
  connect(this, &NetworkManager::handleFileChunkError, fileTransferManager,
          &FileTransferManager::handleChunkError);
  connect(this, &NetworkManager::retryFileTransfer, fileTransferManager,
          &FileTransferManager::retryTransfer);

  // 连接FileTransferManager的信号到NetworkManager的槽
  connect(fileTransferManager, &FileTransferManager::chunkReadyToSend, this,
          &NetworkManager::onChunkReadyToSend);
  connect(fileTransferManager, &FileTransferManager::requestNextChunk, this,
          &NetworkManager::onRequestNextChunk);
  connect(fileTransferManager, &FileTransferManager::taskRetried, this,
          &NetworkManager::onTaskRetried);

  LOG_INFO("Network", "网络管理器初始化完成，已集成文件传输管理器");
  connect(m_reconnectTimer, &QTimer::timeout, [this]() {
    if (!isConnected()) {
      connectToServer(m_host, m_port);
    }
  });
}

// 私有析构函数
NetworkManager::~NetworkManager() {
  // 断开连接
  disconnectFromServer();

  // 清理上传和下载文件
  for (auto it = m_uploadFiles.begin(); it != m_uploadFiles.end(); ++it) {
    QFile *file = it.value();
    if (file) {
      file->close();
      delete file;
    }
  }
  m_uploadFiles.clear();

  for (auto it = m_downloadFiles.begin(); it != m_downloadFiles.end(); ++it) {
    QFile *file = it.value();
    if (file) {
      file->close();
      delete file;
    }
  }
  m_downloadFiles.clear();
}

// 连接服务器
bool NetworkManager::connectToServer(const QString &host, quint16 port) {
  if (isConnected()) {
    LOG_WARN("Network", "已经连接到服务器，无需重复连接");
    return true;
  }

  // 保存服务器信息
  m_host = host;
  m_port = port;

  // 连接服务器
  LOG_INFO("Network", QString("正在连接服务器 %1:%2").arg(host).arg(port));
  m_socket->connectToHost(host, port);

  return true;
}

// 断开连接
void NetworkManager::disconnectFromServer() {
  if (isConnected()) {
    // 发送登出请求
    logoutRequest();

    // 停止心跳定时器
    stopHeartbeatTimer();

    // 断开连接
    LOG_INFO("Network", "正在断开与服务器的连接");
    m_socket->disconnectFromHost();

    // 等待断开连接
    if (m_socket->state() != QAbstractSocket::UnconnectedState) {
      m_socket->waitForDisconnected(3000);
    }
  }
}

// 检查是否已连接
bool NetworkManager::isConnected() const {
  return m_connected && (m_socket->state() == QAbstractSocket::ConnectedState);
}

// 发送数据包
bool NetworkManager::sendPacket(const Packet &packet) {
  if (!isConnected()) {
    LOG_ERROR("Network", "未连接到服务器，无法发送数据包");
    return false;
  }

  // 序列化数据包
  QByteArray data = packet.toByteArray();

  // 发送数据
  qint64 bytesWritten = m_socket->write(data);
  if (bytesWritten == -1) {
    LOG_ERROR("Network",
              QString("发送数据失败: %1").arg(m_socket->errorString()));
    return false;
  }

  // 刷新缓冲区
  m_socket->flush();

  LOG_DEBUG("Network", QString("发送数据包成功，类型: %1, 大小: %2 字节")
                           .arg(packet.msgType)
                           .arg(packet.msgLength));

  return true;
}

// 发送JSON消息
bool NetworkManager::sendJsonMessage(quint32 msgType,
                                     const QJsonObject &jsonObj) {
  // 创建数据包
  Packet packet;
  packet.msgType = msgType;

  // 将JSON对象转换为字节数组
  QJsonDocument jsonDoc(jsonObj);
  packet.msgData = jsonDoc.toJson(QJsonDocument::Compact);
  packet.msgLength = packet.msgData.size();

  // 计算校验和
  packet.checksum = packet.calculateChecksum();

  // 发送数据包
  return sendPacket(packet);
}

// 发送文件数据
bool NetworkManager::sendFileData(quint32 msgType, const QByteArray &fileData) {
  // 创建数据包
  Packet packet;
  packet.msgType = msgType;
  packet.msgData = fileData;
  packet.msgLength = fileData.size();

  // 计算校验和
  packet.checksum = packet.calculateChecksum();

  // 发送数据包
  return sendPacket(packet);
}

// 注册请求
void NetworkManager::registerRequest(const QString &username,
                                     const QString &password,
                                     const QString &email) {
  // 生成随机盐值
  QString salt = generateSalt();

  // 计算密码哈希
  QString passwordHash = calculatePasswordHash(password, salt);

  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["username"] = username;
  jsonObj["passwordHash"] = passwordHash;
  jsonObj["salt"] = salt;
  jsonObj["email"] = email;

  // 发送注册请求
  LOG_INFO("Network", QString("发送注册请求，用户名: %1").arg(username));
  sendJsonMessage(MSG_REGISTER, jsonObj);
}

// 登录请求
void NetworkManager::loginRequest(const QString &username,
                                  const QString &password) {
  // 生成随机盐值
  QString salt = generateSalt();

  // 计算密码哈希
  QString passwordHash = calculatePasswordHash(password, salt);

  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["username"] = username;
  jsonObj["passwordHash"] = passwordHash;
  jsonObj["salt"] = salt;

  // 发送登录请求
  LOG_INFO("Network", QString("发送登录请求，用户名: %1").arg(username));
  sendJsonMessage(MSG_LOGIN, jsonObj);
}

// 登出请求
void NetworkManager::logoutRequest() {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);

  // 发送登出请求
  LOG_INFO("Network",
           QString("发送登出请求，用户ID: %1").arg(m_currentUser.userId));
  sendJsonMessage(MSG_LOGOUT, jsonObj);
}

// 获取文件列表请求
void NetworkManager::getFileListRequest(quint32 parentId) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["parentId"] = static_cast<qint64>(parentId);

  // 发送获取文件列表请求
  LOG_INFO("Network", QString("发送获取文件列表请求，用户ID: %1，父目录ID: %2")
                          .arg(m_currentUser.userId)
                          .arg(parentId));
  sendJsonMessage(MSG_FILE_LIST_REQ, jsonObj);
}

// 上传文件请求
void NetworkManager::uploadFileRequest(const QString &filePath,
                                       quint32 parentId) {
  // 检查文件是否存在
  QFileInfo fileInfo(filePath);
  if (!fileInfo.exists()) {
    LOG_ERROR("Network", QString("文件不存在: %1").arg(filePath));
    emit errorOccurred("文件不存在: " + filePath);
    return;
  }

  // 获取文件信息
  QString fileName = fileInfo.fileName();
  quint64 fileSize = fileInfo.size();
  QString fileHash = calculateFileHash(filePath);

  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["fileName"] = fileName;
  jsonObj["fileSize"] = QString::number(fileSize);
  jsonObj["fileHash"] = fileHash;
  jsonObj["parentId"] = static_cast<qint64>(parentId);

  // 发送上传文件请求
  LOG_INFO("Network", QString("发送上传文件请求，文件名: %1，大小: %2 字节")
                          .arg(fileName)
                          .arg(fileSize));
  sendJsonMessage(MSG_FILE_UPLOAD_REQ, jsonObj);

  // 初始化上传进度
  quint32 fileId = jsonObj["fileId"].toVariant().toUInt();
  m_uploadProgress[fileId] = 0;
  m_fileSizes[fileId] = fileSize;

  // 发送传输状态信号
  emit fileTransferStatus(fileId, "准备上传");
}

// 发送文件数据块
void NetworkManager::sendFileDataChunk(const QByteArray &data, quint32 fileId) {
  // 发送文件数据
  LOG_DEBUG("Network", QString("发送文件数据块，文件ID: %1，大小: %2 字节")
                           .arg(fileId)
                           .arg(data.size()));
  sendFileData(MSG_FILE_DATA, data);

  // 更新上传进度
  if (m_uploadProgress.contains(fileId)) {
    m_uploadProgress[fileId] += data.size();
    qint64 fileSize = m_fileSizes.value(fileId, 0);
    emit fileTransferProgress(fileId, m_uploadProgress[fileId], fileSize);

    // 更新传输状态
    QString status =
        QString("上传中: %1%")
            .arg(fileSize > 0 ? QString::number(m_uploadProgress[fileId] * 100 /
                                                fileSize)
                              : "0");
    emit fileTransferStatus(fileId, status);
  }
}

// 完成文件上传
void NetworkManager::completeFileUpload(quint32 fileId,
                                        const QString &fileHash) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["fileId"] = static_cast<qint64>(fileId);
  jsonObj["fileHash"] = fileHash;

  // 发送完成文件上传请求
  LOG_INFO("Network", QString("发送完成文件上传请求，文件ID: %1").arg(fileId));
  sendJsonMessage(MSG_FILE_UPLOAD_COMPLETE, jsonObj);

  // 更新传输状态
  if (m_uploadProgress.contains(fileId)) {
    emit fileTransferStatus(fileId, "上传完成");
    // 清理进度跟踪
    m_uploadProgress.remove(fileId);
    m_fileSizes.remove(fileId);
  }
}

// 下载文件请求
void NetworkManager::downloadFileRequest(quint32 fileId, quint64 offset) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["fileId"] = static_cast<qint64>(fileId);
  jsonObj["offset"] = QString::number(offset);

  // 发送下载文件请求
  LOG_INFO("Network", QString("发送下载文件请求，文件ID: %1，偏移量: %2")
                          .arg(fileId)
                          .arg(offset));
  sendJsonMessage(MSG_FILE_DOWNLOAD_REQ, jsonObj);

  // 初始化下载进度
  m_downloadProgress[fileId] = offset;

  // 发送传输状态信号
  emit fileTransferStatus(fileId, "准备下载");
}

// 创建目录请求
void NetworkManager::createDirectoryRequest(const QString &dirName,
                                            quint32 parentId) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["dirName"] = dirName;
  jsonObj["parentId"] = static_cast<qint64>(parentId);

  // 发送创建目录请求
  LOG_INFO("Network", QString("发送创建目录请求，目录名: %1，父目录ID: %2")
                          .arg(dirName)
                          .arg(parentId));
  sendJsonMessage(MSG_FILE_CREATE_DIR_REQ, jsonObj);
}

// 删除文件请求
void NetworkManager::deleteFileRequest(quint32 fileId) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["fileId"] = static_cast<qint64>(fileId);

  // 发送删除文件请求
  LOG_INFO("Network", QString("发送删除文件请求，文件ID: %1").arg(fileId));
  sendJsonMessage(MSG_FILE_DELETE_REQ, jsonObj);
}

// 重命名文件请求
void NetworkManager::renameFileRequest(quint32 fileId, const QString &newName) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["fileId"] = static_cast<qint64>(fileId);
  jsonObj["newName"] = newName;

  // 发送重命名文件请求
  LOG_INFO("Network", QString("发送重命名文件请求，文件ID: %1，新名称: %2")
                          .arg(fileId)
                          .arg(newName));
  sendJsonMessage(MSG_FILE_RENAME_REQ, jsonObj);
}

// 移动文件请求
void NetworkManager::moveFileRequest(quint32 fileId, quint32 newParentId) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["fileId"] = static_cast<qint64>(fileId);
  jsonObj["newParentId"] = static_cast<qint64>(newParentId);

  // 发送移动文件请求
  LOG_INFO("Network", QString("发送移动文件请求，文件ID: %1，新父目录ID: %2")
                          .arg(fileId)
                          .arg(newParentId));
  sendJsonMessage(MSG_FILE_MOVE_REQ, jsonObj);
}

// 复制文件请求
void NetworkManager::copyFileRequest(quint32 fileId, quint32 newParentId) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["fileId"] = static_cast<qint64>(fileId);
  jsonObj["newParentId"] = static_cast<qint64>(newParentId);

  // 发送复制文件请求
  LOG_INFO("Network", QString("发送复制文件请求，文件ID: %1，新父目录ID: %2")
                          .arg(fileId)
                          .arg(newParentId));
  sendJsonMessage(MSG_FILE_COPY_REQ, jsonObj);
}

// 分享文件请求
void NetworkManager::shareFileRequest(quint32 fileId,
                                      const QString &expireTime) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["fileId"] = static_cast<qint64>(fileId);
  jsonObj["expireTime"] = expireTime;

  // 发送分享文件请求
  LOG_INFO("Network", QString("发送分享文件请求，文件ID: %1，过期时间: %2")
                          .arg(fileId)
                          .arg(expireTime));
  sendJsonMessage(MSG_FILE_SHARE_REQ, jsonObj);
}

// 搜索文件请求
void NetworkManager::searchFileRequest(const QString &keyword,
                                       quint32 parentId) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["keyword"] = keyword;
  jsonObj["parentId"] = static_cast<qint64>(parentId);

  // 发送搜索文件请求
  LOG_INFO("Network", QString("发送搜索文件请求，关键词: %1，父目录ID: %2")
                          .arg(keyword)
                          .arg(parentId));
  sendJsonMessage(MSG_FILE_SEARCH_REQ, jsonObj);
}

// 通过分享码获取文件请求
void NetworkManager::getFileByShareCodeRequest(const QString &shareCode) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["shareCode"] = shareCode;

  // 发送通过分享码获取文件请求
  LOG_INFO("Network",
           QString("发送通过分享码获取文件请求，分享码: %1").arg(shareCode));
  sendJsonMessage(MSG_FILE_GET_SHARE_REQ, jsonObj);
}

// 搜索用户请求
void NetworkManager::searchUserRequest(const QString &keyword) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["keyword"] = keyword;

  // 发送搜索用户请求
  LOG_INFO("Network", QString("发送搜索用户请求，关键词: %1").arg(keyword));
  sendJsonMessage(MSG_FRIEND_SEARCH_REQ, jsonObj);
}

// 添加好友请求
void NetworkManager::addFriendRequest(quint32 userId) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["friendId"] = static_cast<qint64>(userId);

  // 发送添加好友请求
  LOG_INFO("Network", QString("发送添加好友请求，好友ID: %1").arg(userId));
  sendJsonMessage(MSG_FRIEND_ADD_REQ, jsonObj);
}

// 同意添加好友
void NetworkManager::agreeAddFriend(quint32 userId) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["friendId"] = static_cast<qint64>(userId);

  // 发送同意添加好友请求
  LOG_INFO("Network", QString("发送同意添加好友请求，好友ID: %1").arg(userId));
  sendJsonMessage(MSG_FRIEND_ADD_AGREE, jsonObj);
}

// 拒绝添加好友
void NetworkManager::rejectAddFriend(quint32 userId) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["friendId"] = static_cast<qint64>(userId);

  // 发送拒绝添加好友请求
  LOG_INFO("Network", QString("发送拒绝添加好友请求，好友ID: %1").arg(userId));
  sendJsonMessage(MSG_FRIEND_ADD_REJECT, jsonObj);
}

// 删除好友请求
void NetworkManager::deleteFriendRequest(quint32 userId) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["friendId"] = static_cast<qint64>(userId);

  // 发送删除好友请求
  LOG_INFO("Network", QString("发送删除好友请求，好友ID: %1").arg(userId));
  sendJsonMessage(MSG_FRIEND_DELETE_REQ, jsonObj);
}

// 获取好友列表请求
void NetworkManager::getFriendListRequest() {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);

  // 发送获取好友列表请求
  LOG_INFO(
      "Network",
      QString("发送获取好友列表请求，用户ID: %1").arg(m_currentUser.userId));
  sendJsonMessage(MSG_FRIEND_LIST_REQ, jsonObj);
}

// 发送消息
void NetworkManager::sendMessage(quint32 receiverId, const QString &content) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["senderId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["receiverId"] = static_cast<qint64>(receiverId);
  jsonObj["content"] = content;
  jsonObj["sendTime"] =
      QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");

  // 发送消息
  LOG_INFO(
      "Network",
      QString("发送消息，接收者ID: %1，内容: %2").arg(receiverId).arg(content));
  sendJsonMessage(MSG_MESSAGE_SEND, jsonObj);
}

// 获取历史消息请求
void NetworkManager::getMessageHistoryRequest(quint32 friendId, quint32 offset,
                                              quint32 count) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["friendId"] = static_cast<qint64>(friendId);
  jsonObj["offset"] = static_cast<qint64>(offset);
  jsonObj["count"] = static_cast<qint64>(count);

  // 发送获取历史消息请求
  LOG_INFO("Network",
           QString("发送获取历史消息请求，好友ID: %1，偏移量: %2，数量: %3")
               .arg(friendId)
               .arg(offset)
               .arg(count));
  sendJsonMessage(MSG_MESSAGE_HISTORY_REQ, jsonObj);
}

// 获取离线消息请求
void NetworkManager::getOfflineMessageRequest() {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);

  // 发送获取离线消息请求
  LOG_INFO(
      "Network",
      QString("发送获取离线消息请求，用户ID: %1").arg(m_currentUser.userId));
  sendJsonMessage(MSG_MESSAGE_OFFLINE_REQ, jsonObj);
}

// 修改密码请求
void NetworkManager::changePasswordRequest(const QString &oldPassword,
                                           const QString &newPassword) {
  // 生成盐值
  QString salt = generateSalt();

  // 计算旧密码哈希
  QString oldPasswordHash = calculatePasswordHash(oldPassword, salt);

  // 计算新密码哈希
  QString newPasswordHash = calculatePasswordHash(newPassword, salt);

  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(m_currentUser.userId);
  jsonObj["oldPasswordHash"] = oldPasswordHash;
  jsonObj["newPasswordHash"] = newPasswordHash;
  jsonObj["salt"] = salt;

  // 发送请求
  LOG_INFO("Network",
           QString("发送修改密码请求，用户ID: %1").arg(m_currentUser.userId));
  sendJsonMessage(MSG_CHANGE_PASSWORD_REQ, jsonObj);
}

// 更新用户信息请求
void NetworkManager::updateUserInfoRequest(const UserInfo &userInfo) {
  // 创建JSON对象
  QJsonObject jsonObj;
  jsonObj["userId"] = static_cast<qint64>(userInfo.userId);
  jsonObj["username"] = userInfo.username;
  jsonObj["email"] = userInfo.email;

  // 发送请求
  LOG_INFO("Network",
           QString("发送更新用户信息请求，用户ID: %1").arg(userInfo.userId));
  sendJsonMessage(MSG_UPDATE_USER_INFO_REQ, jsonObj);
}

// 发送文件数据（供FileTransferManager调用）
void NetworkManager::sendFileData(quint32 taskId, quint64 offset,
                                  const QByteArray &data) {
  // 创建文件数据包
  Packet packet;
  packet.msgType = MSG_FILE_DATA;
  packet.msgLength = data.size();
  packet.msgData = data;
  packet.checksum = packet.calculateChecksum();

  // 发送数据包
  bool success = sendPacket(packet);

  LOG_INFO("Network",
           QString("发送文件数据块，任务ID: %1，偏移量: %2，大小: %3，结果: %4")
               .arg(taskId)
               .arg(offset)
               .arg(data.size())
               .arg(success ? "成功" : "失败"));

  // 通知FileTransferManager发送结果
  if (success) {
    // 这里可以发送成功信号或直接调用FileTransferManager的方法
    emit fileDataSent(taskId, true);
  } else {
    emit fileDataSent(taskId, false);
  }
}

// 连接成功槽函数
void NetworkManager::onConnected() {
  m_connected = true;

  // 重置重试计数器
  m_currentRetries = 0;

  // 停止重连定时器
  if (m_reconnectTimer->isActive()) {
    m_reconnectTimer->stop();
  }

  LOG_INFO("Network",
           QString("成功连接到服务器 %1:%2").arg(m_host).arg(m_port));

  // 启动心跳定时器
  startHeartbeatTimer();

  // 发送连接状态改变信号
  emit connectionStateChanged(true);
}

// 断开连接槽函数
void NetworkManager::onDisconnected() {
  m_connected = false;
  LOG_INFO("Network", "与服务器断开连接");

  // 停止心跳定时器
  stopHeartbeatTimer();

  // 发送连接状态改变信号
  emit connectionStateChanged(false);

  // 如果是意外断开，则尝试重新连接
  if (m_reconnectTimer && !m_reconnectTimer->isActive()) {
    int reconnectInterval =
        ConfigManager::getInstance()->getReconnectInterval();
    LOG_INFO("Network", QString("%1 秒后尝试重新连接").arg(reconnectInterval));
    m_reconnectTimer->start(reconnectInterval * 1000);
  }
}

// 接收数据槽函数
void NetworkManager::onReadyRead() {
  // 读取所有可用数据
  QByteArray data = m_socket->readAll();
  m_buffer.append(data);

  LOG_DEBUG("Network", QString("接收到数据，大小: %1 字节").arg(data.size()));

  // 处理缓冲区中的数据
  while (true) {
    // 如果还没有期望的数据大小，且缓冲区大小足够读取消息头
    if (m_expectedSize == 0 &&
        m_buffer.size() >= static_cast<int>(sizeof(quint32) * 3)) {
      QDataStream in(m_buffer);
      in.setVersion(QDataStream::Qt_6_9);

      // 读取消息类型和消息长度
      quint32 msgType, msgLength;
      in >> msgType >> msgLength;

      // 设置期望的数据大小（消息头 + 消息体 + 校验和）
      m_expectedSize = sizeof(quint32) * 3 + msgLength;
    }

    // 如果缓冲区大小达到期望的数据大小
    if (m_expectedSize > 0 && m_buffer.size() >= m_expectedSize) {
      // 提取一个完整的数据包
      QByteArray packetData = m_buffer.left(m_expectedSize);
      m_buffer.remove(0, m_expectedSize);

      // 重置期望的数据大小
      m_expectedSize = 0;

      // 解析数据包
      QDataStream in(packetData);
      in.setVersion(QDataStream::Qt_6_9);

      // 读取数据包
      Packet packet;
      in >> packet.msgType >> packet.msgLength;

      // 读取消息体
      if (packet.msgLength > 0) {
        packet.msgData.resize(packet.msgLength);
        in.readRawData(packet.msgData.data(), packet.msgLength);
      }

      // 读取校验和
      in >> packet.checksum;

      // 验证校验和
      quint32 calculatedChecksum = packet.calculateChecksum();
      if (packet.checksum != calculatedChecksum) {
        LOG_ERROR("Network", QString("校验和验证失败，期望: %1，实际: %2")
                                 .arg(packet.checksum)
                                 .arg(calculatedChecksum));
        continue;
      }

      // 处理数据包
      processPacket(packet);
    } else {
      // 缓冲区数据不足，等待更多数据
      break;
    }
  }
}

// 错误处理槽函数
void NetworkManager::onError(QAbstractSocket::SocketError socketError) {
  QString errorMsg;
  bool shouldRetry = false;

  switch (socketError) {
  case QAbstractSocket::ConnectionRefusedError:
    errorMsg = "连接被服务器拒绝";
    shouldRetry = true;
    break;
  case QAbstractSocket::RemoteHostClosedError:
    errorMsg = "远程主机关闭了连接";
    shouldRetry = true;
    break;
  case QAbstractSocket::HostNotFoundError:
    errorMsg = "找不到主机";
    shouldRetry = false; // 主机不存在，重试无意义
    break;
  case QAbstractSocket::SocketTimeoutError:
    errorMsg = "连接超时";
    shouldRetry = true;
    break;
  case QAbstractSocket::NetworkError:
    errorMsg = "网络错误";
    shouldRetry = true;
    break;
  default:
    errorMsg = QString("未知错误: %1").arg(m_socket->errorString());
    shouldRetry = false;
    break;
  }

  LOG_ERROR("Network", QString("网络错误: %1").arg(errorMsg));

  // 如果应该重试且还有重试次数
  if (shouldRetry && m_currentRetries < m_maxRetries) {
    m_currentRetries++;
    LOG_INFO("Network", QString("准备重试连接，第 %1/%2 次")
                            .arg(m_currentRetries)
                            .arg(m_maxRetries));

    // 启动重连定时器
    m_reconnectTimer->start(m_reconnectInterval * 1000);
  } else {
    // 重试次数用完或不应该重试，发送错误信号
    m_currentRetries = 0;
    emit errorOccurred(errorMsg);
  }
}

// 心跳超时槽函数
void NetworkManager::onHeartbeatTimeout() {
  // 发送心跳包
  sendHeartbeat();
}

// 处理接收到的数据包
void NetworkManager::processPacket(const Packet &packet) {
  LOG_DEBUG("Network", QString("处理数据包，类型: %1，大小: %2 字节")
                           .arg(packet.msgType)
                           .arg(packet.msgLength));

  // 根据消息类型处理数据包
  switch (packet.msgType) {
  case MSG_FILE_DATA: {
    // 文件数据，直接发送信号
    emit fileDataReceived(packet.msgType, packet.msgData);

    // 更新下载进度
    // 从文件数据中提取文件ID
    QDataStream in(packet.msgData);
    quint32 fileId;
    in >> fileId;

    if (m_downloadProgress.contains(fileId)) {
      m_downloadProgress[fileId] += packet.msgData.size();

      // 发送进度信号
      emit fileTransferProgress(fileId, m_downloadProgress[fileId],
                                m_fileSizes.value(fileId, 0));

      // 更新传输状态
      qint64 fileSize = m_fileSizes.value(fileId, 0);
      QString status =
          QString("下载中: %1%")
              .arg(fileSize > 0 ? QString::number(m_downloadProgress[fileId] *
                                                  100 / fileSize)
                                : "0");
      emit fileTransferStatus(fileId, status);
    }
    break;
  }
  default: {
    // 其他消息，解析JSON
    QJsonDocument jsonDoc = QJsonDocument::fromJson(packet.msgData);
    if (jsonDoc.isNull() || !jsonDoc.isObject()) {
      LOG_ERROR("Network", "解析JSON消息失败");
      return;
    }

    QJsonObject jsonObj = jsonDoc.object();
    parseJsonMessage(packet.msgType, jsonObj);
    break;
  }
  }
}

// 解析JSON消息
void NetworkManager::parseJsonMessage(quint32 msgType,
                                      const QJsonObject &jsonObj) {
  bool success = jsonObj["success"].toBool();
  QString message = jsonObj["message"].toString();

  switch (msgType) {
  case MSG_REGISTER_RESP:
    // 注册响应
    LOG_INFO(
        "Network",
        QString("收到注册响应，成功: %1，消息: %2").arg(success).arg(message));
    emit registerResponse(success, message);
    break;

  case MSG_LOGIN_RESP:
    // 登录响应
    LOG_INFO(
        "Network",
        QString("收到登录响应，成功: %1，消息: %2").arg(success).arg(message));
    if (success) {
      // 解析用户信息
      UserInfo userInfo;
      userInfo.userId = jsonObj["userId"].toVariant().toUInt();
      userInfo.username = jsonObj["username"].toString();
      userInfo.email = jsonObj["email"].toString();
      userInfo.storageUsed = jsonObj["storageUsed"].toVariant().toULongLong();
      userInfo.storageTotal = jsonObj["storageTotal"].toVariant().toULongLong();
      userInfo.registerTime = jsonObj["registerTime"].toString();
      userInfo.lastLoginTime = jsonObj["lastLoginTime"].toString();

      // 保存当前用户信息
      m_currentUser = userInfo;

      // 发送登录响应信号
      emit loginResponse(success, message, userInfo);

      // 获取好友列表
      getFriendListRequest();

      // 获取离线消息
      getOfflineMessageRequest();
    } else {
      emit loginResponse(success, message, UserInfo());
    }
    break;

  case MSG_LOGOUT_RESP:
    // 登出响应
    LOG_INFO(
        "Network",
        QString("收到登出响应，成功: %1，消息: %2").arg(success).arg(message));
    emit logoutResponse(success, message);
    break;

  case MSG_FILE_LIST_RESP:
    // 文件列表响应
    LOG_INFO("Network", QString("收到文件列表响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    if (success) {
      QList<FileInfo> fileList;
      QJsonArray fileArray = jsonObj["files"].toArray();

      for (int i = 0; i < fileArray.size(); ++i) {
        QJsonObject fileObj = fileArray[i].toObject();
        FileInfo fileInfo;
        fileInfo.fileId = fileObj["fileId"].toVariant().toUInt();
        fileInfo.fileName = fileObj["fileName"].toString();
        fileInfo.fileSize = fileObj["fileSize"].toVariant().toUInt();
        fileInfo.fileType = fileObj["fileType"].toString();
        fileInfo.parentId = fileObj["parentId"].toVariant().toUInt();
        fileInfo.ownerId = fileObj["ownerId"].toVariant().toUInt();
        fileInfo.filePath = fileObj["filePath"].toString();
        fileInfo.fileHash = fileObj["fileHash"].toString();
        fileInfo.createTime = fileObj["createTime"].toString();
        fileInfo.modifyTime = fileObj["modifyTime"].toString();
        fileInfo.isDir = fileObj["isDir"].toBool();

        fileList.append(fileInfo);
      }

      emit fileListResponse(success, message, fileList);
    } else {
      emit fileListResponse(success, message, QList<FileInfo>());
    }
    break;

  case MSG_FILE_UPLOAD_RESP:
    // 上传文件响应
    LOG_INFO("Network", QString("收到上传文件响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    if (success) {
      quint32 fileId = jsonObj["fileId"].toVariant().toUInt();
      quint64 offset = jsonObj["offset"].toVariant().toULongLong();

      emit uploadFileResponse(success, message, fileId, offset);
    } else {
      emit uploadFileResponse(success, message, 0, 0);
    }
    break;

  case MSG_FILE_DOWNLOAD_RESP:
    // 下载文件响应
    LOG_INFO("Network", QString("收到下载文件响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    if (success) {
      quint32 fileId = jsonObj["fileId"].toVariant().toUInt();
      QString fileName = jsonObj["fileName"].toString();
      quint64 fileSize = jsonObj["fileSize"].toVariant().toULongLong();

      // 保存文件大小
      m_fileSizes[fileId] = fileSize;

      emit downloadFileResponse(success, message, fileId, fileName, fileSize);

      // 更新传输状态
      emit fileTransferStatus(fileId, "下载中");
    } else {
      emit downloadFileResponse(success, message, 0, "", 0);
    }
    break;

  case MSG_FILE_CREATE_DIR_RESP:
    // 创建目录响应
    LOG_INFO("Network", QString("收到创建目录响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    emit createDirectoryResponse(success, message);
    break;

  case MSG_FILE_DELETE_RESP:
    // 删除文件响应
    LOG_INFO("Network", QString("收到删除文件响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    emit deleteFileResponse(success, message);
    break;

  case MSG_FILE_RENAME_RESP:
    // 重命名文件响应
    LOG_INFO("Network", QString("收到重命名文件响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    emit renameFileResponse(success, message);
    break;

  case MSG_FILE_MOVE_RESP:
    // 移动文件响应
    LOG_INFO("Network", QString("收到移动文件响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    emit moveFileResponse(success, message);
    break;

  case MSG_FILE_COPY_RESP:
    // 复制文件响应
    LOG_INFO("Network", QString("收到复制文件响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    emit copyFileResponse(success, message, jsonObj["newFileName"].toString());
    break;

  case MSG_FILE_SHARE_RESP:
    // 分享文件响应
    LOG_INFO("Network", QString("收到分享文件响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    if (success) {
      QString shareCode = jsonObj["shareCode"].toString();
      emit shareFileResponse(success, message, shareCode);
    } else {
      emit shareFileResponse(success, message, "");
    }
    break;

  case MSG_FILE_SEARCH_RESP:
    // 搜索文件响应
    LOG_INFO("Network", QString("收到搜索文件响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    if (success) {
      QList<FileInfo> fileList;
      QJsonArray fileArray = jsonObj["files"].toArray();

      for (int i = 0; i < fileArray.size(); ++i) {
        QJsonObject fileObj = fileArray[i].toObject();
        FileInfo fileInfo;
        fileInfo.fileId = fileObj["fileId"].toVariant().toUInt();
        fileInfo.fileName = fileObj["fileName"].toString();
        fileInfo.fileSize = fileObj["fileSize"].toVariant().toUInt();
        fileInfo.fileType = fileObj["fileType"].toString();
        fileInfo.parentId = fileObj["parentId"].toVariant().toUInt();
        fileInfo.ownerId = fileObj["ownerId"].toVariant().toUInt();
        fileInfo.filePath = fileObj["filePath"].toString();
        fileInfo.fileHash = fileObj["fileHash"].toString();
        fileInfo.createTime = fileObj["createTime"].toString();
        fileInfo.modifyTime = fileObj["modifyTime"].toString();
        fileInfo.isDir = fileObj["isDir"].toBool();

        fileList.append(fileInfo);
      }

      emit searchFileResponse(success, message, fileList);
    } else {
      emit searchFileResponse(success, message, QList<FileInfo>());
    }
    break;

    // 删除重复的 MSG_FILE_SHARE_RESP case，保留第一个

  case MSG_FILE_GET_SHARE_RESP:
    // 通过分享码获取文件响应
    LOG_INFO("Network",
             QString("收到通过分享码获取文件响应，成功: %1，消息: %2")
                 .arg(success)
                 .arg(message));
    if (success) {
      FileInfo fileInfo;
      fileInfo.fileId = jsonObj["fileId"].toVariant().toUInt();
      fileInfo.fileName = jsonObj["fileName"].toString();
      fileInfo.fileSize = jsonObj["fileSize"].toVariant().toUInt();
      fileInfo.fileType = jsonObj["fileType"].toString();
      fileInfo.filePath = jsonObj["filePath"].toString();
      fileInfo.fileHash = jsonObj["fileHash"].toString();

      emit getFileByShareCodeResponse(success, message, fileInfo);
    } else {
      emit getFileByShareCodeResponse(success, message, FileInfo());
    }
    break;

  case MSG_FRIEND_SEARCH_RESP:
    // 搜索用户响应
    LOG_INFO("Network", QString("收到搜索用户响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    if (success) {
      QList<UserInfo> userList;
      QJsonArray userArray = jsonObj["users"].toArray();

      for (int i = 0; i < userArray.size(); ++i) {
        QJsonObject userObj = userArray[i].toObject();
        UserInfo userInfo;
        userInfo.userId = userObj["userId"].toVariant().toUInt();
        userInfo.username = userObj["username"].toString();
        userInfo.email = userObj["email"].toString();

        userList.append(userInfo);
      }

      emit searchUserResponse(success, message, userList);
    } else {
      emit searchUserResponse(success, message, QList<UserInfo>());
    }
    break;

  case MSG_FRIEND_ADD_RESP:
    // 添加好友响应
    LOG_INFO("Network", QString("收到添加好友响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    emit addFriendResponse(success, message);
    break;

  case MSG_FRIEND_ADD_NOTIFY:
    // 添加好友通知
    LOG_INFO("Network", "收到添加好友通知");
    {
      UserInfo userInfo;
      userInfo.userId = jsonObj["userId"].toVariant().toUInt();
      userInfo.username = jsonObj["username"].toString();
      userInfo.email = jsonObj["email"].toString();

      emit addFriendNotify(userInfo);
    }
    break;

  case MSG_FRIEND_ADD_AGREE:
    // 同意添加好友响应
    LOG_INFO("Network", QString("收到同意添加好友响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    emit agreeAddFriendResponse(success, message);
    break;

  case MSG_FRIEND_ADD_REJECT:
    // 拒绝添加好友响应
    LOG_INFO("Network", QString("收到拒绝添加好友响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    emit rejectAddFriendResponse(success, message);
    break;

  case MSG_FRIEND_DELETE_RESP:
    // 删除好友响应
    LOG_INFO("Network", QString("收到删除好友响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    emit deleteFriendResponse(success, message);
    break;

  case MSG_FRIEND_LIST_RESP:
    // 好友列表响应
    LOG_INFO("Network", QString("收到好友列表响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    if (success) {
      QList<FriendInfo> friendList;
      QJsonArray friendArray = jsonObj["friends"].toArray();

      for (int i = 0; i < friendArray.size(); ++i) {
        QJsonObject friendObj = friendArray[i].toObject();
        FriendInfo friendInfo;
        friendInfo.friendId = friendObj["friendId"].toVariant().toUInt();
        friendInfo.username = friendObj["username"].toString();
        friendInfo.email = friendObj["email"].toString();
        friendInfo.isOnline = friendObj["isOnline"].toBool();
        friendInfo.addTime = friendObj["addTime"].toString();
        friendInfo.remark = friendObj["remark"].toString();

        friendList.append(friendInfo);
      }

      emit friendListResponse(success, message, friendList);
    } else {
      emit friendListResponse(success, message, QList<FriendInfo>());
    }
    break;

  case MSG_FRIEND_STATUS_NOTIFY:
    // 好友状态变更通知
    LOG_INFO("Network", "收到好友状态变更通知");
    {
      quint32 friendId = jsonObj["friendId"].toVariant().toUInt();
      bool online = jsonObj["online"].toBool();

      emit friendStatusNotify(friendId, online);
    }
    break;

  case MSG_MESSAGE_RECV:
    // 接收消息
    LOG_INFO("Network", "收到消息");
    {
      MessageInfo messageInfo;
      messageInfo.msgId = jsonObj["msgId"].toVariant().toUInt();
      messageInfo.senderId = jsonObj["senderId"].toVariant().toUInt();
      messageInfo.receiverId = jsonObj["receiverId"].toVariant().toUInt();
      messageInfo.content = jsonObj["content"].toString();
      messageInfo.sendTime = jsonObj["sendTime"].toString();
      messageInfo.isRead = jsonObj["isRead"].toBool();

      emit messageReceived(messageInfo);
    }
    break;

  case MSG_MESSAGE_HISTORY_RESP:
    // 历史消息响应
    LOG_INFO("Network", QString("收到历史消息响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    if (success) {
      QList<MessageInfo> messageList;
      QJsonArray messageArray = jsonObj["messages"].toArray();

      for (int i = 0; i < messageArray.size(); ++i) {
        QJsonObject messageObj = messageArray[i].toObject();
        MessageInfo messageInfo;
        messageInfo.msgId = messageObj["msgId"].toVariant().toUInt();
        messageInfo.senderId = messageObj["senderId"].toVariant().toUInt();
        messageInfo.receiverId = messageObj["receiverId"].toVariant().toUInt();
        messageInfo.content = messageObj["content"].toString();
        messageInfo.sendTime = messageObj["sendTime"].toString();
        messageInfo.isRead = messageObj["isRead"].toBool();

        messageList.append(messageInfo);
      }

      emit messageHistoryResponse(success, message, messageList);
    } else {
      emit messageHistoryResponse(success, message, QList<MessageInfo>());
    }
    break;

  case MSG_MESSAGE_OFFLINE_RESP:
    // 离线消息响应
    LOG_INFO("Network", QString("收到离线消息响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    if (success) {
      QList<MessageInfo> messageList;
      QJsonArray messageArray = jsonObj["messages"].toArray();

      for (int i = 0; i < messageArray.size(); ++i) {
        QJsonObject messageObj = messageArray[i].toObject();
        MessageInfo messageInfo;
        messageInfo.msgId = messageObj["msgId"].toVariant().toUInt();
        messageInfo.senderId = messageObj["senderId"].toVariant().toUInt();
        messageInfo.receiverId = messageObj["receiverId"].toVariant().toUInt();
        messageInfo.content = messageObj["content"].toString();
        messageInfo.sendTime = messageObj["sendTime"].toString();
        messageInfo.isRead = messageObj["isRead"].toBool();

        messageList.append(messageInfo);
      }

      emit offlineMessageResponse(success, message, messageList);
    } else {
      emit offlineMessageResponse(success, message, QList<MessageInfo>());
    }
    break;

  case MSG_CHANGE_PASSWORD_RESP:
    // 修改密码响应
    LOG_INFO("Network", QString("收到修改密码响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    emit changePasswordResponse(success, message);
    break;

  case MSG_UPDATE_USER_INFO_RESP:
    // 更新用户信息响应
    LOG_INFO("Network", QString("收到更新用户信息响应，成功: %1，消息: %2")
                            .arg(success)
                            .arg(message));
    if (success) {
      // 更新本地用户信息
      QJsonObject userObj = jsonObj["userInfo"].toObject();
      m_currentUser.username = userObj["username"].toString();
      m_currentUser.email = userObj["email"].toString();
    }
    emit updateUserInfoResponse(success, message);
    break;

  case MSG_ERROR:
    // 错误消息
    LOG_ERROR("Network", QString("收到错误消息: %1").arg(message));
    emit errorOccurred(message);
    break;

  default:
    // 未知消息类型
    LOG_WARN("Network", QString("收到未知类型的消息: %1").arg(msgType));
    break;
  }
}

// 计算密码哈希
QString NetworkManager::calculatePasswordHash(const QString &password,
                                              const QString &salt) {
  // 将密码和盐值拼接
  QString passwordWithSalt = password + salt;

  // 计算SHA-256哈希
  QByteArray hash = QCryptographicHash::hash(passwordWithSalt.toUtf8(),
                                             QCryptographicHash::Sha256);

  // 转换为十六进制字符串
  return hash.toHex();
}

// 生成随机盐值
QString NetworkManager::generateSalt() {
  // 生成随机盐值
  QByteArray salt;
  for (int i = 0; i < 8; ++i) {
    salt.append(static_cast<char>(QRandomGenerator::global()->bounded(256)));
  }

  // 转换为十六进制字符串
  return salt.toHex();
}

// 计算文件哈希
QString NetworkManager::calculateFileHash(const QString &filePath) {
  QFile file(filePath);
  if (!file.open(QIODevice::ReadOnly)) {
    LOG_ERROR("Network", QString("无法打开文件: %1").arg(filePath));
    return "";
  }

  // 计算SHA-256哈希
  QCryptographicHash hash(QCryptographicHash::Sha256);
  while (!file.atEnd()) {
    hash.addData(file.read(8192));
  }

  file.close();

  // 返回十六进制字符串
  return hash.result().toHex();
}

// 验证文件哈希
bool NetworkManager::verifyFileHash(const QString &filePath,
                                    const QString &expectedHash) {
  // 计算文件哈希
  QString actualHash = calculateFileHash(filePath);

  // 比较哈希值
  if (actualHash.isEmpty() || actualHash != expectedHash) {
    LOG_ERROR("Network",
              QString("文件哈希验证失败，文件: %1，期望: %2，实际: %3")
                  .arg(filePath)
                  .arg(expectedHash)
                  .arg(actualHash));
    return false;
  }

  LOG_INFO("Network", QString("文件哈希验证成功，文件: %1").arg(filePath));
  return true;
}

// 启动心跳定时器
void NetworkManager::startHeartbeatTimer() {
  if (m_heartbeatTimer) {
    int heartbeatInterval =
        ConfigManager::getInstance()->getHeartbeatInterval();
    m_heartbeatTimer->start(heartbeatInterval * 1000);
    LOG_INFO("Network",
             QString("启动心跳定时器，间隔: %1 秒").arg(heartbeatInterval));
  }
}

// 停止心跳定时器
void NetworkManager::stopHeartbeatTimer() {
  if (m_heartbeatTimer && m_heartbeatTimer->isActive()) {
    m_heartbeatTimer->stop();
    LOG_INFO("Network", "停止心跳定时器");
  }
}

// 发送心跳包
void NetworkManager::sendHeartbeat() {
  // 创建空JSON对象
  QJsonObject jsonObj;

  // 发送心跳包
  LOG_DEBUG("Network", "发送心跳包");
  sendJsonMessage(MSG_HEARTBEAT, jsonObj);
}

// 处理文件传输完成
void NetworkManager::handleFileTransferComplete(quint32 fileId) {
  // 更新传输状态
  emit fileTransferStatus(fileId, "传输完成");

  // 清理进度跟踪
  m_uploadProgress.remove(fileId);
  m_downloadProgress.remove(fileId);
  m_fileSizes.remove(fileId);
}

// 添加文件操作到队列
void NetworkManager::addFileOperation(FileOperationType type, quint32 fileId,
                                      const QString &filePath, quint32 parentId,
                                      const QString &newName,
                                      quint32 newParentId) {
  FileOperation operation;
  operation.type = type;
  operation.fileId = fileId;
  operation.filePath = filePath;
  operation.parentId = parentId;
  operation.newName = newName;
  operation.newParentId = newParentId;
  operation.completed = false;
  operation.errorMessage = "";

  m_fileOperationQueue.append(operation);

  // 如果当前没有正在处理操作，则开始处理队列
  if (!m_isProcessingFileOperation) {
    processFileOperationQueue();
  }
}

// 处理文件操作队列
void NetworkManager::processFileOperationQueue() {
  if (m_fileOperationQueue.isEmpty() || m_isProcessingFileOperation) {
    return;
  }

  m_isProcessingFileOperation = true;

  // 处理队列中的第一个操作
  FileOperation operation = m_fileOperationQueue.first();
  processFileOperation(operation);
}

// 处理单个文件操作
void NetworkManager::processFileOperation(const FileOperation &operation) {
  switch (operation.type) {
  case UploadOperation:
    uploadFileRequest(operation.filePath, operation.parentId);
    break;

  case DownloadOperation:
    downloadFileRequest(operation.fileId, 0);
    break;

  case DeleteOperation:
    deleteFileRequest(operation.fileId);
    break;

  case RenameOperation:
    renameFileRequest(operation.fileId, operation.newName);
    break;

  case MoveOperation:
    moveFileRequest(operation.fileId, operation.newParentId);
    break;
  }
}

// 完成文件操作
void NetworkManager::completeFileOperation(const FileOperation &operation,
                                           bool success,
                                           const QString &message) {
  // 找到操作在队列中的位置
  int index = m_fileOperationQueue.indexOf(operation);
  if (index >= 0) {
    // 标记操作为已完成
    FileOperation completedOp = m_fileOperationQueue[index];
    completedOp.completed = true;
    completedOp.errorMessage = message;
    m_fileOperationQueue[index] = completedOp;

    // 从队列中移除已完成的操作
    m_fileOperationQueue.removeAt(index);
  }

  // 如果还有未完成的操作，继续处理队列
  if (!m_fileOperationQueue.isEmpty()) {
    processFileOperationQueue();
  } else {
    m_isProcessingFileOperation = false;
  }
}

// 数据块准备好发送槽函数
void NetworkManager::onChunkReadyToSend(quint32 taskId, quint64 offset,
                                        const QByteArray &data) {
  LOG_INFO("Network",
           QString("数据块准备好发送，任务ID: %1，偏移量: %2，大小: %3")
               .arg(taskId)
               .arg(offset)
               .arg(data.size()));

  // 创建文件数据包
  QJsonObject jsonObj;
  jsonObj["taskId"] = static_cast<qint32>(taskId);
  jsonObj["offset"] = static_cast<qint64>(offset);
  jsonObj["data"] = QString(data.toBase64()); // 使用Base64编码数据

  // 发送文件数据包
  sendJsonMessage(MSG_FILE_DATA, jsonObj);
}

// 请求下一个数据块槽函数
void NetworkManager::onRequestNextChunk(quint32 taskId, quint64 offset,
                                        quint32 size) {
  LOG_INFO("Network",
           QString("请求下一个数据块，任务ID: %1，偏移量: %2，大小: %3")
               .arg(taskId)
               .arg(offset)
               .arg(size));

  // 创建请求包
  QJsonObject jsonObj;
  jsonObj["taskId"] = static_cast<qint32>(taskId);
  jsonObj["offset"] = static_cast<qint64>(offset);
  jsonObj["size"] = static_cast<qint32>(size);

  // 发送请求
  sendJsonMessage(MSG_FILE_DOWNLOAD_REQ, jsonObj);
}

// 任务重试槽函数
void NetworkManager::onTaskRetried(quint32 taskId) {
  LOG_INFO("Network", QString("任务重试，任务ID: %1").arg(taskId));

  // 如果任务已连接，可以在这里重新开始传输
  // 具体实现取决于业务逻辑
}

// 请求重试方法
void NetworkManager::retryRequest(quint32 msgId, const QJsonObject &requestData,
                                  MessageType msgType) {
  // 检查重试次数
  int retryCount = m_requestRetries.value(msgId, 0);
  if (retryCount >= m_maxRetries) {
    LOG_ERROR("Network",
              QString("请求重试次数已达上限，消息ID: %1").arg(msgId));
    m_requestRetries.remove(msgId);
    return;
  }

  // 增加重试次数
  m_requestRetries[msgId] = retryCount + 1;

  LOG_INFO("Network", QString("重试请求，消息ID: %1，第 %2/%3 次")
                          .arg(msgId)
                          .arg(retryCount + 1)
                          .arg(m_maxRetries));

  // 重新发送请求
  sendJsonMessage(msgType, requestData);
}

// 处理文件数据
void NetworkManager::handleFileData(const QByteArray &data) {
  LOG_INFO("Network", QString("处理文件数据，大小: %1 字节").arg(data.size()));

  // 解析JSON数据
  QJsonDocument doc = QJsonDocument::fromJson(data);
  if (doc.isNull() || !doc.isObject()) {
    LOG_ERROR("Network", "解析文件数据失败，无效的JSON数据");
    return;
  }

  QJsonObject jsonObj = doc.object();

  // 检查必要字段
  if (!jsonObj.contains("taskId") || !jsonObj.contains("offset") ||
      !jsonObj.contains("data")) {
    LOG_ERROR("Network", "文件数据缺少必要字段");
    return;
  }

  // 提取数据
  quint32 taskId = jsonObj["taskId"].toVariant().toUInt();
  quint64 offset = jsonObj["offset"].toVariant().toULongLong();
  QByteArray fileData =
      QByteArray::fromBase64(jsonObj["data"].toString().toUtf8());

  // 发送信号通知接收到文件数据块
  emit handleFileChunkReceived(taskId, fileData);
}

// 处理文件上传完成
void NetworkManager::handleCompleteFileUpload(const QJsonObject &jsonObj) {
  LOG_INFO("Network", "处理文件上传完成");

  // 检查必要字段
  if (!jsonObj.contains("taskId") || !jsonObj.contains("fileId") ||
      !jsonObj.contains("fileHash")) {
    LOG_ERROR("Network", "文件上传完成数据缺少必要字段");
    return;
  }

  // 提取数据
  quint32 taskId = jsonObj["taskId"].toVariant().toUInt();
  quint32 fileId = jsonObj["fileId"].toVariant().toUInt();
  QString fileHash = jsonObj["fileHash"].toString();

  // 发送信号通知文件上传完成
  emit handleFileChunkSent(taskId, true);

  LOG_INFO("Network",
           QString("文件上传完成，任务ID: %1，文件ID: %2，文件哈希: %3")
               .arg(taskId)
               .arg(fileId)
               .arg(fileHash));
}

// 处理文件下载响应
void NetworkManager::handleDownloadFileResponse(const QJsonObject &jsonObj) {
  LOG_INFO("Network", "处理文件下载响应");

  // 检查必要字段
  if (!jsonObj.contains("success") || !jsonObj.contains("taskId")) {
    LOG_ERROR("Network", "文件下载响应缺少必要字段");
    return;
  }

  // 提取数据
  bool success = jsonObj["success"].toBool();
  quint32 taskId = jsonObj["taskId"].toVariant().toUInt();

  if (success) {
    // 下载成功
    emit handleFileChunkSent(taskId, true);
    LOG_INFO("Network", QString("文件下载成功，任务ID: %1").arg(taskId));
  } else {
    // 下载失败
    QString message = jsonObj["message"].toString("下载失败");
    emit handleFileChunkError(taskId, message);
    LOG_ERROR(
        "Network",
        QString("文件下载失败，任务ID: %1，错误: %2").arg(taskId).arg(message));
  }
}

// 取消文件操作
void NetworkManager::cancelFileOperation(quint32 fileId) {
  // 查找文件操作
  for (int i = 0; i < m_fileOperationQueue.size(); ++i) {
    FileOperation &operation = m_fileOperationQueue[i];
    if (operation.fileId == fileId && !operation.completed) {
      // 标记操作为已取消
      operation.completed = true;
      operation.errorMessage = "用户取消操作";

      // 从队列中移除已取消的操作
      m_fileOperationQueue.removeAt(i);

      // 如果还有未完成的操作，继续处理队列
      if (!m_fileOperationQueue.isEmpty()) {
        processFileOperationQueue();
      } else {
        m_isProcessingFileOperation = false;
      }

      return;
    }
  }
}

// 获取文件操作进度
quint64 NetworkManager::getFileOperationProgress(quint32 fileId) const {
  // 查找文件操作
  for (const FileOperation &operation : m_fileOperationQueue) {
    if (operation.fileId == fileId && !operation.completed) {
      return operation.progress;
    }
  }
  return 0;
}

// 获取文件操作状态
FileOperationType NetworkManager::getFileOperationType(quint32 fileId) const {
  // 查找文件操作
  for (const FileOperation &operation : m_fileOperationQueue) {
    if (operation.fileId == fileId && !operation.completed) {
      return operation.type;
    }
  }
  return UploadOperation; // 默认返回上传操作
}

// 获取文件操作队列大小
int NetworkManager::getFileOperationQueueSize() const {
  return m_fileOperationQueue.size();
}

// 处理接收到的文件块
void NetworkManager::handleFileChunkReceived(quint32 fileId,
                                             const QByteArray &data) {
  Q_UNUSED(fileId)
  Q_UNUSED(data)
  // TODO: 实现文件块接收处理逻辑
  LOG_INFO(
      "Network",
      QString("收到文件块，文件ID: %1，大小: %2").arg(fileId).arg(data.size()));
}

// 处理文件块发送结果
void NetworkManager::handleFileChunkSent(quint32 fileId, bool success) {
  Q_UNUSED(fileId)
  Q_UNUSED(success)
  // TODO: 实现文件块发送结果处理逻辑
  LOG_INFO(
      "Network",
      QString("文件块发送结果，文件ID: %1，成功: %2").arg(fileId).arg(success));
}

// 处理文件块错误
void NetworkManager::handleFileChunkError(quint32 fileId,
                                          const QString &error) {
  Q_UNUSED(fileId)
  Q_UNUSED(error)
  // TODO: 实现文件块错误处理逻辑
  LOG_ERROR("Network",
            QString("文件块错误，文件ID: %1，错误: %2").arg(fileId).arg(error));
}

// 重试文件传输
void NetworkManager::retryFileTransfer(quint32 fileId) {
  Q_UNUSED(fileId)
  // TODO: 实现文件传输重试逻辑
  LOG_INFO("Network", QString("重试文件传输，文件ID: %1").arg(fileId));
}

// 请求下一个文件块
void NetworkManager::requestNextFileChunk(quint32 fileId, quint64 offset,
                                          quint32 chunkSize) {
  Q_UNUSED(fileId)
  Q_UNUSED(offset)
  Q_UNUSED(chunkSize)
  // TODO: 实现请求下一个文件块逻辑
  LOG_INFO("Network",
           QString("请求下一个文件块，文件ID: %1，偏移: %2，大小: %3")
               .arg(fileId)
               .arg(offset)
               .arg(chunkSize));
}

// 发送文件块
void NetworkManager::sendFileChunk(quint32 fileId, quint64 offset,
                                   const QByteArray &data) {
  Q_UNUSED(fileId)
  Q_UNUSED(offset)
  Q_UNUSED(data)
  // TODO: 实现发送文件块逻辑
  LOG_INFO("Network", QString("发送文件块，文件ID: %1，偏移: %2，大小: %3")
                          .arg(fileId)
                          .arg(offset)
                          .arg(data.size()));
}
