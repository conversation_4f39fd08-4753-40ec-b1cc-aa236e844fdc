#############################################################################
# Makefile for building: Client
# Generated by qmake (3.1) (Qt 6.9.2)
# Project:  ../../Client.pro
# Template: app
# Command: F:/QT/QT6.9.2/6.9.2/mingw_64/bin/qmake.exe -o Makefile ../../Client.pro -spec win32-g++ CONFIG+=debug CONFIG+=qml_debug
#############################################################################

MAKEFILE      = Makefile

EQ            = =

first: debug
install: debug-install
uninstall: debug-uninstall
QMAKE         = F:/QT/QT6.9.2/6.9.2/mingw_64/bin/qmake.exe
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = cp -f
INSTALL_PROGRAM = cp -f
INSTALL_DIR   = cp -f -R
QINSTALL      = F:/QT/QT6.9.2/6.9.2/mingw_64/bin/qmake.exe -install qinstall
QINSTALL_PROGRAM = F:/QT/QT6.9.2/6.9.2/mingw_64/bin/qmake.exe -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
IDC           = idc
IDL           = midl
ZIP           = 
DEF_FILE      = 
RES_FILE      = 
SED           = sed
MOVE          = mv -f
SUBTARGETS    =  \
		debug \
		release


debug: FORCE
	$(MAKE) -f $(MAKEFILE).Debug
debug-make_first: FORCE
	$(MAKE) -f $(MAKEFILE).Debug 
debug-all: FORCE
	$(MAKE) -f $(MAKEFILE).Debug all
debug-clean: FORCE
	$(MAKE) -f $(MAKEFILE).Debug clean
debug-distclean: FORCE
	$(MAKE) -f $(MAKEFILE).Debug distclean
debug-install: FORCE
	$(MAKE) -f $(MAKEFILE).Debug install
debug-uninstall: FORCE
	$(MAKE) -f $(MAKEFILE).Debug uninstall
release: FORCE
	$(MAKE) -f $(MAKEFILE).Release
release-make_first: FORCE
	$(MAKE) -f $(MAKEFILE).Release 
release-all: FORCE
	$(MAKE) -f $(MAKEFILE).Release all
release-clean: FORCE
	$(MAKE) -f $(MAKEFILE).Release clean
release-distclean: FORCE
	$(MAKE) -f $(MAKEFILE).Release distclean
release-install: FORCE
	$(MAKE) -f $(MAKEFILE).Release install
release-uninstall: FORCE
	$(MAKE) -f $(MAKEFILE).Release uninstall

Makefile: ../../Client.pro F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/win32-g++/qmake.conf F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/spec_pre.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/device_config.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/sanitize.conf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/gcc-base.conf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/g++-base.conf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/win32/windows_vulkan_sdk.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/windows-vulkan.conf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/g++-win32.conf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/windows-desktop.conf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/qconfig.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_ext_freetype.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_ext_libjpeg.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_ext_libpng.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_ext_openxr_loader.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_charts.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_charts_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_chartsqml.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_chartsqml_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_concurrent.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_core.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_core_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_dbus.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_dbus_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_designer.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_designer_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_entrypoint_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_example_icons_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_examples_asset_downloader_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_ffmpegmediapluginimpl_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_freetype_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_gui.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_gui_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_harfbuzz_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_help.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_help_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_jpeg_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsanimation.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsanimation_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsfolderlistmodel.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsfolderlistmodel_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsplatform.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsplatform_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsqmlmodels.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsqmlmodels_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labssettings.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labssettings_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labssharedimage.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labssharedimage_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labswavefrontmesh.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labswavefrontmesh_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_linguist.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimedia.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimediaquick_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimediatestlibprivate_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_network.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_network_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_networkauth.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_networkauth_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_opengl.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_opengl_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_openglwidgets.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_openglwidgets_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_png_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_printsupport.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qdoccatch_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qdoccatchconversions_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qdoccatchgenerators_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qml.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qml_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlassetdownloader.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlassetdownloader_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlcompiler.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlcompiler_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlcore.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlcore_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmldom_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlformat_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlintegration.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlintegration_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmllocalstorage.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmllocalstorage_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlls_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlmeta.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlmeta_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlmodels.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlmodels_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlnetwork.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlnetwork_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmltest.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmltoolingsettings_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmltyperegistrar_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlworkerscript.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlworkerscript_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlxmllistmodel.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlxmllistmodel_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3d.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3d_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dassetimport.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dassetimport_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dassetutils.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dassetutils_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3deffects.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3deffects_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dglslparser_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dhelpers.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dhelpers_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dhelpersimpl.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dhelpersimpl_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3diblbaker.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3diblbaker_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dparticleeffects.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dparticleeffects_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dparticles.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dparticles_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3druntimerender.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3druntimerender_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dspatialaudio_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dutils.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dutils_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dxr.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dxr_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basic.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basic_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusion.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusion_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imagine.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imagine_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2impl.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2impl_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2material.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2material_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universal.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universal_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2windowsstyleimpl.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2windowsstyleimpl_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrolstestutilsprivate_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2quickimpl.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2quickimpl_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2utils.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2utils_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickeffects.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickeffects_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicklayouts.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicklayouts_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktestutilsprivate_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktimeline.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktimeline_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktimelineblendtrees.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktimelineblendtrees_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickvectorimage.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickvectorimage_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickvectorimagegenerator_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_shadertools.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_shadertools_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_spatialaudio.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_spatialaudio_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_sql.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_sql_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_svg.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_svg_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_svgwidgets.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_svgwidgets_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_testinternals_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_testlib.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_testlib_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_tools_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_uiplugin.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_uitools.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_uitools_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_widgets.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_widgets_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_xml.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_xml_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_zlib_private.pri \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qt_functions.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qt_config.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/win32-g++/qmake.conf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/spec_post.prf \
		.qmake.stash \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/exclusive_builds.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/toolchain.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/default_pre.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/win32/default_pre.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/resolve_config.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/exclusive_builds_post.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/default_post.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qml_debug.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/precompile_header.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/warn_on.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/permissions.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qt.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/resources_functions.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/resources.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/moc.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/win32/opengl.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/uic.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qmake_use.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/file_copies.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/win32/windows.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/testcase_targets.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/exceptions.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/yacc.prf \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/lex.prf \
		../../Client.pro \
		F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Widgets.prl \
		F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Gui.prl \
		F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Network.prl \
		F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Sql.prl \
		F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Core.prl \
		F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6EntryPoint.prl \
		F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/build_pass.prf
	$(QMAKE) -o Makefile ../../Client.pro -spec win32-g++ CONFIG+=debug CONFIG+=qml_debug
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/spec_pre.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/device_config.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/sanitize.conf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/gcc-base.conf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/g++-base.conf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/win32/windows_vulkan_sdk.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/windows-vulkan.conf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/g++-win32.conf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/common/windows-desktop.conf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/qconfig.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_ext_freetype.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_ext_libjpeg.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_ext_libpng.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_ext_openxr_loader.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_charts.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_charts_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_chartsqml.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_chartsqml_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_concurrent.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_concurrent_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_core.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_core_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_dbus.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_dbus_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_designer.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_designer_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_designercomponents_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_entrypoint_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_example_icons_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_examples_asset_downloader_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_fb_support_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_ffmpegmediapluginimpl_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_freetype_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_gui.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_gui_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_harfbuzz_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_help.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_help_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_jpeg_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsanimation.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsanimation_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsfolderlistmodel.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsfolderlistmodel_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsplatform.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsplatform_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsqmlmodels.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labsqmlmodels_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labssettings.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labssettings_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labssharedimage.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labssharedimage_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labswavefrontmesh.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_labswavefrontmesh_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_linguist.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimedia.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimedia_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimediaquick_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimediatestlibprivate_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimediawidgets.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_network.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_network_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_networkauth.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_networkauth_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_opengl.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_opengl_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_openglwidgets.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_openglwidgets_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_packetprotocol_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_png_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_printsupport.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_printsupport_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qdoccatch_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qdoccatchconversions_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qdoccatchgenerators_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qml.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qml_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlassetdownloader.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlassetdownloader_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlcompiler.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlcompiler_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlcore.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlcore_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmldebug_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmldom_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlformat_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlintegration.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlintegration_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmllocalstorage.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmllocalstorage_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlls_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlmeta.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlmeta_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlmodels.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlmodels_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlnetwork.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlnetwork_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmltest.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmltest_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmltoolingsettings_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmltyperegistrar_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlworkerscript.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlworkerscript_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlxmllistmodel.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_qmlxmllistmodel_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3d.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3d_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dassetimport.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dassetimport_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dassetutils.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dassetutils_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3deffects.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3deffects_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dglslparser_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dhelpers.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dhelpers_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dhelpersimpl.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dhelpersimpl_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3diblbaker.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3diblbaker_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dparticleeffects.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dparticleeffects_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dparticles.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dparticles_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3druntimerender.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3druntimerender_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dspatialaudio_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dutils.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dutils_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dxr.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick3dxr_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quick_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basic.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basic_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusion.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusion_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imagine.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imagine_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2impl.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2impl_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2material.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2material_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universal.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universal_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2windowsstyleimpl.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrols2windowsstyleimpl_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickcontrolstestutilsprivate_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2quickimpl.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2quickimpl_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2utils.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickdialogs2utils_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickeffects.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickeffects_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicklayouts.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicklayouts_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickparticles_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickshapes_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktemplates2.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktestutilsprivate_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktimeline.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktimeline_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktimelineblendtrees.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quicktimelineblendtrees_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickvectorimage.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickvectorimage_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickvectorimagegenerator_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickwidgets.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_quickwidgets_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_shadertools.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_shadertools_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_spatialaudio.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_spatialaudio_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_sql.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_sql_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_svg.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_svg_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_svgwidgets.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_svgwidgets_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_testinternals_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_testlib.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_testlib_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_tools_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_uiplugin.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_uitools.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_uitools_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_widgets.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_widgets_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_xml.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_xml_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/modules/qt_lib_zlib_private.pri:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qt_functions.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qt_config.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/win32-g++/qmake.conf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/spec_post.prf:
.qmake.stash:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/exclusive_builds.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/toolchain.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/default_pre.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/win32/default_pre.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/resolve_config.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/exclusive_builds_post.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/default_post.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qml_debug.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/precompile_header.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/warn_on.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/permissions.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qt.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/resources_functions.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/resources.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/moc.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/win32/opengl.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/uic.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/qmake_use.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/file_copies.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/win32/windows.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/testcase_targets.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/exceptions.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/yacc.prf:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/lex.prf:
../../Client.pro:
F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Widgets.prl:
F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Gui.prl:
F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Network.prl:
F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Sql.prl:
F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6Core.prl:
F:/QT/QT6.9.2/6.9.2/mingw_64/lib/Qt6EntryPoint.prl:
F:/QT/QT6.9.2/6.9.2/mingw_64/mkspecs/features/build_pass.prf:
qmake: FORCE
	@$(QMAKE) -o Makefile ../../Client.pro -spec win32-g++ CONFIG+=debug CONFIG+=qml_debug

qmake_all: FORCE

make_first: debug-make_first release-make_first  FORCE
all: debug-all release-all  FORCE
clean: debug-clean release-clean  FORCE
distclean: debug-distclean release-distclean  FORCE
	-$(DEL_FILE) Makefile
	-$(DEL_FILE) .qmake.stash

debug-mocclean:
	$(MAKE) -f $(MAKEFILE).Debug mocclean
release-mocclean:
	$(MAKE) -f $(MAKEFILE).Release mocclean
mocclean: debug-mocclean release-mocclean

debug-mocables:
	$(MAKE) -f $(MAKEFILE).Debug mocables
release-mocables:
	$(MAKE) -f $(MAKEFILE).Release mocables
mocables: debug-mocables release-mocables

check: first

benchmark: first
FORCE:

.SUFFIXES:

$(MAKEFILE).Debug: Makefile
$(MAKEFILE).Release: Makefile
