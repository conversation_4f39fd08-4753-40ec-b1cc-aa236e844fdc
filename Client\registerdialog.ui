<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RegisterDialog</class>
 <widget class="QDialog" name="RegisterDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>350</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>用户注册</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icons/register.png</normaloff>:/icons/register.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QFormLayout" name="formLayout">
     <property name="horizontalSpacing">
      <number>20</number>
     </property>
     <property name="verticalSpacing">
      <number>15</number>
     </property>
     <item row="0" column="0">
      <widget class="QLabel" name="label_username">
       <property name="text">
        <string>用户名：</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLineEdit" name="lineEdit_username">
       <property name="placeholderText">
        <string>请输入用户名（3-20个字符）</string>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="label_email">
       <property name="text">
        <string>邮箱：</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QLineEdit" name="lineEdit_email">
       <property name="placeholderText">
        <string>请输入邮箱地址</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="label_password">
       <property name="text">
        <string>密码：</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QLineEdit" name="lineEdit_password">
       <property name="echoMode">
        <enum>QLineEdit::Password</enum>
       </property>
       <property name="placeholderText">
        <string>请输入密码（至少6位）</string>
       </property>
      </widget>
     </item>
     <item row="3" column="0">
      <widget class="QLabel" name="label_confirm_password">
       <property name="text">
        <string>确认密码：</string>
       </property>
      </widget>
     </item>
     <item row="3" column="1">
      <widget class="QLineEdit" name="lineEdit_confirm_password">
       <property name="echoMode">
        <enum>QLineEdit::Password</enum>
       </property>
       <property name="placeholderText">
        <string>请再次输入密码</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>RegisterDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>RegisterDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
