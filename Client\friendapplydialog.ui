<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FriendApplyDialog</class>
 <widget class="QDialog" name="FriendApplyDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>500</width>
    <height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>好友申请</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icons/friend_apply.png</normaloff>:/icons/friend_apply.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTableWidget" name="tableWidget_applies">
     <property name="selectionMode">
      <enum>QAbstractItemView::SingleSelection</enum>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectRows</enum>
     </property>
     <column>
      <property name="text">
       <string>申请人</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>申请时间</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>申请信息</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>操作</string>
      </property>
     </column>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_buttons">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_agree">
       <property name="text">
        <string>同意</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/agree.png</normaloff>:/icons/agree.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_reject">
       <property name="text">
        <string>拒绝</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/reject.png</normaloff>:/icons/reject.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_close">
       <property name="text">
        <string>关闭</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/close.png</normaloff>:/icons/close.png</iconset>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
