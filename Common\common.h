#ifndef COMMON_H
#define COMMON_H

// 公共头文件，包含所有模块共享的定义和常量

#include <QByteArray>
#include <QDataStream>
#include <QIODevice>
#include <QJsonDocument>
#include <QJsonObject>
#include <QRandomGenerator>
#include <QString>
#include <QtGlobal>

// CRC32校验和计算
class CRC32 {
public:
  static quint32 calculate(const QByteArray &data) {
    static const quint32 crc32_table[256] = {
        0x00000000, 0x77073096, 0xee0e612c, 0x990951ba, 0x076dc419, 0x706af48f,
        0xe963a535, 0x9e6495a3, 0x0edb8832, 0x79dcb8a4, 0xe0d5e91e, 0x97d2d988,
        0x09b64c2b, 0x7eb17cbd, 0xe7b82d07, 0x90bf1d91, 0x1db71064, 0x6ab020f2,
        0xf3b97148, 0x84be41de, 0x1adad47d, 0x6ddde4eb, 0xf4d4b551, 0x83d385c7,
        0x136c9856, 0x646ba8c0, 0xfd62f97a, 0x8a65c9ec, 0x14015c4f, 0x63066cd9,
        0xfa0f3d63, 0x8d080df5, 0x3b6e20c8, 0x4c69105e, 0xd56041e4, 0xa2677172,
        0x3c03e4d1, 0x4b04d447, 0xd20d85fd, 0xa50ab56b, 0x35b5a8fa, 0x42b2986c,
        0xdbbbc9d6, 0xacbcf940, 0x32d86ce3, 0x45df5c75, 0xdcd60dcf, 0xabd13d59,
        0x26d930ac, 0x51de003a, 0xc8d75180, 0xbfd06116, 0x21b4f4b5, 0x56b3c423,
        0xcfba9599, 0xb8bda50f, 0x2802b89e, 0x5f058808, 0xc60cd9b2, 0xb10be924,
        0x2f6f7c87, 0x58684c11, 0xc1611dab, 0xb6662d3d, 0x76dc4190, 0x01db7106,
        0x98d220bc, 0xefd5102a, 0x71b18589, 0x06b6b51f, 0x9fbfe4a5, 0xe8b8d433,
        0x7807c9a2, 0x0f00f934, 0x9609a88e, 0xe10e9818, 0x7f6a0dbb, 0x086d3d2d,
        0x91646c97, 0xe6635c01, 0x6b6b51f4, 0x1c6c6162, 0x856530d8, 0xf262004e,
        0x6c0695ed, 0x1b01a57b, 0x8208f4c1, 0xf50fc457, 0x65b0d9c6, 0x12b7e950,
        0x8bbeb8ea, 0xfcb9887c, 0x62dd1ddf, 0x15da2d49, 0x8cd37cf3, 0xfbd44c65,
        0x4db26158, 0x3ab551ce, 0xa3bc0074, 0xd4bb30e2, 0x4adfa541, 0x3dd895d7,
        0xa4d1c46d, 0xd3d6f4fb, 0x4369e96a, 0x346ed9fc, 0xad678846, 0xda60b8d0,
        0x44042d73, 0x33031de5, 0xaa0a4c5f, 0xdd0d7cc9, 0x5005713c, 0x270241aa,
        0xbe0b1010, 0xc90c2086, 0x5768b525, 0x206f85b3, 0xb966d409, 0xce61e49f,
        0x5edef90e, 0x29d9c998, 0xb0d09822, 0xc7d7a8b4, 0x59b33d17, 0x2eb40d81,
        0xb7bd5c3b, 0xc0ba6cad, 0xedb88320, 0x9abfb3b6, 0x03b6e20c, 0x74b1d29a,
        0xead54739, 0x9dd277af, 0x04db2615, 0x73dc1683, 0xe3630b12, 0x94643b84,
        0x0d6d6a3e, 0x7a6a5aa8, 0xe40ecf0b, 0x9309ff9d, 0x0a00ae27, 0x7d079eb1,
        0xf00f9344, 0x8708a3d2, 0x1e01f268, 0x6906c2fe, 0xf762575d, 0x806567cb,
        0x196c3671, 0x6e6b06e7, 0xfed41b76, 0x89d32be0, 0x10da7a5a, 0x67dd4acc,
        0xf9b9df6f, 0x8ebeeff9, 0x17b7be43, 0x60b08ed5, 0xd6d6a3e8, 0xa1d1937e,
        0x38d8c2c4, 0x4fdff252, 0xd1bb67f1, 0xa6bc5767, 0x3fb506dd, 0x48b2364b,
        0xd80d2bda, 0xaf0a1b4c, 0x36034af6, 0x41047a60, 0xdf60efc3, 0xa867df55,
        0x316e8eef, 0x4669be79, 0xcb61b38c, 0xbc66831a, 0x256fd2a0, 0x5268e236,
        0xcc0c7795, 0xbb0b4703, 0x220216b9, 0x5505262f, 0xc5ba3bbe, 0xb2bd0b28,
        0x2bb45a92, 0x5cb36a04, 0xc2d7ffa7, 0xb5d0cf31, 0x2cd99e8b, 0x5bdeae1d,
        0x9b64c2b0, 0xec63f226, 0x756aa39c, 0x026d930a, 0x9c0906a9, 0xeb0e363f,
        0x72076785, 0x05005713, 0x95bf4a82, 0xe2b87a14, 0x7bb12bae, 0x0cb61b38,
        0x92d28e9b, 0xe5d5be0d, 0x7cdcefb7, 0x0bdbdf21, 0x86d3d2d4, 0xf1d4e242,
        0x68ddb3f8, 0x1fda836e, 0x81be16cd, 0xf6b9265b, 0x6fb077e1, 0x18b74777,
        0x88085ae6, 0xff0f6a70, 0x66063bca, 0x11010b5c, 0x8f659eff, 0xf862ae69,
        0x616bffd3, 0x166ccf45, 0xa00ae278, 0xd70dd2ee, 0x4e048354, 0x3903b3c2,
        0xa7672661, 0xd06016f7, 0x4969474d, 0x3e6e77db, 0xaed16a4a, 0xd9d65adc,
        0x40df0b66, 0x37d83bf0, 0xa9bcae53, 0xdebb9ec5, 0x47b2cf7f, 0x30b5ffe9,
        0xbdbdf21c, 0xcabac28a, 0x53b39330, 0x24b4a3a6, 0xbad03605, 0xcdd70693,
        0x54de5729, 0x23d967bf, 0xb3667a2e, 0xc4614ab8, 0x5d681b02, 0x2a6f2b94,
        0xb40bbe37, 0xc30c8ea1, 0x5a05df1b, 0x2d02ef8d};

    quint32 crc = 0xFFFFFFFF;
    for (int i = 0; i < data.size(); ++i) {
      crc =
          crc32_table[(crc ^ static_cast<quint8>(data[i])) & 0xFF] ^ (crc >> 8);
    }
    return crc ^ 0xFFFFFFFF;
  }
};

// 消息类型枚举
enum MessageType {
  // 注册和登录相关
  MSG_REGISTER = 1001, // 注册请求
  MSG_REGISTER_RESP,   // 注册响应
  MSG_LOGIN,           // 登录请求
  MSG_LOGIN_RESP,      // 登录响应
  MSG_LOGOUT,          // 登出请求
  MSG_LOGOUT_RESP,     // 登出响应

  MSG_HEARTBEAT, // 心跳包

  // 文件操作相关
  MSG_FILE_LIST_REQ,        // 获取文件列表请求
  MSG_FILE_LIST_RESP,       // 获取文件列表响应
  MSG_FILE_UPLOAD_REQ,      // 文件上传请求
  MSG_FILE_UPLOAD_RESP,     // 文件上传响应
  MSG_FILE_DATA,            // 文件数据
  MSG_FILE_UPLOAD_COMPLETE, // 文件上传完成
  MSG_FILE_DOWNLOAD_REQ,    // 文件下载请求
  MSG_FILE_DOWNLOAD_RESP,   // 文件下载响应
  MSG_FILE_CREATE_DIR_REQ,  // 创建目录请求
  MSG_FILE_CREATE_DIR_RESP, // 创建目录响应
  MSG_FILE_DELETE_REQ,      // 删除文件请求
  MSG_FILE_DELETE_RESP,     // 删除文件响应
  MSG_FILE_RENAME_REQ,      // 重命名文件请求
  MSG_FILE_RENAME_RESP,     // 重命名文件响应
  MSG_FILE_MOVE_REQ,        // 移动文件请求
  MSG_FILE_MOVE_RESP,       // 移动文件响应
  MSG_FILE_COPY_REQ,        // 复制文件请求
  MSG_FILE_COPY_RESP,       // 复制文件响应
  MSG_FILE_SEARCH_REQ,      // 搜索文件请求
  MSG_FILE_SEARCH_RESP,     // 搜索文件响应
  MSG_FILE_SHARE_REQ,       // 分享文件请求
  MSG_FILE_SHARE_RESP,      // 分享文件响应
  MSG_FILE_GET_SHARE_REQ,   // 通过分享码获取文件请求
  MSG_FILE_GET_SHARE_RESP,  // 通过分享码获取文件响应

  // 好友相关
  MSG_FRIEND_SEARCH_REQ,    // 搜索用户请求
  MSG_FRIEND_SEARCH_RESP,   // 搜索用户响应
  MSG_FRIEND_ADD_REQ,       // 添加好友请求
  MSG_FRIEND_ADD_RESP,      // 添加好友响应
  MSG_FRIEND_ADD_NOTIFY,    // 添加好友通知
  MSG_FRIEND_ADD_AGREE,     // 同意添加好友
  MSG_FRIEND_ADD_REJECT,    // 拒绝添加好友
  MSG_FRIEND_DELETE_REQ,    // 删除好友请求
  MSG_FRIEND_DELETE_RESP,   // 删除好友响应
  MSG_FRIEND_LIST_REQ,      // 获取好友列表请求
  MSG_FRIEND_LIST_RESP,     // 获取好友列表响应
  MSG_FRIEND_STATUS_NOTIFY, // 好友状态变更通知

  // 消息相关
  MSG_MESSAGE_SEND,         // 发送消息
  MSG_MESSAGE_RECV,         // 接收消息
  MSG_MESSAGE_HISTORY_REQ,  // 获取历史消息请求
  MSG_MESSAGE_HISTORY_RESP, // 获取历史消息响应
  MSG_MESSAGE_OFFLINE_REQ,  // 获取离线消息请求
  MSG_MESSAGE_OFFLINE_RESP, // 获取离线消息响应

  // 错误消息
  MSG_ERROR // 错误消息
};

// 消息结构体
struct Packet {
  quint32 msgType;    // 消息类型
  quint32 msgLength;  // 消息体长度
  QByteArray msgData; // 消息体数据
  quint32 checksum;   // 校验和

  Packet() : msgType(0), msgLength(0), checksum(0) {}

  // 序列化
  QByteArray toByteArray() const {
    QByteArray block;
    QDataStream out(&block, QIODevice::WriteOnly);

    // 设置数据流版本，确保跨平台兼容性
    out.setVersion(QDataStream::Qt_6_9);

    // 写入消息头
    out << msgType;
    out << msgLength;

    // 写入消息体
    if (msgLength > 0) {
      out.writeRawData(msgData.constData(), msgLength);
    }

    // 写入校验和
    out << checksum;

    return block;
  }

  // 计算校验和
  quint32 calculateChecksum() const {
    // 使用CRC32算法计算校验和
    return CRC32::calculate(msgData);
  }
};

// 用户信息结构
struct UserInfo {
  quint32 userId;        // 用户ID
  QString username;      // 用户名
  QString email;         // 邮箱
  QString passwordHash;  // 密码哈希
  quint64 storageUsed;   // 已用存储空间
  quint64 storageTotal;  // 总存储空间
  QString registerTime;  // 注册时间
  QString lastLoginTime; // 最后登录时间

  UserInfo() : userId(0), storageUsed(0), storageTotal(0) {}
};

// 文件信息结构
struct FileInfo {
  quint32 fileId;     // 文件ID
  QString fileName;   // 文件名
  quint32 fileSize;   // 文件大小
  QString fileType;   // 文件类型
  quint32 parentId;   // 父目录ID
  quint32 ownerId;    // 所有者ID
  QString filePath;   // 文件路径
  QString fileHash;   // 文件哈希值
  QString createTime; // 创建时间
  QString modifyTime; // 修改时间
  bool isDir;         // 是否为目录

  FileInfo() : fileId(0), fileSize(0), parentId(0), ownerId(0), isDir(false) {}
};

// 好友信息结构
struct FriendInfo {
  quint32 friendId; // 好友ID
  QString username; // 好友用户名
  QString email;    // 好友邮箱
  bool isOnline;    // 是否在线
  QString addTime;  // 添加时间
  QString remark;   // 备注

  FriendInfo() : friendId(0), isOnline(false) {}
};

// 消息信息结构
struct MessageInfo {
  quint32 msgId;      // 消息ID
  quint32 senderId;   // 发送者ID
  quint32 receiverId; // 接收者ID
  QString content;    // 消息内容
  QString sendTime;   // 发送时间
  bool isRead;        // 是否已读

  MessageInfo() : msgId(0), senderId(0), receiverId(0), isRead(false) {}
};

// 好友申请状态枚举
enum FriendApplyStatus {
  APPLY_PENDING = 0,  // 待处理
  APPLY_ACCEPTED = 1, // 已同意
  APPLY_REJECTED = 2  // 已拒绝
};

// 好友申请信息结构
struct FriendApplyInfo {
  quint32 applyId;          // 申请ID
  quint32 applicantId;      // 申请人ID
  QString applicantName;    // 申请人用户名
  QString applicantAvatar;  // 申请人头像
  QString applyMessage;     // 申请消息
  QString applyTime;        // 申请时间
  FriendApplyStatus status; // 申请状态

  FriendApplyInfo() : applyId(0), applicantId(0), status(APPLY_PENDING) {}
};

// 分享信息结构
struct ShareInfo {
  QString shareCode;     // 分享码
  quint32 fileId;        // 文件ID
  quint32 ownerId;       // 所有者ID
  QString createTime;    // 创建时间
  QString expireTime;    // 过期时间
  quint32 downloadCount; // 下载次数

  ShareInfo() : fileId(0), ownerId(0), downloadCount(0) {}
};

// 错误码枚举
enum ErrorCode {
  ERROR_NONE = 0,         // 无错误
  ERROR_NETWORK,          // 网络错误
  ERROR_SERVER,           // 服务器错误
  ERROR_AUTH,             // 认证失败
  ERROR_FILE_NOT_FOUND,   // 文件未找到
  ERROR_FILE_EXISTS,      // 文件已存在
  ERROR_PERMISSION,       // 权限不足
  ERROR_STORAGE_FULL,     // 存储空间已满
  ERROR_FRIEND_EXISTS,    // 好友关系已存在
  ERROR_FRIEND_NOT_FOUND, // 好友未找到
  ERROR_SHARE_NOT_FOUND,  // 分享未找到
  ERROR_SHARE_EXPIRED     // 分享已过期
};

// 日志级别枚举
enum LogLevel { LOG_DEBUG, LOG_INFO, LOG_WARN, LOG_ERROR };

// 服务器配置
struct ServerConfig {
  QString host;           // 服务器地址
  quint16 port;           // 服务器端口
  QString dbPath;         // 数据库路径
  QString fileDir;        // 文件存储目录
  int maxConnections;     // 最大连接数
  int heartbeatInterval;  // 心跳间隔(秒)
  int sessionTimeout;     // 会话超时时间(秒)
  quint64 defaultStorage; // 默认存储空间大小(字节)

  ServerConfig()
      : port(0), maxConnections(0), heartbeatInterval(0), sessionTimeout(0),
        defaultStorage(0) {}
};

// 客户端配置
struct ClientConfig {
  QString serverHost;    // 服务器地址
  quint16 serverPort;    // 服务器端口
  QString downloadDir;   // 下载目录
  int heartbeatInterval; // 心跳间隔(秒)
  int reconnectInterval; // 重连间隔(秒)
  int maxRetries;        // 最大重试次数
  int chunkSize;         // 文件传输块大小(字节)

  ClientConfig()
      : serverPort(0), heartbeatInterval(30), reconnectInterval(5),
        maxRetries(3), chunkSize(64 * 1024) {} // 默认64KB块大小
};

// 文件传输类型枚举
enum TransferType {
  TRANSFER_UPLOAD,  // 上传
  TRANSFER_DOWNLOAD // 下载
};

// 文件传输状态枚举
enum TransferStatus {
  TRANSFER_PENDING,     // 等待中
  TRANSFER_TRANSFERING, // 传输中
  TRANSFER_PAUSED,      // 已暂停
  TRANSFER_COMPLETED,   // 已完成
  TRANSFER_FAILED,      // 失败
  TRANSFER_CANCELED     // 已取消
};

#endif // COMMON_H
