#ifndef FILEPROPERTIESDIALOG_H
#define FILEPROPERTIESDIALOG_H

#include <QDialog>
#include "../Common/common.h"

namespace Ui {
class FilePropertiesDialog;
}

class FilePropertiesDialog : public QDialog
{
    Q_OBJECT

public:
    explicit FilePropertiesDialog(QWidget *parent = nullptr);
    ~FilePropertiesDialog();

    // 设置文件信息
    void setFileInfo(const FileInfo& fileInfo);

    // 获取文件信息
    FileInfo getFileInfo() const;

private slots:
    // 确定按钮点击槽函数
    void on_pushButton_ok_clicked();

private:
    // 初始化UI
    void initUI();

    // 加载文件信息
    void loadFileInfo();

    // 格式化文件大小
    QString formatFileSize(quint64 size) const;

    Ui::FilePropertiesDialog *ui;      // UI对象
    FileInfo m_fileInfo;              // 文件信息
};

#endif // FILEPROPERTIESDIALOG_H
