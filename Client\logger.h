#ifndef LOGGER_H
#define LOGGER_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QDebug>
#include <QMutex>
#include <QMutexLocker>
#include "../Common/common.h"

// 日志管理器类（单例模式）
class Logger : public QObject
{
    Q_OBJECT

public:
    // 获取单例实例
    static Logger* getInstance();

    // 设置日志级别
    void setLogLevel(LogLevel level);

    // 记录不同级别的日志
    void debug(const QString& module, const QString& message);
    void info(const QString& module, const QString& message);
    void warn(const QString& module, const QString& message);
    void error(const QString& module, const QString& message);

private:
    // 私有构造函数
    Logger(QObject *parent = nullptr);

    // 私有析构函数
    ~Logger();

    // 禁止拷贝构造和赋值
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;

    // 日志输出方法
    void log(LogLevel level, const QString& module, const QString& message);

    // 将日志级别转换为字符串
    QString levelToString(LogLevel level);

    // 格式化日志消息
    QString formatMessage(LogLevel level, const QString& module, const QString& message);

    static Logger* m_instance;     // 单例实例
    static QMutex m_mutex;         // 互斥锁
    LogLevel m_logLevel;           // 当前日志级别
};

// 定义日志宏，方便使用
#define LOG_DEBUG(module, message) Logger::getInstance()->debug(module, message)
#define LOG_INFO(module, message)  Logger::getInstance()->info(module, message)
#define LOG_WARN(module, message)  Logger::getInstance()->warn(module, message)
#define LOG_ERROR(module, message) Logger::getInstance()->error(module, message)

#endif // LOGGER_H
