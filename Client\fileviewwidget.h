#ifndef FILEVIEWWIDGET_H
#define FILEVIEWWIDGET_H

#include "../Common/common.h"
#include "filesearchdialog.h"
#include "filesharedialog.h"
#include <QContextMenuEvent>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QFileDialog>
#include <QMenu>
#include <QMessageBox>
#include <QMimeData>
#include <QStandardPaths>
#include <QWidget>

namespace Ui {
class FileViewWidget;
}

class FileViewWidget : public QWidget {
  Q_OBJECT

public:
  explicit FileViewWidget(QWidget *parent = nullptr);
  ~FileViewWidget();

  // 设置父目录ID
  void setParentId(quint32 parentId);

  // 获取父目录ID
  quint32 parentId() const;

  // 设置文件列表
  void setFileList(const QList<FileInfo> &fileList);

signals:
  // 文件选中信号
  void fileSelected(const FileInfo &fileInfo);

  // 打开目录请求信号
  void openDirectoryRequested(quint32 parentId, const QString &title);

  // 上传文件请求信号
  void uploadFileRequested(const QString &filePath);

  // 下载文件请求信号
  void downloadFileRequested(quint32 fileId);

  // 新建文件夹请求信号
  void newFolderRequested(const QString &folderName);

  // 删除文件请求信号
  void deleteFileRequested(quint32 fileId);

  // 重命名文件请求信号
  void renameFileRequested(quint32 fileId, const QString &newName);

  // 移动文件请求信号
  void moveFileRequested(quint32 fileId, quint32 newParentId);

  // 搜索文件请求信号
  void searchFileRequested(const QString &keyword);

  // 分享文件请求信号
  void shareFileRequested(quint32 fileId);

private slots:
  // 列表视图双击槽函数
  void on_listView_doubleClicked(const QModelIndex &index);

  // 图标视图双击槽函数
  void on_iconView_doubleClicked(const QModelIndex &index);

  // 列表视图点击槽函数
  void on_listView_clicked(const QModelIndex &index);

  // 图标视图点击槽函数
  void on_iconView_clicked(const QModelIndex &index);

  // 刷新按钮点击槽函数
  void on_pushButton_refresh_clicked();

  // 上传按钮点击槽函数
  void on_pushButton_upload_clicked();

  // 下载按钮点击槽函数
  void on_pushButton_download_clicked();

  // 新建文件夹按钮点击槽函数
  void on_pushButton_new_folder_clicked();

  // 删除按钮点击槽函数
  void on_pushButton_delete_clicked();

  // 重命名按钮点击槽函数
  void on_pushButton_rename_clicked();

  // 搜索按钮点击槽函数
  void on_pushButton_search_clicked();

  // 文件拖放槽函数
  void handleFileDrop(const QList<QUrl> &urls);

  // 文件搜索对话框槽函数
  void onSearchFileRequested(const QString &keyword);
  void onOpenDirectoryRequested(quint32 fileId, const QString &dirName);
  void onDownloadFileRequested(quint32 fileId);

  // 文件分享对话框槽函数
  void onShareFileRequested(quint32 fileId);
  void onShareFileCompleted(bool success, const QString &message,
                            const QString &shareCode);

private:
  // 初始化UI
  void initUI();

  // 初始化上下文菜单
  void initContextMenu();

  // 更新视图
  void updateView();

  // 获取选中的文件
  FileInfo getSelectedFile() const;

  // 上下文菜单事件
  void contextMenuEvent(QContextMenuEvent *event) override;

  // 拖放事件处理
  void dragEnterEvent(QDragEnterEvent *event) override;
  void dropEvent(QDropEvent *event) override;

  // 处理拖放文件
  void handleDroppedFiles(const QList<QUrl> &urls);

  Ui::FileViewWidget *ui;     // UI对象
  quint32 m_parentId;         // 父目录ID
  QList<FileInfo> m_fileList; // 文件列表
  QMenu *m_contextMenu;       // 上下文菜单
  QAction *m_actionOpen;      // 打开动作
  QAction *m_actionDownload;  // 下载动作
  QAction *m_actionShare;     // 分享动作
  QAction *m_actionRename;    // 重命名动作
  QAction *m_actionDelete;    // 删除动作
  QAction *m_actionCancel;    // 取消动作

  // 网络管理器相关
  class NetworkManager *m_networkManager;

  // 取消文件操作
  void cancelFileOperation(quint32 fileId);

  // 处理取消操作
  void handleCancelOperation();
};

#endif // FILEVIEWWIDGET_H
