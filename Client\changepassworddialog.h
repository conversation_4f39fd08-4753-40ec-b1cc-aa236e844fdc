#ifndef CHANGEPASSWORDDIALOG_H
#define CHANGEPASSWORDDIALOG_H

#include <QDialog>
#include "../Common/common.h"

namespace Ui {
class ChangePasswordDialog;
}

class ChangePasswordDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ChangePasswordDialog(QWidget *parent = nullptr);
    ~ChangePasswordDialog();

    // 设置用户ID
    void setUserId(quint32 userId);

    // 获取用户ID
    quint32 userId() const;

signals:
    // 修改密码请求信号
    void changePasswordRequested(quint32 userId, const QString& oldPassword, const QString& newPassword);

private slots:
    // 确定按钮点击槽函数
    void on_pushButton_ok_clicked();

    // 取消按钮点击槽函数
    void on_pushButton_cancel_clicked();

private:
    // 初始化UI
    void initUI();

    // 验证输入
    bool validateInput();

    Ui::ChangePasswordDialog *ui;      // UI对象
    quint32 m_userId;                  // 用户ID
};

#endif // CHANGEPASSWORDDIALOG_H
