#include "filetransfermanager.h"
#include "logger.h"
#include "networkmanager.h"
#include <QDateTime>
#include <QDir>
#include <QFileInfo>
#include <QStandardPaths>

// 初始化静态成员变量
FileTransferManager *FileTransferManager::m_instance = nullptr;
QMutex FileTransferManager::m_mutex;

// 获取单例实例
FileTransferManager *FileTransferManager::getInstance() {
  if (m_instance == nullptr) {
    QMutexLocker locker(&m_mutex);
    if (m_instance == nullptr) {
      m_instance = new FileTransferManager();
    }
  }
  return m_instance;
}

// 私有构造函数
FileTransferManager::FileTransferManager(QObject *parent)
    : QObject(parent), m_speedCalculationTimer(nullptr), m_nextTaskId(1),
      m_chunkSize(1024 * 1024),   // 默认1MB块大小
      m_maxConcurrentTransfers(3) // 默认最大3个并发传输
{
  // 初始化网络管理器
  m_networkManager = NetworkManager::getInstance();

  // 创建速度计算定时器
  m_speedCalculationTimer = new QTimer(this);
  connect(m_speedCalculationTimer, &QTimer::timeout, this,
          &FileTransferManager::onSpeedCalculationTimeout);
  m_speedCalculationTimer->start(1000); // 每秒计算一次速度

  LOG_INFO("FileTransfer", "文件传输管理器初始化完成");
}

// 私有析构函数
FileTransferManager::~FileTransferManager() {
  // 停止定时器
  if (m_speedCalculationTimer) {
    m_speedCalculationTimer->stop();
    delete m_speedCalculationTimer;
    m_speedCalculationTimer = nullptr;
  }

  // 清理上传文件
  for (auto it = m_uploadFiles.begin(); it != m_uploadFiles.end(); ++it) {
    QFile *file = it.value();
    if (file) {
      file->close();
      delete file;
    }
  }
  m_uploadFiles.clear();

  // 清理下载文件
  for (auto it = m_downloadFiles.begin(); it != m_downloadFiles.end(); ++it) {
    QFile *file = it.value();
    if (file) {
      file->close();
      delete file;
    }
  }
  m_downloadFiles.clear();
}

// 添加上传任务
quint32 FileTransferManager::addUploadTask(const QString &localPath,
                                           quint32 parentId) {
  // 检查文件是否存在
  QFileInfo fileInfo(localPath);
  if (!fileInfo.exists()) {
    LOG_ERROR("FileTransfer", QString("文件不存在: %1").arg(localPath));
    return 0;
  }

  // 生成任务ID
  quint32 taskId = generateTaskId();

  // 创建任务
  TransferTask task;
  task.taskId = taskId;
  task.fileName = fileInfo.fileName();
  task.fileSize = fileInfo.size();
  task.localPath = localPath;
  task.type = TRANSFER_UPLOAD;
  task.status = TRANSFER_PENDING;
  task.startTime = QDateTime::currentDateTime();

  // 添加到任务列表
  m_tasks[taskId] = task;

  LOG_INFO("FileTransfer",
           QString("添加上传任务，任务ID: %1，文件: %2，大小: %3 字节")
               .arg(taskId)
               .arg(localPath)
               .arg(fileInfo.size()));

  // 发送信号
  emit taskAdded(taskId);

  return taskId;
}

// 添加下载任务
quint32 FileTransferManager::addDownloadTask(quint32 fileId,
                                             const QString &localPath) {
  // 生成任务ID
  quint32 taskId = generateTaskId();

  // 创建任务
  TransferTask task;
  task.taskId = taskId;
  task.fileId = fileId;
  task.localPath = localPath;
  task.type = TRANSFER_DOWNLOAD;
  task.status = TRANSFER_PENDING;
  task.startTime = QDateTime::currentDateTime();

  // 添加到任务列表
  m_tasks[taskId] = task;

  LOG_INFO("FileTransfer",
           QString("添加下载任务，任务ID: %1，文件ID: %2，本地路径: %3")
               .arg(taskId)
               .arg(fileId)
               .arg(localPath));

  // 发送信号
  emit taskAdded(taskId);

  return taskId;
}

// 开始任务
bool FileTransferManager::startTask(quint32 taskId) {
  // 检查任务是否存在
  if (!m_tasks.contains(taskId)) {
    LOG_ERROR("FileTransfer", QString("任务不存在，任务ID: %1").arg(taskId));
    return false;
  }

  // 获取任务
  TransferTask &task = m_tasks[taskId];

  // 检查任务状态
  if (task.status != TRANSFER_PENDING && task.status != TRANSFER_PAUSED) {
    LOG_WARN("FileTransfer", QString("任务状态不允许开始，任务ID: %1，状态: %2")
                                 .arg(taskId)
                                 .arg(task.status));
    return false;
  }

  // 根据任务类型处理
  if (task.type == TRANSFER_UPLOAD) {
    // 打开文件
    QFile *file = new QFile(task.localPath);
    if (!file->open(QIODevice::ReadOnly)) {
      LOG_ERROR("FileTransfer",
                QString("无法打开文件: %1").arg(task.localPath));
      delete file;
      return false;
    }

    // 如果是断点续传，则设置文件位置
    if (task.bytesTransferred > 0) {
      file->seek(task.bytesTransferred);
    }

    // 保存文件指针
    m_uploadFiles[taskId] = file;
  } else if (task.type == TRANSFER_DOWNLOAD) {
    // 打开文件
    QFile *file = new QFile(task.localPath);
    if (!file->open(QIODevice::WriteOnly)) {
      LOG_ERROR("FileTransfer",
                QString("无法创建文件: %1").arg(task.localPath));
      delete file;
      return false;
    }

    // 如果是断点续传，则设置文件位置
    if (task.bytesTransferred > 0) {
      file->seek(task.bytesTransferred);
    }

    // 保存文件指针
    m_downloadFiles[taskId] = file;
  }

  // 更新任务状态
  updateTaskStatus(taskId, TRANSFER_TRANSFERING);

  LOG_INFO("FileTransfer", QString("开始任务，任务ID: %1").arg(taskId));

  return true;
}

// 暂停任务
bool FileTransferManager::pauseTask(quint32 taskId) {
  // 检查任务是否存在
  if (!m_tasks.contains(taskId)) {
    LOG_ERROR("FileTransfer", QString("任务不存在，任务ID: %1").arg(taskId));
    return false;
  }

  // 获取任务
  TransferTask &task = m_tasks[taskId];

  // 检查任务状态
  if (task.status != TRANSFER_TRANSFERING) {
    LOG_WARN("FileTransfer", QString("任务状态不允许暂停，任务ID: %1，状态: %2")
                                 .arg(taskId)
                                 .arg(task.status));
    return false;
  }

  // 更新任务状态
  updateTaskStatus(taskId, TRANSFER_PAUSED);

  LOG_INFO("FileTransfer", QString("暂停任务，任务ID: %1").arg(taskId));

  return true;
}

// 继续任务
bool FileTransferManager::resumeTask(quint32 taskId) {
  // 检查任务是否存在
  if (!m_tasks.contains(taskId)) {
    LOG_ERROR("FileTransfer", QString("任务不存在，任务ID: %1").arg(taskId));
    return false;
  }

  // 获取任务
  TransferTask &task = m_tasks[taskId];

  // 检查任务状态
  if (task.status != TRANSFER_PAUSED) {
    LOG_WARN("FileTransfer", QString("任务状态不允许继续，任务ID: %1，状态: %2")
                                 .arg(taskId)
                                 .arg(task.status));
    return false;
  }

  // 更新任务状态
  updateTaskStatus(taskId, TRANSFER_TRANSFERING);

  LOG_INFO("FileTransfer", QString("继续任务，任务ID: %1").arg(taskId));

  return true;
}

// 取消任务
bool FileTransferManager::cancelTask(quint32 taskId) {
  // 检查任务是否存在
  if (!m_tasks.contains(taskId)) {
    LOG_ERROR("FileTransfer", QString("任务不存在，任务ID: %1").arg(taskId));
    return false;
  }

  // 获取任务
  TransferTask &task = m_tasks[taskId];

  // 检查任务状态
  if (task.status == TRANSFER_COMPLETED || task.status == TRANSFER_FAILED) {
    LOG_WARN("FileTransfer", QString("任务状态不允许取消，任务ID: %1，状态: %2")
                                 .arg(taskId)
                                 .arg(task.status));
    return false;
  }

  // 关闭文件
  if (task.type == TRANSFER_UPLOAD && m_uploadFiles.contains(taskId)) {
    QFile *file = m_uploadFiles[taskId];
    file->close();
    delete file;
    m_uploadFiles.remove(taskId);
  } else if (task.type == TRANSFER_DOWNLOAD &&
             m_downloadFiles.contains(taskId)) {
    QFile *file = m_downloadFiles[taskId];
    file->close();
    delete file;
    m_downloadFiles.remove(taskId);
  }

  // 更新任务状态
  updateTaskStatus(taskId, TRANSFER_CANCELED);

  LOG_INFO("FileTransfer", QString("取消任务，任务ID: %1").arg(taskId));

  return true;
}

// 获取任务列表
QList<TransferTask> FileTransferManager::getTaskList() const {
  QList<TransferTask> taskList;
  for (auto it = m_tasks.begin(); it != m_tasks.end(); ++it) {
    taskList.append(it.value());
  }
  return taskList;
}

// 获取任务
TransferTask FileTransferManager::getTask(quint32 taskId) const {
  if (m_tasks.contains(taskId)) {
    return m_tasks[taskId];
  }
  return TransferTask();
}

// 更新任务进度
bool FileTransferManager::updateTaskProgress(quint32 taskId,
                                             quint64 bytesTransferred) {
  // 检查任务是否存在
  if (!m_tasks.contains(taskId)) {
    LOG_ERROR("FileTransfer", QString("任务不存在，任务ID: %1").arg(taskId));
    return false;
  }

  // 获取任务
  TransferTask &task = m_tasks[taskId];

  // 更新进度
  task.bytesTransferred = bytesTransferred;

  // 发送信号
  emit taskProgressUpdated(taskId, bytesTransferred, task.fileSize);

  return true;
}

// 完成任务
bool FileTransferManager::completeTask(quint32 taskId) {
  // 检查任务是否存在
  if (!m_tasks.contains(taskId)) {
    LOG_ERROR("FileTransfer", QString("任务不存在，任务ID: %1").arg(taskId));
    return false;
  }

  // 获取任务
  TransferTask &task = m_tasks[taskId];

  // 关闭文件
  if (task.type == TRANSFER_UPLOAD && m_uploadFiles.contains(taskId)) {
    QFile *file = m_uploadFiles[taskId];
    file->close();
    delete file;
    m_uploadFiles.remove(taskId);
  } else if (task.type == TRANSFER_DOWNLOAD &&
             m_downloadFiles.contains(taskId)) {
    QFile *file = m_downloadFiles[taskId];
    file->close();
    delete file;
    m_downloadFiles.remove(taskId);
  }

  // 更新任务状态
  task.endTime = QDateTime::currentDateTime();
  updateTaskStatus(taskId, TRANSFER_COMPLETED);

  LOG_INFO("FileTransfer", QString("完成任务，任务ID: %1").arg(taskId));

  return true;
}

// 任务失败
bool FileTransferManager::failTask(quint32 taskId,
                                   const QString &errorMessage) {
  // 检查任务是否存在
  if (!m_tasks.contains(taskId)) {
    LOG_ERROR("FileTransfer", QString("任务不存在，任务ID: %1").arg(taskId));
    return false;
  }

  // 获取任务
  TransferTask &task = m_tasks[taskId];

  // 关闭文件
  if (task.type == TRANSFER_UPLOAD && m_uploadFiles.contains(taskId)) {
    QFile *file = m_uploadFiles[taskId];
    file->close();
    delete file;
    m_uploadFiles.remove(taskId);
  } else if (task.type == TRANSFER_DOWNLOAD &&
             m_downloadFiles.contains(taskId)) {
    QFile *file = m_downloadFiles[taskId];
    file->close();
    delete file;
    m_downloadFiles.remove(taskId);
  }

  // 更新任务状态
  task.errorMessage = errorMessage;
  task.endTime = QDateTime::currentDateTime();
  updateTaskStatus(taskId, TRANSFER_FAILED);

  LOG_ERROR(
      "FileTransfer",
      QString("任务失败，任务ID: %1，错误: %2").arg(taskId).arg(errorMessage));

  return true;
}

// 清理已完成任务
void FileTransferManager::cleanupCompletedTasks() {
  QList<quint32> taskIdsToRemove;

  // 找出需要清理的任务
  for (auto it = m_tasks.begin(); it != m_tasks.end(); ++it) {
    quint32 taskId = it.key();
    const TransferTask &task = it.value();

    // 如果任务已完成或失败，并且已经结束超过5分钟，则清理
    if ((task.status == TRANSFER_COMPLETED || task.status == TRANSFER_FAILED ||
         task.status == TRANSFER_CANCELED) &&
        task.endTime.isValid()) {
      QDateTime now = QDateTime::currentDateTime();
      if (task.endTime.secsTo(now) > 300) { // 5分钟
        taskIdsToRemove.append(taskId);
      }
    }
  }

  // 清理任务
  for (quint32 taskId : taskIdsToRemove) {
    m_tasks.remove(taskId);
    LOG_INFO("FileTransfer", QString("清理任务，任务ID: %1").arg(taskId));
  }
}

// 速度计算定时器槽函数
void FileTransferManager::onSpeedCalculationTimeout() {
  // 计算传输速度
  calculateTransferSpeed();
}

// 生成任务ID
quint32 FileTransferManager::generateTaskId() { return m_nextTaskId++; }

// 计算传输速度
void FileTransferManager::calculateTransferSpeed() {
  // 遍历所有正在传输的任务
  for (auto it = m_tasks.begin(); it != m_tasks.end(); ++it) {
    quint32 taskId = it.key();
    TransferTask &task = it.value();

    // 只计算正在传输的任务
    if (task.status == TRANSFER_TRANSFERING) {
      // 计算速度（字节/秒）
      QDateTime now = QDateTime::currentDateTime();
      qint64 elapsedSeconds = task.startTime.secsTo(now);
      if (elapsedSeconds > 0) {
        task.speed = task.bytesTransferred / elapsedSeconds;

        // 计算剩余时间（秒）
        if (task.speed > 0) {
          quint64 remainingBytes = task.fileSize - task.bytesTransferred;
          task.remainingTime = remainingBytes / task.speed;
        } else {
          task.remainingTime = -1; // 未知
        }

        // 发送信号
        emit taskSpeedUpdated(taskId, task.speed, task.remainingTime);
      }
    }
  }
}

// 更新任务状态
void FileTransferManager::updateTaskStatus(quint32 taskId,
                                           TransferStatus status) {
  // 检查任务是否存在
  if (!m_tasks.contains(taskId)) {
    return;
  }

  // 获取任务
  TransferTask &task = m_tasks[taskId];

  // 更新状态
  task.status = status;

  // 发送信号
  emit taskStatusChanged(taskId, status);
}

// 发送下一个数据块
void FileTransferManager::sendNextChunk(quint32 taskId) {
  // 检查任务是否存在
  if (!m_tasks.contains(taskId)) {
    LOG_ERROR("FileTransfer", QString("任务不存在，任务ID: %1").arg(taskId));
    return;
  }

  // 获取任务
  TransferTask &task = m_tasks[taskId];

  // 检查是否还有数据块需要发送
  if (task.bytesTransferred >= task.fileSize) {
    // 所有数据块已发送完成，完成传输
    completeTask(taskId);
    return;
  }

  // 计算当前块的偏移量和大小
  quint64 offset = task.bytesTransferred;
  quint64 chunkSize =
      qMin(static_cast<quint64>(m_chunkSize), task.fileSize - offset);

  // 读取数据块
  QByteArray chunkData;
  if (task.type == TRANSFER_UPLOAD) {
    // 上传任务
    if (m_uploadFiles.contains(taskId)) {
      QFile *file = m_uploadFiles[taskId];
      if (file) {
        // 确保文件指针在正确位置
        file->seek(offset);

        // 读取数据块
        chunkData = file->read(chunkSize);
        if (chunkData.isEmpty()) {
          LOG_ERROR("FileTransfer",
                    QString("读取文件数据块失败，任务ID: %1").arg(taskId));
          failTask(taskId, "读取文件数据块失败");
          return;
        }

        // 更新任务进度
        task.bytesTransferred += chunkData.size();
        updateTaskProgress(taskId, task.bytesTransferred);

        // 发送数据块到网络管理器
        m_networkManager->sendFileData(taskId, offset, chunkData);
        LOG_INFO("FileTransfer",
                 QString("发送数据块，任务ID: %1，偏移量: %2，大小: %3")
                     .arg(taskId)
                     .arg(offset)
                     .arg(chunkData.size()));

        // 更新已发送块数
        if (!m_chunkOffsets.contains(taskId)) {
          m_chunkOffsets[taskId] = offset;
          m_chunkCounts[taskId] =
              (task.fileSize + m_chunkSize - 1) / m_chunkSize; // 计算总块数
          m_sentChunks[taskId] = 0;
        }
        m_sentChunks[taskId]++;

        // 发送信号通知数据块已准备好发送
        emit chunkReadyToSend(taskId, offset, chunkData);
      } else {
        LOG_ERROR("FileTransfer",
                  QString("上传文件不存在，任务ID: %1").arg(taskId));
        failTask(taskId, "上传文件不存在");
      }
    } else {
      LOG_ERROR("FileTransfer",
                QString("上传文件未打开，任务ID: %1").arg(taskId));
      failTask(taskId, "上传文件未打开");
    }
  } else if (task.type == TRANSFER_DOWNLOAD) {
    // 下载任务
    // 下载任务的数据块接收由handleChunkReceived方法处理
    // 这里只是请求下一个数据块
    LOG_INFO("FileTransfer",
             QString("请求下载数据块，任务ID: %1，偏移量: %2，大小: %3")
                 .arg(taskId)
                 .arg(offset)
                 .arg(chunkSize));

    // 更新已请求块数
    if (!m_chunkOffsets.contains(taskId)) {
      m_chunkOffsets[taskId] = offset;
      m_chunkCounts[taskId] =
          (task.fileSize + m_chunkSize - 1) / m_chunkSize; // 计算总块数
      m_sentChunks[taskId] = 0;
    }
    m_sentChunks[taskId]++;

    // 发送信号通知请求下一个数据块
    emit requestNextChunk(taskId, offset, chunkSize);
  }
}

// 处理数据块发送完成
void FileTransferManager::handleChunkSent(quint32 taskId, bool success) {
  // 检查任务是否存在
  if (!m_tasks.contains(taskId)) {
    LOG_ERROR("FileTransfer", QString("任务不存在，任务ID: %1").arg(taskId));
    return;
  }

  if (success) {
    // 发送成功，继续发送下一个数据块
    LOG_INFO("FileTransfer", QString("数据块发送成功，任务ID: %1").arg(taskId));
    sendNextChunk(taskId);
  } else {
    // 发送失败，处理错误
    LOG_ERROR("FileTransfer",
              QString("数据块发送失败，任务ID: %1").arg(taskId));
    failTask(taskId, "数据块发送失败");
  }
}

// 处理接收到的数据块
void FileTransferManager::handleChunkReceived(quint32 taskId,
                                              const QByteArray &data) {
  // 检查任务是否存在
  if (!m_tasks.contains(taskId)) {
    LOG_ERROR("FileTransfer", QString("任务不存在，任务ID: %1").arg(taskId));
    return;
  }

  // 获取任务
  TransferTask &task = m_tasks[taskId];

  // 检查是否为下载任务
  if (task.type != TRANSFER_DOWNLOAD) {
    LOG_ERROR("FileTransfer", QString("任务类型错误，任务ID: %1").arg(taskId));
    return;
  }

  // 检查下载文件是否已打开
  if (!m_downloadFiles.contains(taskId)) {
    LOG_ERROR("FileTransfer",
              QString("下载文件未打开，任务ID: %1").arg(taskId));
    failTask(taskId, "下载文件未打开");
    return;
  }

  // 写入数据块
  QFile *file = m_downloadFiles[taskId];
  if (file) {
    // 写入数据
    qint64 bytesWritten = file->write(data);
    if (bytesWritten == -1) {
      LOG_ERROR("FileTransfer",
                QString("写入文件失败，任务ID: %1").arg(taskId));
      failTask(taskId, "写入文件失败");
      return;
    }

    // 更新任务进度
    task.bytesTransferred += bytesWritten;
    updateTaskProgress(taskId, task.bytesTransferred);

    // 更新已接收块数
    if (!m_sentChunks.contains(taskId)) {
      m_chunkOffsets[taskId] = task.bytesTransferred - bytesWritten;
      m_chunkCounts[taskId] =
          (task.fileSize + m_chunkSize - 1) / m_chunkSize; // 计算总块数
      m_sentChunks[taskId] = 0;
    }
    m_sentChunks[taskId]++;

    LOG_INFO("FileTransfer",
             QString("接收数据块成功，任务ID: %1，已接收: %2/%3 字节")
                 .arg(taskId)
                 .arg(task.bytesTransferred)
                 .arg(task.fileSize));

    // 检查是否下载完成
    if (task.bytesTransferred >= task.fileSize) {
      // 所有数据块已接收完成，完成传输
      completeTask(taskId);
    } else {
      // 继续请求下一个数据块
      sendNextChunk(taskId);
    }
  }
}

// 处理数据块传输错误
void FileTransferManager::handleChunkError(quint32 taskId,
                                           const QString &error) {
  LOG_ERROR(
      "FileTransfer",
      QString("数据块传输错误，任务ID: %1，错误: %2").arg(taskId).arg(error));
  failTask(taskId, error);
}

// 重试传输
void FileTransferManager::retryTransfer(quint32 taskId) {
  // 检查任务是否存在
  if (!m_tasks.contains(taskId)) {
    LOG_ERROR("FileTransfer", QString("任务不存在，任务ID: %1").arg(taskId));
    return;
  }

  // 获取任务
  TransferTask &task = m_tasks[taskId];

  // 检查任务状态是否允许重试
  if (task.status != TRANSFER_FAILED && task.status != TRANSFER_CANCELED) {
    LOG_WARN("FileTransfer", QString("任务状态不允许重试，任务ID: %1，状态: %2")
                                 .arg(taskId)
                                 .arg(task.status));
    return;
  }

  // 重置任务状态为等待中
  updateTaskStatus(taskId, TRANSFER_PENDING);

  // 重置传输进度
  task.bytesTransferred = 0;
  updateTaskProgress(taskId, 0);

  // 重置块计数器
  m_chunkOffsets.remove(taskId);
  m_chunkCounts.remove(taskId);
  m_sentChunks.remove(taskId);

  LOG_INFO("FileTransfer", QString("重试传输，任务ID: %1").arg(taskId));

  // 发送信号通知任务已重试
  emit taskRetried(taskId);
}
