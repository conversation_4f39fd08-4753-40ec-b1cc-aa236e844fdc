/********************************************************************************
** Form generated from reading UI file 'loginwindow.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_LOGINWINDOW_H
#define UI_LOGINWINDOW_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_LoginWindow
{
public:
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout;
    QSpacerItem *verticalSpacer_4;
    QHBoxLayout *horizontalLayout;
    QSpacerItem *horizontalSpacer;
    QLabel *label_logo;
    QLabel *label_title;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *verticalSpacer_3;
    QFormLayout *formLayout;
    QLabel *label_username;
    QLineEdit *lineEdit_username;
    QLabel *label_password;
    QLineEdit *lineEdit_password;
    QHBoxLayout *horizontalLayout_2;
    QCheckBox *checkBox_remember;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *pushButton_settings;
    QSpacerItem *verticalSpacer_2;
    QHBoxLayout *horizontalLayout_3;
    QPushButton *pushButton_login;
    QPushButton *pushButton_register;
    QSpacerItem *verticalSpacer;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *LoginWindow)
    {
        if (LoginWindow->objectName().isEmpty())
            LoginWindow->setObjectName("LoginWindow");
        LoginWindow->resize(400, 500);
        LoginWindow->setMinimumSize(QSize(400, 500));
        LoginWindow->setMaximumSize(QSize(400, 500));
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/cloud.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        LoginWindow->setWindowIcon(icon);
        centralwidget = new QWidget(LoginWindow);
        centralwidget->setObjectName("centralwidget");
        verticalLayout = new QVBoxLayout(centralwidget);
        verticalLayout->setObjectName("verticalLayout");
        verticalSpacer_4 = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_4);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName("horizontalLayout");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        label_logo = new QLabel(centralwidget);
        label_logo->setObjectName("label_logo");
        label_logo->setMinimumSize(QSize(80, 80));
        label_logo->setMaximumSize(QSize(80, 80));
        label_logo->setPixmap(QPixmap(QString::fromUtf8(":/icons/cloud.png")));
        label_logo->setScaledContents(true);

        horizontalLayout->addWidget(label_logo);

        label_title = new QLabel(centralwidget);
        label_title->setObjectName("label_title");
        QFont font;
        font.setFamilies({QString::fromUtf8("Arial")});
        font.setPointSize(24);
        font.setBold(true);
        label_title->setFont(font);
        label_title->setAlignment(Qt::AlignCenter);

        horizontalLayout->addWidget(label_title);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);


        verticalLayout->addLayout(horizontalLayout);

        verticalSpacer_3 = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_3);

        formLayout = new QFormLayout();
        formLayout->setObjectName("formLayout");
        formLayout->setHorizontalSpacing(20);
        formLayout->setVerticalSpacing(15);
        label_username = new QLabel(centralwidget);
        label_username->setObjectName("label_username");

        formLayout->setWidget(0, QFormLayout::ItemRole::LabelRole, label_username);

        lineEdit_username = new QLineEdit(centralwidget);
        lineEdit_username->setObjectName("lineEdit_username");

        formLayout->setWidget(0, QFormLayout::ItemRole::FieldRole, lineEdit_username);

        label_password = new QLabel(centralwidget);
        label_password->setObjectName("label_password");

        formLayout->setWidget(1, QFormLayout::ItemRole::LabelRole, label_password);

        lineEdit_password = new QLineEdit(centralwidget);
        lineEdit_password->setObjectName("lineEdit_password");
        lineEdit_password->setEchoMode(QLineEdit::Password);

        formLayout->setWidget(1, QFormLayout::ItemRole::FieldRole, lineEdit_password);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName("horizontalLayout_2");
        checkBox_remember = new QCheckBox(centralwidget);
        checkBox_remember->setObjectName("checkBox_remember");

        horizontalLayout_2->addWidget(checkBox_remember);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_3);

        pushButton_settings = new QPushButton(centralwidget);
        pushButton_settings->setObjectName("pushButton_settings");
        pushButton_settings->setFlat(true);

        horizontalLayout_2->addWidget(pushButton_settings);


        formLayout->setLayout(2, QFormLayout::ItemRole::SpanningRole, horizontalLayout_2);


        verticalLayout->addLayout(formLayout);

        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_2);

        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName("horizontalLayout_3");
        pushButton_login = new QPushButton(centralwidget);
        pushButton_login->setObjectName("pushButton_login");
        pushButton_login->setMinimumSize(QSize(0, 40));
        QFont font1;
        font1.setPointSize(12);
        font1.setBold(true);
        pushButton_login->setFont(font1);

        horizontalLayout_3->addWidget(pushButton_login);

        pushButton_register = new QPushButton(centralwidget);
        pushButton_register->setObjectName("pushButton_register");
        pushButton_register->setMinimumSize(QSize(0, 40));
        QFont font2;
        font2.setPointSize(12);
        pushButton_register->setFont(font2);

        horizontalLayout_3->addWidget(pushButton_register);


        verticalLayout->addLayout(horizontalLayout_3);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        LoginWindow->setCentralWidget(centralwidget);
        statusbar = new QStatusBar(LoginWindow);
        statusbar->setObjectName("statusbar");
        LoginWindow->setStatusBar(statusbar);

        retranslateUi(LoginWindow);

        pushButton_login->setDefault(true);


        QMetaObject::connectSlotsByName(LoginWindow);
    } // setupUi

    void retranslateUi(QMainWindow *LoginWindow)
    {
        LoginWindow->setWindowTitle(QCoreApplication::translate("LoginWindow", "Cloud7 - \347\231\273\345\275\225", nullptr));
        label_logo->setText(QString());
        label_title->setText(QCoreApplication::translate("LoginWindow", "Cloud7", nullptr));
        label_username->setText(QCoreApplication::translate("LoginWindow", "\347\224\250\346\210\267\345\220\215/\351\202\256\347\256\261\357\274\232", nullptr));
        lineEdit_username->setPlaceholderText(QCoreApplication::translate("LoginWindow", "\350\257\267\350\276\223\345\205\245\347\224\250\346\210\267\345\220\215\346\210\226\351\202\256\347\256\261", nullptr));
        label_password->setText(QCoreApplication::translate("LoginWindow", "\345\257\206\347\240\201\357\274\232", nullptr));
        lineEdit_password->setPlaceholderText(QCoreApplication::translate("LoginWindow", "\350\257\267\350\276\223\345\205\245\345\257\206\347\240\201", nullptr));
        checkBox_remember->setText(QCoreApplication::translate("LoginWindow", "\350\256\260\344\275\217\346\210\221", nullptr));
        pushButton_settings->setText(QCoreApplication::translate("LoginWindow", "\346\234\215\345\212\241\345\231\250\350\256\276\347\275\256", nullptr));
        pushButton_login->setText(QCoreApplication::translate("LoginWindow", "\347\231\273\345\275\225", nullptr));
        pushButton_register->setText(QCoreApplication::translate("LoginWindow", "\346\263\250\345\206\214", nullptr));
    } // retranslateUi

};

namespace Ui {
    class LoginWindow: public Ui_LoginWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_LOGINWINDOW_H
