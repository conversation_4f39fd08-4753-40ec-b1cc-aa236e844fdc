/* Cloud7 深色主题样式表 */

/* 主窗口样式 */
QMainWindow {
    background-color: #2b2b2b;
    color: #ffffff;
}

/* 菜单栏样式 */
QMenuBar {
    background-color: #3c3c3c;
    border-bottom: 1px solid #555555;
    color: #ffffff;
    padding: 2px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
    margin: 2px;
    border-radius: 3px;
}

QMenuBar::item:selected {
    background-color: #0d7377;
    color: #ffffff;
}

/* 工具栏样式 */
QToolBar {
    background-color: #3c3c3c;
    border: 1px solid #555555;
    spacing: 2px;
    padding: 2px;
}

QToolButton {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 3px;
    padding: 4px;
    margin: 1px;
    color: #ffffff;
}

QToolButton:hover {
    background-color: #0d7377;
    border-color: #14a085;
}

QToolButton:pressed {
    background-color: #14a085;
}

/* 按钮样式 */
QPushButton {
    background-color: #0d7377;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #14a085;
}

QPushButton:pressed {
    background-color: #40e0d0;
}

QPushButton:disabled {
    background-color: #555555;
    color: #888888;
}

/* 输入框样式 */
QLineEdit {
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 6px;
    background-color: #404040;
    color: #ffffff;
}

QLineEdit:focus {
    border-color: #0d7377;
    outline: none;
}

/* 列表视图样式 */
QListView {
    background-color: #404040;
    border: 1px solid #555555;
    border-radius: 4px;
    color: #ffffff;
    selection-background-color: #0d7377;
}

QListView::item {
    padding: 4px;
    border-bottom: 1px solid #555555;
}

QListView::item:selected {
    background-color: #0d7377;
    color: #ffffff;
}

/* 树视图样式 */
QTreeView {
    background-color: #404040;
    border: 1px solid #555555;
    border-radius: 4px;
    color: #ffffff;
    selection-background-color: #0d7377;
}

QTreeView::item {
    padding: 2px;
}

QTreeView::item:selected {
    background-color: #0d7377;
    color: #ffffff;
}

/* 标签页样式 */
QTabWidget::pane {
    border: 1px solid #555555;
    background-color: #404040;
}

QTabBar::tab {
    background-color: #3c3c3c;
    border: 1px solid #555555;
    border-bottom: none;
    padding: 6px 12px;
    margin-right: 2px;
    color: #ffffff;
}

QTabBar::tab:selected {
    background-color: #404040;
    border-bottom: 2px solid #0d7377;
}

QTabBar::tab:hover {
    background-color: #0d7377;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #3c3c3c;
    border-top: 1px solid #555555;
    color: #cccccc;
}

/* 分割器样式 */
QSplitter::handle {
    background-color: #555555;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

/* 进度条样式 */
QProgressBar {
    border: 1px solid #555555;
    border-radius: 4px;
    text-align: center;
    background-color: #3c3c3c;
    color: #ffffff;
}

QProgressBar::chunk {
    background-color: #0d7377;
    border-radius: 3px;
}
