#include "friendsearchdialog.h"
#include "logger.h"
#include "ui_friendsearchdialog.h"
#include <QMessageBox>

// 构造函数
FriendSearchDialog::FriendSearchDialog(QWidget *parent)
    : QDialog(parent), ui(new Ui::FriendSearchDialog), m_userId(0) {
  // 初始化UI
  ui->setupUi(this);

  // 初始化UI
  initUI();
}

// 析构函数
FriendSearchDialog::~FriendSearchDialog() { delete ui; }

// 设置用户ID
void FriendSearchDialog::setUserId(quint32 userId) { m_userId = userId; }

// 获取用户ID
quint32 FriendSearchDialog::userId() const { return m_userId; }

// 搜索按钮点击槽函数
void FriendSearchDialog::on_pushButton_search_clicked() {
  // 获取搜索关键词
  QString keyword = ui->lineEdit_search->text().trimmed();

  // 检查关键词是否为空
  if (keyword.isEmpty()) {
    QMessageBox::warning(this, "搜索提示", "请输入搜索关键词");
    return;
  }

  // 发送搜索用户请求信号
  emit searchUserRequested(keyword);
}

// 添加好友按钮点击槽函数
void FriendSearchDialog::on_pushButton_add_friend_clicked() {
  // 获取选中的用户
  QList<QTableWidgetItem *> selectedItems =
      ui->tableWidget_users->selectedItems();
  if (selectedItems.isEmpty()) {
    QMessageBox::warning(this, "添加好友", "请选择要添加的用户");
    return;
  }

  // 获取行号
  int row = selectedItems.first()->row();

  // 获取用户ID
  QTableWidgetItem *userIdItem = ui->tableWidget_users->item(row, 0);
  if (!userIdItem) {
    QMessageBox::warning(this, "添加好友", "无法获取用户信息");
    return;
  }

  quint32 userId = userIdItem->data(Qt::UserRole).toUInt();

  // 发送添加好友请求信号
  emit addFriendRequested(userId);
}

// 关闭按钮点击槽函数
void FriendSearchDialog::on_pushButton_close_clicked() {
  // 关闭对话框
  reject();
}

// 初始化UI
void FriendSearchDialog::initUI() {
  // 设置窗口标题
  setWindowTitle("好友搜索");

  // 设置窗口图标
  setWindowIcon(QIcon(":/icons/search.png"));

  // 设置窗口大小
  resize(500, 400);

  // 设置表格属性
  ui->tableWidget_users->setAlternatingRowColors(true);
  ui->tableWidget_users->setSelectionMode(QAbstractItemView::SingleSelection);
  ui->tableWidget_users->setSelectionBehavior(QAbstractItemView::SelectRows);
  ui->tableWidget_users->setEditTriggers(QAbstractItemView::NoEditTriggers);

  // 设置表头
  ui->tableWidget_users->horizontalHeader()->setSectionResizeMode(
      0, QHeaderView::Stretch); // 用户名
  ui->tableWidget_users->horizontalHeader()->setSectionResizeMode(
      1, QHeaderView::Stretch); // 邮箱
  ui->tableWidget_users->horizontalHeader()->setSectionResizeMode(
      2, QHeaderView::ResizeToContents); // 注册时间
  ui->tableWidget_users->horizontalHeader()->setSectionResizeMode(
      3, QHeaderView::ResizeToContents); // 操作

  // 连接信号槽
  connect(ui->pushButton_search, &QPushButton::clicked, this,
          &FriendSearchDialog::on_pushButton_search_clicked);
  connect(ui->pushButton_add_friend, &QPushButton::clicked, this,
          &FriendSearchDialog::on_pushButton_add_friend_clicked);
  connect(ui->pushButton_close, &QPushButton::clicked, this,
          &FriendSearchDialog::on_pushButton_close_clicked);

  // 连接搜索用户响应信号
  connect(this, &FriendSearchDialog::searchUserRequested, this,
          &FriendSearchDialog::searchUsers);
}

// 搜索用户
void FriendSearchDialog::searchUsers() {
  // 获取搜索关键词
  QString keyword = ui->lineEdit_search->text().trimmed();

  // 检查关键词是否为空
  if (keyword.isEmpty()) {
    QMessageBox::warning(this, "搜索提示", "请输入搜索关键词");
    return;
  }

  // 清空当前表格
  ui->tableWidget_users->setRowCount(0);

  // 显示加载状态
  ui->pushButton_search->setEnabled(false);
  ui->pushButton_search->setText("搜索中...");

  // 发送搜索用户请求信号
  emit searchUserRequested(keyword);
}

// 加载用户列表
void FriendSearchDialog::loadUserList(const QList<UserInfo> &userList) {
  // 清空表格
  ui->tableWidget_users->setRowCount(0);

  // 添加用户到表格
  for (const UserInfo &userInfo : userList) {
    // 添加行
    int row = ui->tableWidget_users->rowCount();
    ui->tableWidget_users->insertRow(row);

    // 设置用户名
    QTableWidgetItem *usernameItem = new QTableWidgetItem(userInfo.username);
    usernameItem->setData(Qt::UserRole, userInfo.userId);
    ui->tableWidget_users->setItem(row, 0, usernameItem);

    // 设置邮箱
    QTableWidgetItem *emailItem = new QTableWidgetItem(userInfo.email);
    ui->tableWidget_users->setItem(row, 1, emailItem);

    // 设置注册时间
    QTableWidgetItem *registerTimeItem =
        new QTableWidgetItem(userInfo.registerTime);
    ui->tableWidget_users->setItem(row, 2, registerTimeItem);

    // 设置操作按钮
    QTableWidgetItem *actionItem = new QTableWidgetItem("添加");
    ui->tableWidget_users->setItem(row, 3, actionItem);
  }

  LOG_INFO("FriendSearch",
           QString("加载用户列表完成，共 %1 个用户").arg(userList.size()));
}

// 设置搜索结果
void FriendSearchDialog::setSearchResults(const QList<UserInfo> &userList) {
  // 保存搜索结果
  m_userList = userList;

  // 加载用户列表
  loadUserList(userList);

  // 恢复搜索按钮状态
  ui->pushButton_search->setEnabled(true);
  ui->pushButton_search->setText("搜索");
}

// 添加好友按钮点击槽函数
void FriendSearchDialog::on_pushButton_add_clicked() {
  // 获取选中的用户
  UserInfo selectedUser = getSelectedUser();
  if (selectedUser.userId == 0) {
    QMessageBox::warning(this, "添加好友", "请选择要添加的用户");
    return;
  }

  // 发送添加好友请求信号
  emit addFriendRequested(selectedUser.userId);
}

// 用户列表双击槽函数
void FriendSearchDialog::on_listWidget_users_itemDoubleClicked(
    QListWidgetItem *item) {
  Q_UNUSED(item)
  // 双击时添加好友
  on_pushButton_add_clicked();
}

// 搜索结果响应槽函数
void FriendSearchDialog::onSearchUserResponse(bool success,
                                              const QString &message,
                                              const QList<UserInfo> &userList) {
  // 恢复搜索按钮状态
  ui->pushButton_search->setEnabled(true);
  ui->pushButton_search->setText("搜索");

  if (success) {
    // 设置搜索结果
    setSearchResults(userList);

    if (userList.isEmpty()) {
      QMessageBox::information(this, "搜索结果", "未找到匹配的用户");
    }
  } else {
    QMessageBox::warning(this, "搜索失败", message);
  }
}

// 获取选中的用户
UserInfo FriendSearchDialog::getSelectedUser() const {
  UserInfo userInfo;

  // 获取选中的行
  QList<QTableWidgetItem *> selectedItems =
      ui->tableWidget_users->selectedItems();
  if (selectedItems.isEmpty()) {
    return userInfo;
  }

  // 获取行号
  int row = selectedItems.first()->row();

  // 获取用户ID
  QTableWidgetItem *userIdItem = ui->tableWidget_users->item(row, 0);
  if (userIdItem) {
    quint32 userId = userIdItem->data(Qt::UserRole).toUInt();

    // 在用户列表中查找对应的用户信息
    for (const UserInfo &user : m_userList) {
      if (user.userId == userId) {
        userInfo = user;
        break;
      }
    }
  }

  return userInfo;
}
