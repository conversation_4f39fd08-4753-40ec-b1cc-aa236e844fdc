#ifndef FILEHANDLER_H
#define FILEHANDLER_H

#include "../Common/common.h"
#include <QCryptographicHash>
#include <QDateTime>
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QJsonArray>
#include <QJsonObject>
#include <QMutex>
#include <QMutexLocker>
#include <QObject>
#include <QString>

// 文件处理类（单例模式）
class FileHandler : public QObject {
  Q_OBJECT

public:
  // 获取单例实例
  static FileHandler *getInstance();

  // 初始化文件处理器
  bool initialize(const QString &fileDir);

  // 保存文件
  bool saveFile(quint32 fileId, const QByteArray &data, quint64 offset = 0);

  // 读取文件
  QByteArray readFile(quint32 fileId, quint64 offset = 0, qint64 size = -1);

  // 删除文件
  bool deleteFile(quint32 fileId);

  // 移动文件
  bool moveFile(quint32 fileId, quint32 newParentId);

  // 复制文件
  bool copyFile(quint32 fileId, quint32 newParentId);

  // 重命名文件
  bool renameFile(quint32 fileId, const QString &newName);

  // 计算文件哈希
  QString calculateFileHash(quint32 fileId);

  // 获取文件大小
  quint64 getFileSize(quint32 fileId);

  // 检查文件是否存在
  bool fileExists(quint32 fileId);

  // 创建目录
  quint32 createDirectory(const QString &dirName, quint32 parentId);

  // 获取文件路径
  QString getFilePath(quint32 fileId);

  // 生成文件路径
  QString generateFilePath(quint32 fileId);

  // 清理未使用的文件
  void cleanupUnusedFiles();

private:
  // 私有构造函数
  FileHandler(QObject *parent = nullptr);

  // 私有析构函数
  ~FileHandler();

  // 禁止拷贝构造和赋值
  FileHandler(const FileHandler &) = delete;
  FileHandler &operator=(const FileHandler &) = delete;

  // 创建文件目录结构
  bool createFileDirectoryStructure();

  // 获取文件相对路径
  QString getFileRelativePath(quint32 fileId);

  // 生成新文件ID（临时方案，应该从数据库获取）
  quint32 generateNewFileId();

  static FileHandler *m_instance; // 单例实例
  static QMutex m_mutex;          // 互斥锁

  QString m_fileDir;                    // 文件存储目录
  QMap<quint32, QString> m_filePathMap; // 文件ID到路径的映射
};

#endif // FILEHANDLER_H
