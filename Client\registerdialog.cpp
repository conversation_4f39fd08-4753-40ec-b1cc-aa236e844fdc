#include "registerdialog.h"
#include "logger.h"
#include <QMessageBox>
#include <QRegularExpression>
#include <QRegularExpressionValidator>

// 构造函数
RegisterDialog::RegisterDialog(QWidget *parent)
    : QDialog(parent), ui(new Ui::RegisterDialog) {
  // 初始化UI
  ui->setupUi(this);

  // 初始化UI
  initUI();
}

// 析构函数
RegisterDialog::~RegisterDialog() { delete ui; }

// 初始化UI
void RegisterDialog::initUI() {
  // 设置窗口标题
  setWindowTitle("用户注册");

  // 设置窗口图标
  setWindowIcon(QIcon(":/icons/register.png"));

  // 设置用户名输入框验证器（允许字母、数字、下划线，长度3-20）
  QRegularExpression usernameRegExp("[a-zA-Z0-9_]{3,20}");
  QValidator *usernameValidator =
      new QRegularExpressionValidator(usernameRegExp, this);
  ui->lineEdit_username->setValidator(usernameValidator);

  // 设置邮箱输入框验证器
  QRegularExpression emailRegExp(
      "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
  QValidator *emailValidator =
      new QRegularExpressionValidator(emailRegExp, this);
  ui->lineEdit_email->setValidator(emailValidator);

  // 设置密码输入框验证器（不允许空格）
  QRegularExpression passwordRegExp("\\S+");
  QValidator *passwordValidator =
      new QRegularExpressionValidator(passwordRegExp, this);
  ui->lineEdit_password->setValidator(passwordValidator);
  ui->lineEdit_confirm_password->setValidator(passwordValidator);
}

// 验证输入
bool RegisterDialog::validateInput() {
  // 获取输入
  QString username = ui->lineEdit_username->text().trimmed();
  QString email = ui->lineEdit_email->text().trimmed();
  QString password = ui->lineEdit_password->text();
  QString confirmPassword = ui->lineEdit_confirm_password->text();

  // 验证用户名
  if (username.isEmpty()) {
    QMessageBox::warning(this, "输入错误", "请输入用户名");
    ui->lineEdit_username->setFocus();
    return false;
  }

  // 验证用户名长度
  if (username.length() < 3 || username.length() > 20) {
    QMessageBox::warning(this, "输入错误", "用户名长度应为3-20个字符");
    ui->lineEdit_username->setFocus();
    return false;
  }

  // 验证邮箱
  if (email.isEmpty()) {
    QMessageBox::warning(this, "输入错误", "请输入邮箱地址");
    ui->lineEdit_email->setFocus();
    return false;
  }

  // 验证邮箱格式
  QRegularExpression emailRegExp(
      "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
  if (!emailRegExp.match(email).hasMatch()) {
    QMessageBox::warning(this, "输入错误", "请输入有效的邮箱地址");
    ui->lineEdit_email->setFocus();
    return false;
  }

  // 验证密码
  if (password.isEmpty()) {
    QMessageBox::warning(this, "输入错误", "请输入密码");
    ui->lineEdit_password->setFocus();
    return false;
  }

  // 验证密码长度
  if (password.length() < 6) {
    QMessageBox::warning(this, "输入错误", "密码长度至少为6位");
    ui->lineEdit_password->setFocus();
    return false;
  }

  // 验证确认密码
  if (confirmPassword.isEmpty()) {
    QMessageBox::warning(this, "输入错误", "请确认密码");
    ui->lineEdit_confirm_password->setFocus();
    return false;
  }

  // 验证两次输入的密码是否一致
  if (password != confirmPassword) {
    QMessageBox::warning(this, "输入错误", "两次输入的密码不一致");
    ui->lineEdit_confirm_password->setFocus();
    return false;
  }

  return true;
}

// 对话框接受槽函数
void RegisterDialog::accept() {
  // 验证输入
  if (!validateInput()) {
    return;
  }

  // 获取输入
  QString username = ui->lineEdit_username->text().trimmed();
  QString email = ui->lineEdit_email->text().trimmed();
  QString password = ui->lineEdit_password->text();

  // 发送注册请求信号
  emit registerRequested(username, password, email);

  LOG_INFO(
      "Register",
      QString("发送注册请求，用户名: %1，邮箱: %2").arg(username).arg(email));
}
