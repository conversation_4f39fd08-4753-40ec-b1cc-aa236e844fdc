#include "exception.h"

// Cloud7Exception 构造函数
Cloud7Exception::Cloud7Exception(const QString& message, ErrorCode errorCode) :
    m_message(message),
    m_errorCode(errorCode)
{
}

// Cloud7Exception 析构函数
Cloud7Exception::~Cloud7Exception()
{
}

// 克隆异常
Cloud7Exception* Cloud7Exception::clone() const
{
    return new Cloud7Exception(*this);
}

// 抛出异常
void Cloud7Exception::raise() const
{
    throw *this;
}

// 获取错误消息
QString Cloud7Exception::message() const
{
    return m_message;
}

// 获取错误码
ErrorCode Cloud7Exception::errorCode() const
{
    return m_errorCode;
}

// NetworkException 构造函数
NetworkException::NetworkException(const QString& message, ErrorCode errorCode) :
    Cloud7Exception(message, errorCode)
{
}

// DatabaseException 构造函数
DatabaseException::DatabaseException(const QString& message, ErrorCode errorCode) :
    Cloud7Exception(message, errorCode)
{
}

// FileException 构造函数
FileException::FileException(const QString& message, ErrorCode errorCode) :
    Cloud7Exception(message, errorCode)
{
}

// AuthException 构造函数
AuthException::AuthException(const QString& message, ErrorCode errorCode) :
    Cloud7Exception(message, errorCode)
{
}

// PermissionException 构造函数
PermissionException::PermissionException(const QString& message, ErrorCode errorCode) :
    Cloud7Exception(message, errorCode)
{
}

// ParameterException 构造函数
ParameterException::ParameterException(const QString& message, ErrorCode errorCode) :
    Cloud7Exception(message, errorCode)
{
}
