#ifndef USERSETTINGSDIALOG_H
#define USERSETTINGSDIALOG_H

#include "../Common/common.h"
#include <QDialog>

namespace Ui {
class UserSettingsDialog;
}

class NetworkManager;

class UserSettingsDialog : public QDialog {
  Q_OBJECT

public:
  explicit UserSettingsDialog(QWidget *parent = nullptr);
  ~UserSettingsDialog();

  // 设置用户信息
  void setUserInfo(const UserInfo &userInfo);

  // 获取用户信息
  UserInfo getUserInfo() const;

private slots:
  // 修改密码按钮点击槽函数
  void on_pushButton_change_password_clicked();

  // 保存按钮点击槽函数
  void on_pushButton_save_clicked();

  // 取消按钮点击槽函数
  void on_pushButton_cancel_clicked();

  // 修改密码响应槽函数
  void onChangePasswordResponse(bool success, const QString &message);

  // 更新用户信息响应槽函数
  void onUpdateUserInfoResponse(bool success, const QString &message);

private:
  // 初始化UI
  void initUI();

  // 加载用户信息
  void loadUserInfo();

  // 保存用户信息
  void saveUserInfo();

  // 验证输入
  bool validateInput();

  Ui::UserSettingsDialog *ui;       // UI对象
  UserInfo m_userInfo;              // 用户信息
  NetworkManager *m_networkManager; // 网络管理器
};

#endif // USERSETTINGSDIALOG_H
