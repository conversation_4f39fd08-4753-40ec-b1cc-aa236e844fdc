#include "databasemanager.h"
#include "logger.h"
#include <QCryptographicHash>
#include <QDateTime>
#include <QDir>
#include <QRandomGenerator>
#include <QSqlRecord>
#include <QVariant>


// 初始化静态成员变量
DatabaseManager *DatabaseManager::m_instance = nullptr;
QMutex DatabaseManager::m_mutex;

// 获取单例实例
DatabaseManager *DatabaseManager::getInstance() {
  if (m_instance == nullptr) {
    QMutexLocker locker(&m_mutex);
    if (m_instance == nullptr) {
      m_instance = new DatabaseManager();
    }
  }
  return m_instance;
}

// 私有构造函数
DatabaseManager::DatabaseManager(QObject *parent) : QObject(parent) {
  // 初始化数据库连接
  m_db = QSqlDatabase::addDatabase("QSQLITE");
}

// 私有析构函数
DatabaseManager::~DatabaseManager() {
  // 关闭数据库连接
  close();
}

// 初始化数据库
bool DatabaseManager::initialize(const QString &dbPath) {
  // 保存数据库路径
  m_dbPath = dbPath;

  // 设置数据库路径
  m_db.setDatabaseName(dbPath);

  // 打开数据库
  if (!m_db.open()) {
    LOG_ERROR("Database",
              QString("无法打开数据库: %1").arg(m_db.lastError().text()));
    return false;
  }

  // 创建表
  if (!createTables()) {
    LOG_ERROR("Database", "创建表失败");
    return false;
  }

  LOG_INFO("Database", QString("数据库初始化成功，路径: %1").arg(dbPath));
  return true;
}

// 关闭数据库
void DatabaseManager::close() {
  if (m_db.isOpen()) {
    m_db.close();
    LOG_INFO("Database", "数据库连接已关闭");
  }
}

// 创建表
bool DatabaseManager::createTables() {
  QSqlQuery query(m_db);

  // 创建用户表
  QString createUserTable = R"(
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            email TEXT NOT NULL UNIQUE,
            password_hash TEXT NOT NULL,
            salt TEXT NOT NULL,
            storage_used INTEGER DEFAULT 0,
            storage_total INTEGER NOT NULL,
            register_time TEXT NOT NULL,
            last_login_time TEXT
        )
    )";

  if (!executeQuery(query, createUserTable)) {
    return false;
  }

  // 创建文件表
  QString createFileTable = R"(
        CREATE TABLE IF NOT EXISTS files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_name TEXT NOT NULL,
            file_size INTEGER NOT NULL,
            file_type TEXT NOT NULL,
            parent_id INTEGER NOT NULL,
            owner_id INTEGER NOT NULL,
            file_path TEXT NOT NULL,
            file_hash TEXT,
            create_time TEXT NOT NULL,
            modify_time TEXT NOT NULL,
            is_dir INTEGER NOT NULL DEFAULT 0,
            FOREIGN KEY (parent_id) REFERENCES files(id) ON DELETE CASCADE,
            FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
        )
    )";

  if (!executeQuery(query, createFileTable)) {
    return false;
  }

  // 创建好友关系表
  QString createFriendRelationshipTable = R"(
        CREATE TABLE IF NOT EXISTS friend_relationships (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            friend_id INTEGER NOT NULL,
            add_time TEXT NOT NULL,
            remark TEXT,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (friend_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE(user_id, friend_id)
        )
    )";

  if (!executeQuery(query, createFriendRelationshipTable)) {
    return false;
  }

  // 创建消息表
  QString createMessageTable = R"(
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sender_id INTEGER NOT NULL,
            receiver_id INTEGER NOT NULL,
            content TEXT NOT NULL,
            send_time TEXT NOT NULL,
            is_read INTEGER NOT NULL DEFAULT 0,
            FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
        )
    )";

  if (!executeQuery(query, createMessageTable)) {
    return false;
  }

  // 创建会话表
  QString createSessionTable = R"(
        CREATE TABLE IF NOT EXISTS sessions (
            session_id TEXT PRIMARY KEY,
            user_id INTEGER NOT NULL,
            last_heartbeat TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    )";

  if (!executeQuery(query, createSessionTable)) {
    return false;
  }

  // 创建分享表
  QString createShareTable = R"(
        CREATE TABLE IF NOT EXISTS file_shares (
            share_code TEXT PRIMARY KEY,
            file_id INTEGER NOT NULL,
            owner_id INTEGER NOT NULL,
            create_time TEXT NOT NULL,
            expire_time TEXT,
            download_count INTEGER DEFAULT 0,
            FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE,
            FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
        )
    )";

  if (!executeQuery(query, createShareTable)) {
    return false;
  }

  LOG_INFO("Database", "表创建成功");
  return true;
}

// 添加用户
bool DatabaseManager::addUser(const QString &username,
                              const QString &passwordHash, const QString &salt,
                              const QString &email) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("INSERT INTO users (username, email, password_hash, salt, "
                "storage_total, register_time) "
                "VALUES (:username, :email, :password_hash, :salt, "
                ":storage_total, :register_time)");

  // 绑定参数
  query.bindValue(":username", username);
  query.bindValue(":email", email);
  query.bindValue(":password_hash", passwordHash);
  query.bindValue(":salt", salt);
  query.bindValue(":storage_total", 1073741824); // 默认1GB存储空间
  query.bindValue(":register_time",
                  formatDateTime(QDateTime::currentDateTime()));

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("添加用户失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database", QString("添加用户成功，用户名: %1").arg(username));
  return true;
}

// 获取用户信息
bool DatabaseManager::getUser(const QString &username, UserInfo &userInfo) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("SELECT id, username, email, password_hash, storage_used, "
                "storage_total, register_time, last_login_time "
                "FROM users WHERE username = :username");

  // 绑定参数
  query.bindValue(":username", username);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("获取用户信息失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 检查结果
  if (!query.next()) {
    LOG_WARN("Database", QString("用户不存在，用户名: %1").arg(username));
    return false;
  }

  // 填充用户信息
  userInfo.userId = query.value("id").toUInt();
  userInfo.username = query.value("username").toString();
  userInfo.email = query.value("email").toString();
  userInfo.passwordHash = query.value("password_hash").toString();
  userInfo.storageUsed = query.value("storage_used").toULongLong();
  userInfo.storageTotal = query.value("storage_total").toULongLong();
  userInfo.registerTime = query.value("register_time").toString();
  userInfo.lastLoginTime = query.value("last_login_time").toString();

  return true;
}

// 根据ID获取用户信息
bool DatabaseManager::getUserById(quint32 userId, UserInfo &userInfo) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("SELECT id, username, email, password_hash, storage_used, "
                "storage_total, register_time, last_login_time "
                "FROM users WHERE id = :user_id");

  // 绑定参数
  query.bindValue(":user_id", userId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("获取用户信息失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 检查结果
  if (!query.next()) {
    LOG_WARN("Database", QString("用户不存在，用户ID: %1").arg(userId));
    return false;
  }

  // 填充用户信息
  userInfo.userId = query.value("id").toUInt();
  userInfo.username = query.value("username").toString();
  userInfo.email = query.value("email").toString();
  userInfo.passwordHash = query.value("password_hash").toString();
  userInfo.storageUsed = query.value("storage_used").toULongLong();
  userInfo.storageTotal = query.value("storage_total").toULongLong();
  userInfo.registerTime = query.value("register_time").toString();
  userInfo.lastLoginTime = query.value("last_login_time").toString();

  return true;
}

// 更新用户信息
bool DatabaseManager::updateUser(quint32 userId, const UserInfo &userInfo) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("UPDATE users SET username = :username, email = :email, "
                "storage_used = :storage_used, storage_total = :storage_total, "
                "last_login_time = :last_login_time WHERE id = :user_id");

  // 绑定参数
  query.bindValue(":username", userInfo.username);
  query.bindValue(":email", userInfo.email);
  query.bindValue(":storage_used", userInfo.storageUsed);
  query.bindValue(":storage_total", userInfo.storageTotal);
  query.bindValue(":last_login_time", userInfo.lastLoginTime);
  query.bindValue(":user_id", userId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("更新用户信息失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database", QString("更新用户信息成功，用户ID: %1").arg(userId));
  return true;
}

// 更新用户密码
bool DatabaseManager::updateUserPassword(quint32 userId,
                                         const QString &newPasswordHash,
                                         const QString &salt) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("UPDATE users SET password_hash = :password_hash, salt = :salt "
                "WHERE id = :user_id");

  // 绑定参数
  query.bindValue(":password_hash", newPasswordHash);
  query.bindValue(":salt", salt);
  query.bindValue(":user_id", userId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("更新用户密码失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database", QString("更新用户密码成功，用户ID: %1").arg(userId));
  return true;
}

// 更新用户存储空间使用量
bool DatabaseManager::updateUserStorage(quint32 userId, quint64 storageUsed) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare(
      "UPDATE users SET storage_used = :storage_used WHERE id = :user_id");

  // 绑定参数
  query.bindValue(":storage_used", storageUsed);
  query.bindValue(":user_id", userId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database", QString("更新用户存储空间使用量失败: %1")
                              .arg(query.lastError().text()));
    return false;
  }

  return true;
}

// 删除用户
bool DatabaseManager::deleteUser(quint32 userId) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("DELETE FROM users WHERE id = :user_id");

  // 绑定参数
  query.bindValue(":user_id", userId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("删除用户失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database", QString("删除用户成功，用户ID: %1").arg(userId));
  return true;
}

// 检查用户名是否存在
bool DatabaseManager::checkUsernameExists(const QString &username) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("SELECT COUNT(*) FROM users WHERE username = :username");

  // 绑定参数
  query.bindValue(":username", username);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR(
        "Database",
        QString("检查用户名是否存在失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 检查结果
  if (query.next()) {
    int count = query.value(0).toInt();
    return count > 0;
  }

  return false;
}

// 检查邮箱是否存在
bool DatabaseManager::checkEmailExists(const QString &email) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("SELECT COUNT(*) FROM users WHERE email = :email");

  // 绑定参数
  query.bindValue(":email", email);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR(
        "Database",
        QString("检查邮箱是否存在失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 检查结果
  if (query.next()) {
    int count = query.value(0).toInt();
    return count > 0;
  }

  return false;
}

// 添加文件
bool DatabaseManager::addFile(const FileInfo &fileInfo) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare(
      "INSERT INTO files (file_name, file_size, file_type, parent_id, "
      "owner_id, "
      "file_path, file_hash, create_time, modify_time, is_dir) "
      "VALUES (:file_name, :file_size, :file_type, :parent_id, :owner_id, "
      ":file_path, :file_hash, :create_time, :modify_time, :is_dir)");

  // 绑定参数
  query.bindValue(":file_name", fileInfo.fileName);
  query.bindValue(":file_size", fileInfo.fileSize);
  query.bindValue(":file_type", fileInfo.fileType);
  query.bindValue(":parent_id", fileInfo.parentId);
  query.bindValue(":owner_id", fileInfo.ownerId);
  query.bindValue(":file_path", fileInfo.filePath);
  query.bindValue(":file_hash", fileInfo.fileHash);
  query.bindValue(":create_time", fileInfo.createTime);
  query.bindValue(":modify_time", fileInfo.modifyTime);
  query.bindValue(":is_dir", fileInfo.isDir ? 1 : 0);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("添加文件失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database",
           QString("添加文件成功，文件名: %1").arg(fileInfo.fileName));
  return true;
}

// 获取文件信息
bool DatabaseManager::getFile(quint32 fileId, FileInfo &fileInfo) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare(
      "SELECT id, file_name, file_size, file_type, parent_id, owner_id, "
      "file_path, file_hash, create_time, modify_time, is_dir "
      "FROM files WHERE id = :file_id");

  // 绑定参数
  query.bindValue(":file_id", fileId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("获取文件信息失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 检查结果
  if (!query.next()) {
    LOG_WARN("Database", QString("文件不存在，文件ID: %1").arg(fileId));
    return false;
  }

  // 填充文件信息
  fileInfo.fileId = query.value("id").toUInt();
  fileInfo.fileName = query.value("file_name").toString();
  fileInfo.fileSize = query.value("file_size").toUInt();
  fileInfo.fileType = query.value("file_type").toString();
  fileInfo.parentId = query.value("parent_id").toUInt();
  fileInfo.ownerId = query.value("owner_id").toUInt();
  fileInfo.filePath = query.value("file_path").toString();
  fileInfo.fileHash = query.value("file_hash").toString();
  fileInfo.createTime = query.value("create_time").toString();
  fileInfo.modifyTime = query.value("modify_time").toString();
  fileInfo.isDir = query.value("is_dir").toInt() == 1;

  return true;
}

// 根据父目录ID获取文件列表
bool DatabaseManager::getFilesByParentId(quint32 parentId,
                                         QList<FileInfo> &fileList) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare(
      "SELECT id, file_name, file_size, file_type, parent_id, owner_id, "
      "file_path, file_hash, create_time, modify_time, is_dir "
      "FROM files WHERE parent_id = :parent_id ORDER BY is_dir DESC, file_name "
      "ASC");

  // 绑定参数
  query.bindValue(":parent_id", parentId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("获取文件列表失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 清空文件列表
  fileList.clear();

  // 填充文件列表
  while (query.next()) {
    FileInfo fileInfo;
    fileInfo.fileId = query.value("id").toUInt();
    fileInfo.fileName = query.value("file_name").toString();
    fileInfo.fileSize = query.value("file_size").toUInt();
    fileInfo.fileType = query.value("file_type").toString();
    fileInfo.parentId = query.value("parent_id").toUInt();
    fileInfo.ownerId = query.value("owner_id").toUInt();
    fileInfo.filePath = query.value("file_path").toString();
    fileInfo.fileHash = query.value("file_hash").toString();
    fileInfo.createTime = query.value("create_time").toString();
    fileInfo.modifyTime = query.value("modify_time").toString();
    fileInfo.isDir = query.value("is_dir").toInt() == 1;

    fileList.append(fileInfo);
  }

  return true;
}

// 根据用户ID获取文件列表
bool DatabaseManager::getFilesByUserId(quint32 userId,
                                       QList<FileInfo> &fileList) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare(
      "SELECT id, file_name, file_size, file_type, parent_id, owner_id, "
      "file_path, file_hash, create_time, modify_time, is_dir "
      "FROM files WHERE owner_id = :user_id ORDER BY is_dir DESC, file_name "
      "ASC");

  // 绑定参数
  query.bindValue(":user_id", userId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("获取文件列表失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 清空文件列表
  fileList.clear();

  // 填充文件列表
  while (query.next()) {
    FileInfo fileInfo;
    fileInfo.fileId = query.value("id").toUInt();
    fileInfo.fileName = query.value("file_name").toString();
    fileInfo.fileSize = query.value("file_size").toUInt();
    fileInfo.fileType = query.value("file_type").toString();
    fileInfo.parentId = query.value("parent_id").toUInt();
    fileInfo.ownerId = query.value("owner_id").toUInt();
    fileInfo.filePath = query.value("file_path").toString();
    fileInfo.fileHash = query.value("file_hash").toString();
    fileInfo.createTime = query.value("create_time").toString();
    fileInfo.modifyTime = query.value("modify_time").toString();
    fileInfo.isDir = query.value("is_dir").toInt() == 1;

    fileList.append(fileInfo);
  }

  return true;
}

// 更新文件信息
bool DatabaseManager::updateFile(const FileInfo &fileInfo) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare(
      "UPDATE files SET file_name = :file_name, file_size = :file_size, "
      "file_type = :file_type, parent_id = :parent_id, file_path = :file_path, "
      "file_hash = :file_hash, modify_time = :modify_time WHERE id = :file_id");

  // 绑定参数
  query.bindValue(":file_name", fileInfo.fileName);
  query.bindValue(":file_size", fileInfo.fileSize);
  query.bindValue(":file_type", fileInfo.fileType);
  query.bindValue(":parent_id", fileInfo.parentId);
  query.bindValue(":file_path", fileInfo.filePath);
  query.bindValue(":file_hash", fileInfo.fileHash);
  query.bindValue(":modify_time", fileInfo.modifyTime);
  query.bindValue(":file_id", fileInfo.fileId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("更新文件信息失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database",
           QString("更新文件信息成功，文件ID: %1").arg(fileInfo.fileId));
  return true;
}

// 删除文件
bool DatabaseManager::deleteFile(quint32 fileId) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("DELETE FROM files WHERE id = :file_id");

  // 绑定参数
  query.bindValue(":file_id", fileId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("删除文件失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database", QString("删除文件成功，文件ID: %1").arg(fileId));
  return true;
}

// 根据父目录ID删除文件
bool DatabaseManager::deleteFilesByParentId(quint32 parentId) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("DELETE FROM files WHERE parent_id = :parent_id");

  // 绑定参数
  query.bindValue(":parent_id", parentId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("删除文件失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database", QString("删除文件成功，父目录ID: %1").arg(parentId));
  return true;
}

// 检查文件是否存在
bool DatabaseManager::checkFileExists(quint32 parentId,
                                      const QString &fileName) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("SELECT COUNT(*) FROM files WHERE parent_id = :parent_id AND "
                "file_name = :file_name");

  // 绑定参数
  query.bindValue(":parent_id", parentId);
  query.bindValue(":file_name", fileName);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR(
        "Database",
        QString("检查文件是否存在失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 检查结果
  if (query.next()) {
    int count = query.value(0).toInt();
    return count > 0;
  }

  return false;
}

// 复制文件
bool DatabaseManager::copyFile(quint32 fileId, quint32 newParentId,
                               QString &newFileName) {
  QSqlQuery query(m_db);

  // 获取源文件信息
  FileInfo sourceFile;
  if (!getFile(fileId, sourceFile)) {
    LOG_ERROR("Database",
              QString("获取源文件信息失败，文件ID: %1").arg(fileId));
    return false;
  }

  // 检查新位置是否已存在同名文件
  QString originalName = sourceFile.fileName;
  QString baseName = originalName;
  QString extension;

  // 分离文件名和扩展名
  int dotPos = originalName.lastIndexOf('.');
  if (dotPos != -1) {
    baseName = originalName.left(dotPos);
    extension = originalName.mid(dotPos);
  }

  // 生成新的文件名（如果已存在则添加编号）
  newFileName = originalName;
  int counter = 1;

  while (checkFileExists(newParentId, newFileName)) {
    newFileName = QString("%2_%1%3").arg(counter).arg(baseName).arg(extension);
    counter++;
  }

  // 创建新文件记录
  FileInfo newFile = sourceFile;
  newFile.fileId = 0; // 数据库将自动分配新ID
  newFile.fileName = newFileName;
  newFile.parentId = newParentId;
  newFile.createTime = formatDateTime(QDateTime::currentDateTime());
  newFile.modifyTime = formatDateTime(QDateTime::currentDateTime());

  // 如果是目录，递归复制其内容
  if (sourceFile.isDir) {
    // 先添加目录记录
    if (!addFile(newFile)) {
      LOG_ERROR("Database",
                QString("添加目录失败，目录名: %1").arg(newFileName));
      return false;
    }

    // 获取源目录下的所有文件和子目录
    QList<FileInfo> children;
    if (!getFilesByParentId(fileId, children)) {
      LOG_ERROR("Database",
                QString("获取源目录内容失败，目录ID: %1").arg(fileId));
      return false;
    }

    // 递归复制子文件和子目录
    for (const FileInfo &child : children) {
      QString childNewName;
      if (!copyFile(child.fileId, newFile.fileId, childNewName)) {
        LOG_ERROR("Database",
                  QString("复制子文件失败，文件名: %1").arg(child.fileName));
        return false;
      }
    }
  } else {
    // 添加文件记录
    if (!addFile(newFile)) {
      LOG_ERROR("Database",
                QString("添加文件失败，文件名: %1").arg(newFileName));
      return false;
    }
  }

  LOG_INFO("Database",
           QString("复制文件成功，源文件ID: %1，新文件名: %2，新父目录ID: %3")
               .arg(fileId)
               .arg(newFileName)
               .arg(newParentId));

  return true;
}

// 移动文件
bool DatabaseManager::moveFile(quint32 fileId, quint32 newParentId) {
  QSqlQuery query(m_db);

  // 获取源文件信息
  FileInfo fileInfo;
  if (!getFile(fileId, fileInfo)) {
    LOG_ERROR("Database",
              QString("获取源文件信息失败，文件ID: %1").arg(fileId));
    return false;
  }

  // 检查新父目录是否存在
  if (newParentId != 0) {
    FileInfo newParentInfo;
    if (!getFile(newParentId, newParentInfo)) {
      LOG_ERROR("Database",
                QString("获取父目录信息失败，目录ID: %1").arg(newParentId));
      return false;
    }

    // 检查新父目录是否为目录
    if (!newParentInfo.isDir) {
      LOG_ERROR("Database",
                QString("新父目录不是目录，目录ID: %1").arg(newParentId));
      return false;
    }

    // 检查新父目录下是否已存在同名文件
    if (checkFileExists(newParentId, fileInfo.fileName)) {
      LOG_ERROR("Database", QString("新父目录下已存在同名文件，文件名: %1")
                                .arg(fileInfo.fileName));
      return false;
    }
  }

  // 更新文件信息
  fileInfo.parentId = newParentId;
  fileInfo.modifyTime = formatDateTime(QDateTime::currentDateTime());

  // 更新数据库
  if (!updateFile(fileInfo)) {
    LOG_ERROR("Database", QString("更新文件信息失败，文件ID: %1").arg(fileId));
    return false;
  }

  LOG_INFO("Database", QString("移动文件成功，文件ID: %1，新父目录ID: %2")
                           .arg(fileId)
                           .arg(newParentId));

  return true;
}

// 搜索文件
bool DatabaseManager::searchFiles(quint32 userId, const QString &keyword,
                                  quint32 parentId, QList<FileInfo> &fileList) {
  QSqlQuery query(m_db);

  // 构建SQL查询
  QString sql =
      "SELECT id, file_name, file_size, file_type, parent_id, owner_id, "
      "file_path, file_hash, create_time, modify_time, is_dir "
      "FROM files WHERE owner_id = :user_id AND file_name LIKE :keyword";

  // 如果指定了父目录，添加父目录条件
  if (parentId > 0) {
    sql += " AND parent_id = :parent_id";
  }

  // 添加排序条件
  sql += " ORDER BY is_dir DESC, file_name ASC";

  // 准备查询
  query.prepare(sql);

  // 绑定参数
  query.bindValue(":user_id", userId);
  query.bindValue(":keyword", "%" + keyword + "%");

  if (parentId > 0) {
    query.bindValue(":parent_id", parentId);
  }

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("搜索文件失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 清空文件列表
  fileList.clear();

  // 填充文件列表
  while (query.next()) {
    FileInfo fileInfo;
    fileInfo.fileId = query.value("id").toUInt();
    fileInfo.fileName = query.value("file_name").toString();
    fileInfo.fileSize = query.value("file_size").toUInt();
    fileInfo.fileType = query.value("file_type").toString();
    fileInfo.parentId = query.value("parent_id").toUInt();
    fileInfo.ownerId = query.value("owner_id").toUInt();
    fileInfo.filePath = query.value("file_path").toString();
    fileInfo.fileHash = query.value("file_hash").toString();
    fileInfo.createTime = query.value("create_time").toString();
    fileInfo.modifyTime = query.value("modify_time").toString();
    fileInfo.isDir = query.value("is_dir").toInt() == 1;

    fileList.append(fileInfo);
  }

  LOG_INFO("Database",
           QString("搜索文件完成，关键词: %1，父目录ID: %2，找到文件数: %3")
               .arg(keyword)
               .arg(parentId)
               .arg(fileList.size()));

  return true;
}

// 生成分享码
QString DatabaseManager::generateShareCode(quint32 fileId) {
  // 生成一个唯一的分享码，使用文件ID和时间戳的组合
  QString shareCode = QString("share_%1_%2")
                          .arg(fileId)
                          .arg(QDateTime::currentDateTime().toSecsSinceEpoch());

  // 计算哈希值作为分享码
  QCryptographicHash hash(QCryptographicHash::Sha256);
  hash.addData(shareCode.toUtf8());
  QString hashResult = hash.result().toHex();

  // 取前16位作为分享码
  return hashResult.left(16);
}

// 分享文件
bool DatabaseManager::shareFile(quint32 fileId, const QString &expireTime) {
  QSqlQuery query(m_db);

  // 获取文件信息
  FileInfo fileInfo;
  if (!getFile(fileId, fileInfo)) {
    LOG_ERROR("Database", QString("获取文件信息失败，文件ID: %1").arg(fileId));
    return false;
  }

  // 生成分享码
  QString shareCode = generateShareCode(fileId);

  // 创建分享记录
  ShareInfo shareInfo;
  shareInfo.shareCode = shareCode;
  shareInfo.fileId = fileId;
  shareInfo.ownerId = fileInfo.ownerId;
  shareInfo.createTime = formatDateTime(QDateTime::currentDateTime());
  shareInfo.expireTime = expireTime.isEmpty()
                             ? ""
                             : formatDateTime(QDateTime::fromString(
                                   expireTime, "yyyy-MM-dd hh:mm:ss"));
  shareInfo.downloadCount = 0;

  // 添加分享记录
  if (!addShare(shareInfo)) {
    LOG_ERROR("Database", QString("添加分享记录失败，文件ID: %1").arg(fileId));
    return false;
  }

  LOG_INFO("Database", QString("分享文件成功，文件ID: %1，分享码: %2")
                           .arg(fileId)
                           .arg(shareCode));

  return true;
}

// 根据文件ID获取分享信息
bool DatabaseManager::getShareByFileId(quint32 fileId, ShareInfo &shareInfo) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("SELECT share_code, file_id, owner_id, create_time, "
                "expire_time, download_count "
                "FROM file_shares WHERE file_id = :file_id AND (expire_time IS "
                "NULL OR expire_time > :current_time) "
                "ORDER BY create_time DESC LIMIT 1");

  // 绑定参数
  query.bindValue(":file_id", fileId);
  query.bindValue(":current_time",
                  formatDateTime(QDateTime::currentDateTime()));

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("获取分享信息失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 检查结果
  if (!query.next()) {
    LOG_WARN("Database",
             QString("未找到有效的分享记录，文件ID: %1").arg(fileId));
    return false;
  }

  // 填充分享信息
  shareInfo.shareCode = query.value("share_code").toString();
  shareInfo.fileId = query.value("file_id").toUInt();
  shareInfo.ownerId = query.value("owner_id").toUInt();
  shareInfo.createTime = query.value("create_time").toString();
  shareInfo.expireTime = query.value("expire_time").toString();
  shareInfo.downloadCount = query.value("download_count").toUInt();

  return true;
}

// 获取用户存储空间使用量
quint64 DatabaseManager::getUserStorageUsed(quint32 userId) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("SELECT storage_used FROM users WHERE id = :user_id");

  // 绑定参数
  query.bindValue(":user_id", userId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database", QString("获取用户存储空间使用量失败: %1")
                              .arg(query.lastError().text()));
    return 0;
  }

  // 检查结果
  if (query.next()) {
    return query.value("storage_used").toULongLong();
  }

  return 0;
}

// 添加好友关系
bool DatabaseManager::addFriendRelationship(quint32 userId, quint32 friendId) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare(
      "INSERT INTO friend_relationships (user_id, friend_id, add_time) "
      "VALUES (:user_id, :friend_id, :add_time)");

  // 绑定参数
  query.bindValue(":user_id", userId);
  query.bindValue(":friend_id", friendId);
  query.bindValue(":add_time", formatDateTime(QDateTime::currentDateTime()));

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("添加好友关系失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database", QString("添加好友关系成功，用户ID: %1，好友ID: %2")
                           .arg(userId)
                           .arg(friendId));
  return true;
}

// 移除好友关系
bool DatabaseManager::removeFriendRelationship(quint32 userId,
                                               quint32 friendId) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("DELETE FROM friend_relationships WHERE user_id = :user_id AND "
                "friend_id = :friend_id");

  // 绑定参数
  query.bindValue(":user_id", userId);
  query.bindValue(":friend_id", friendId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("移除好友关系失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database", QString("移除好友关系成功，用户ID: %1，好友ID: %2")
                           .arg(userId)
                           .arg(friendId));
  return true;
}

// 获取好友列表
bool DatabaseManager::getFriendList(quint32 userId,
                                    QList<FriendInfo> &friendList) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare(
      "SELECT fr.friend_id, u.username, u.email, fr.add_time, fr.remark "
      "FROM friend_relationships fr "
      "JOIN users u ON fr.friend_id = u.id "
      "WHERE fr.user_id = :user_id "
      "ORDER BY fr.add_time DESC");

  // 绑定参数
  query.bindValue(":user_id", userId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("获取好友列表失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 清空好友列表
  friendList.clear();

  // 填充好友列表
  while (query.next()) {
    FriendInfo friendInfo;
    friendInfo.friendId = query.value("friend_id").toUInt();
    friendInfo.username = query.value("username").toString();
    friendInfo.email = query.value("email").toString();
    friendInfo.addTime = query.value("add_time").toString();
    friendInfo.remark = query.value("remark").toString();

    // 检查好友是否在线
    QString sessionId;
    if (getSessionByUserId(friendInfo.friendId, sessionId)) {
      friendInfo.isOnline = true;
    } else {
      friendInfo.isOnline = false;
    }

    friendList.append(friendInfo);
  }

  return true;
}

// 检查好友关系是否存在
bool DatabaseManager::checkFriendRelationshipExists(quint32 userId,
                                                    quint32 friendId) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("SELECT COUNT(*) FROM friend_relationships "
                "WHERE user_id = :user_id AND friend_id = :friend_id");

  // 绑定参数
  query.bindValue(":user_id", userId);
  query.bindValue(":friend_id", friendId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR(
        "Database",
        QString("检查好友关系是否存在失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 检查结果
  if (query.next()) {
    int count = query.value(0).toInt();
    return count > 0;
  }

  return false;
}

// 添加消息
bool DatabaseManager::addMessage(const MessageInfo &messageInfo) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare(
      "INSERT INTO messages (sender_id, receiver_id, content, send_time, "
      "is_read) "
      "VALUES (:sender_id, :receiver_id, :content, :send_time, :is_read)");

  // 绑定参数
  query.bindValue(":sender_id", messageInfo.senderId);
  query.bindValue(":receiver_id", messageInfo.receiverId);
  query.bindValue(":content", messageInfo.content);
  query.bindValue(":send_time", messageInfo.sendTime);
  query.bindValue(":is_read", messageInfo.isRead ? 1 : 0);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("添加消息失败: %1").arg(query.lastError().text()));
    return false;
  }

  return true;
}

// 获取消息历史
bool DatabaseManager::getMessageHistory(quint32 userId, quint32 friendId,
                                        quint32 offset, quint32 count,
                                        QList<MessageInfo> &messageList) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare(
      "SELECT id, sender_id, receiver_id, content, send_time, is_read "
      "FROM messages "
      "WHERE ((sender_id = :user_id AND receiver_id = :friend_id) OR "
      "(sender_id = :friend_id AND receiver_id = :user_id)) "
      "ORDER BY send_time DESC "
      "LIMIT :limit OFFSET :offset");

  // 绑定参数
  query.bindValue(":user_id", userId);
  query.bindValue(":friend_id", friendId);
  query.bindValue(":limit", count);
  query.bindValue(":offset", offset);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("获取消息历史失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 清空消息列表
  messageList.clear();

  // 填充消息列表
  while (query.next()) {
    MessageInfo messageInfo;
    messageInfo.msgId = query.value("id").toUInt();
    messageInfo.senderId = query.value("sender_id").toUInt();
    messageInfo.receiverId = query.value("receiver_id").toUInt();
    messageInfo.content = query.value("content").toString();
    messageInfo.sendTime = query.value("send_time").toString();
    messageInfo.isRead = query.value("is_read").toInt() == 1;

    messageList.append(messageInfo);
  }

  // 反转列表，使消息按时间升序排列
  std::reverse(messageList.begin(), messageList.end());

  return true;
}

// 获取离线消息
bool DatabaseManager::getOfflineMessages(quint32 userId,
                                         QList<MessageInfo> &messageList) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare(
      "SELECT id, sender_id, receiver_id, content, send_time, is_read "
      "FROM messages "
      "WHERE receiver_id = :user_id AND is_read = 0 "
      "ORDER BY send_time ASC");

  // 绑定参数
  query.bindValue(":user_id", userId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("获取离线消息失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 清空消息列表
  messageList.clear();

  // 填充消息列表
  while (query.next()) {
    MessageInfo messageInfo;
    messageInfo.msgId = query.value("id").toUInt();
    messageInfo.senderId = query.value("sender_id").toUInt();
    messageInfo.receiverId = query.value("receiver_id").toUInt();
    messageInfo.content = query.value("content").toString();
    messageInfo.sendTime = query.value("send_time").toString();
    messageInfo.isRead = query.value("is_read").toInt() == 1;

    messageList.append(messageInfo);
  }

  return true;
}

// 标记消息为已读
bool DatabaseManager::markMessagesAsRead(quint32 userId, quint32 friendId) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("UPDATE messages SET is_read = 1 "
                "WHERE receiver_id = :user_id AND sender_id = :friend_id AND "
                "is_read = 0");

  // 绑定参数
  query.bindValue(":user_id", userId);
  query.bindValue(":friend_id", friendId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("标记消息为已读失败: %1").arg(query.lastError().text()));
    return false;
  }

  return true;
}

// 添加会话
bool DatabaseManager::addSession(quint32 userId, const QString &sessionId) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("INSERT INTO sessions (session_id, user_id, last_heartbeat) "
                "VALUES (:session_id, :user_id, :last_heartbeat)");

  // 绑定参数
  query.bindValue(":session_id", sessionId);
  query.bindValue(":user_id", userId);
  query.bindValue(":last_heartbeat",
                  formatDateTime(QDateTime::currentDateTime()));

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("添加会话失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database", QString("添加会话成功，用户ID: %1，会话ID: %2")
                           .arg(userId)
                           .arg(sessionId));
  return true;
}

// 移除会话
bool DatabaseManager::removeSession(const QString &sessionId) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("DELETE FROM sessions WHERE session_id = :session_id");

  // 绑定参数
  query.bindValue(":session_id", sessionId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("移除会话失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database", QString("移除会话成功，会话ID: %1").arg(sessionId));
  return true;
}

// 获取会话
bool DatabaseManager::getSession(const QString &sessionId, quint32 &userId) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("SELECT user_id FROM sessions WHERE session_id = :session_id");

  // 绑定参数
  query.bindValue(":session_id", sessionId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("获取会话失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 检查结果
  if (query.next()) {
    userId = query.value("user_id").toUInt();
    return true;
  }

  return false;
}

// 根据用户ID获取会话
bool DatabaseManager::getSessionByUserId(quint32 userId, QString &sessionId) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("SELECT session_id FROM sessions WHERE user_id = :user_id");

  // 绑定参数
  query.bindValue(":user_id", userId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("获取会话失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 检查结果
  if (query.next()) {
    sessionId = query.value("session_id").toString();
    return true;
  }

  return false;
}

// 更新会话心跳时间
bool DatabaseManager::updateSessionHeartbeat(const QString &sessionId) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("UPDATE sessions SET last_heartbeat = :last_heartbeat "
                "WHERE session_id = :session_id");

  // 绑定参数
  query.bindValue(":last_heartbeat",
                  formatDateTime(QDateTime::currentDateTime()));
  query.bindValue(":session_id", sessionId);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR(
        "Database",
        QString("更新会话心跳时间失败: %1").arg(query.lastError().text()));
    return false;
  }

  return true;
}

// 移除过期会话
bool DatabaseManager::removeExpiredSessions(int timeoutSeconds) {
  QSqlQuery query(m_db);

  // 计算过期时间
  QDateTime expireTime = QDateTime::currentDateTime().addSecs(-timeoutSeconds);

  // 准备SQL语句
  query.prepare("DELETE FROM sessions WHERE last_heartbeat < :expire_time");

  // 绑定参数
  query.bindValue(":expire_time", formatDateTime(expireTime));

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("移除过期会话失败: %1").arg(query.lastError().text()));
    return false;
  }

  int count = query.numRowsAffected();
  if (count > 0) {
    LOG_INFO("Database", QString("移除过期会话成功，数量: %1").arg(count));
  }

  return true;
}

// 添加分享
bool DatabaseManager::addShare(const ShareInfo &shareInfo) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare(
      "INSERT INTO file_shares (share_code, file_id, owner_id, create_time, "
      "expire_time) "
      "VALUES (:share_code, :file_id, :owner_id, :create_time, :expire_time)");

  // 绑定参数
  query.bindValue(":share_code", shareInfo.shareCode);
  query.bindValue(":file_id", shareInfo.fileId);
  query.bindValue(":owner_id", shareInfo.ownerId);
  query.bindValue(":create_time", shareInfo.createTime);
  query.bindValue(":expire_time", shareInfo.expireTime);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("添加分享失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database",
           QString("添加分享成功，分享码: %1").arg(shareInfo.shareCode));
  return true;
}

// 获取分享
bool DatabaseManager::getShare(const QString &shareCode, ShareInfo &shareInfo) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("SELECT share_code, file_id, owner_id, create_time, "
                "expire_time, download_count "
                "FROM file_shares WHERE share_code = :share_code");

  // 绑定参数
  query.bindValue(":share_code", shareCode);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("获取分享失败: %1").arg(query.lastError().text()));
    return false;
  }

  // 检查结果
  if (!query.next()) {
    LOG_WARN("Database", QString("分享不存在，分享码: %1").arg(shareCode));
    return false;
  }

  // 填充分享信息
  shareInfo.shareCode = query.value("share_code").toString();
  shareInfo.fileId = query.value("file_id").toUInt();
  shareInfo.ownerId = query.value("owner_id").toUInt();
  shareInfo.createTime = query.value("create_time").toString();
  shareInfo.expireTime = query.value("expire_time").toString();
  shareInfo.downloadCount = query.value("download_count").toUInt();

  // 检查分享是否过期
  if (!shareInfo.expireTime.isEmpty()) {
    QDateTime expireDateTime = parseDateTime(shareInfo.expireTime);
    if (expireDateTime.isValid() &&
        expireDateTime < QDateTime::currentDateTime()) {
      LOG_WARN("Database", QString("分享已过期，分享码: %1").arg(shareCode));
      return false;
    }
  }

  return true;
}

// 移除分享
bool DatabaseManager::removeShare(const QString &shareCode) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("DELETE FROM file_shares WHERE share_code = :share_code");

  // 绑定参数
  query.bindValue(":share_code", shareCode);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("移除分享失败: %1").arg(query.lastError().text()));
    return false;
  }

  LOG_INFO("Database", QString("移除分享成功，分享码: %1").arg(shareCode));
  return true;
}

// 移除过期分享
bool DatabaseManager::removeExpiredShares() {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("DELETE FROM file_shares WHERE expire_time IS NOT NULL AND "
                "expire_time < :current_time");

  // 绑定参数
  query.bindValue(":current_time",
                  formatDateTime(QDateTime::currentDateTime()));

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR("Database",
              QString("移除过期分享失败: %1").arg(query.lastError().text()));
    return false;
  }

  int count = query.numRowsAffected();
  if (count > 0) {
    LOG_INFO("Database", QString("移除过期分享成功，数量: %1").arg(count));
  }

  return true;
}

// 增加分享下载次数
bool DatabaseManager::incrementShareDownloadCount(const QString &shareCode) {
  QSqlQuery query(m_db);

  // 准备SQL语句
  query.prepare("UPDATE file_shares SET download_count = download_count + 1 "
                "WHERE share_code = :share_code");

  // 绑定参数
  query.bindValue(":share_code", shareCode);

  // 执行查询
  if (!query.exec()) {
    LOG_ERROR(
        "Database",
        QString("增加分享下载次数失败: %1").arg(query.lastError().text()));
    return false;
  }

  return true;
}

// 计算密码哈希
QString DatabaseManager::calculatePasswordHash(const QString &password,
                                               const QString &salt) {
  QString saltedPassword = password + salt;
  QByteArray hash = QCryptographicHash::hash(saltedPassword.toUtf8(),
                                             QCryptographicHash::Md5);
  return hash.toHex();
}

// 生成随机盐值
QString DatabaseManager::generateSalt() {
  const QString possibleCharacters(
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789");
  const int randomStringLength = 8; // 盐值长度

  QString randomString;
  for (int i = 0; i < randomStringLength; ++i) {
    int index =
        QRandomGenerator::global()->bounded(0, possibleCharacters.length());
    QChar nextChar = possibleCharacters.at(index);
    randomString.append(nextChar);
  }

  return randomString;
}

// 执行查询并检查错误
bool DatabaseManager::executeQuery(QSqlQuery &query, const QString &sql) {
  // 执行查询
  if (!query.exec(sql)) {
    LOG_ERROR("Database", QString("执行查询失败: %1，错误: %2")
                              .arg(sql)
                              .arg(query.lastError().text()));
    return false;
  }

  return true;
}

// 格式化日期时间
QString DatabaseManager::formatDateTime(const QDateTime &dateTime) const {
  return dateTime.toString("yyyy-MM-dd hh:mm:ss");
}

// 解析日期时间
QDateTime DatabaseManager::parseDateTime(const QString &dateTimeStr) const {
  return QDateTime::fromString(dateTimeStr, "yyyy-MM-dd hh:mm:ss");
}
