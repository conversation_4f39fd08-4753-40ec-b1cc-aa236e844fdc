<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FileSearchDialog</class>
 <widget class="QDialog" name="FileSearchDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>文件搜索</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icons/search.png</normaloff>:/icons/search.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_search">
     <item>
      <widget class="QLineEdit" name="lineEdit_keyword">
       <property name="placeholderText">
        <string>输入文件名进行搜索</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_search">
       <property name="text">
        <string>搜索</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/search.png</normaloff>:/icons/search.png</iconset>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QListWidget" name="listWidget_results">
     <property name="selectionMode">
      <enum>QAbstractItemView::SingleSelection</enum>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectRows</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_buttons">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_close">
       <property name="text">
        <string>关闭</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/close.png</normaloff>:/icons/close.png</iconset>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
