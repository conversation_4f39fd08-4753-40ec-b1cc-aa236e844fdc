#include <QApplication>
#include "loginwindow.h"
#include "configmanager.h"
#include "logger.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 设置应用程序信息
    app.setApplicationName("Cloud7");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("Cloud7Team");

    // 初始化配置管理器
    ConfigManager::getInstance()->loadConfig();

    // 初始化日志系统
    Logger::getInstance()->setLogLevel(LOG_INFO);

    // 创建并显示登录窗口
    LoginWindow loginWindow;
    loginWindow.show();

    // 运行应用程序
    return app.exec();
}
