/****************************************************************************
** Meta object code from reading C++ file 'fileviewwidget.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../fileviewwidget.h"
#include <QtCore/qmetatype.h>
#include <QtCore/QList>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'fileviewwidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN14FileViewWidgetE_t {};
} // unnamed namespace

template <> constexpr inline auto FileViewWidget::qt_create_metaobjectdata<qt_meta_tag_ZN14FileViewWidgetE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "FileViewWidget",
        "fileSelected",
        "",
        "FileInfo",
        "fileInfo",
        "openDirectoryRequested",
        "parentId",
        "title",
        "uploadFileRequested",
        "filePath",
        "downloadFileRequested",
        "fileId",
        "newFolderRequested",
        "folderName",
        "deleteFileRequested",
        "renameFileRequested",
        "newName",
        "moveFileRequested",
        "newParentId",
        "searchFileRequested",
        "keyword",
        "shareFileRequested",
        "on_listView_doubleClicked",
        "QModelIndex",
        "index",
        "on_iconView_doubleClicked",
        "on_listView_clicked",
        "on_iconView_clicked",
        "on_pushButton_refresh_clicked",
        "on_pushButton_upload_clicked",
        "on_pushButton_download_clicked",
        "on_pushButton_new_folder_clicked",
        "on_pushButton_delete_clicked",
        "on_pushButton_rename_clicked",
        "on_pushButton_search_clicked",
        "handleFileDrop",
        "QList<QUrl>",
        "urls",
        "onSearchFileRequested",
        "onOpenDirectoryRequested",
        "dirName",
        "onDownloadFileRequested",
        "onShareFileRequested",
        "onShareFileCompleted",
        "success",
        "message",
        "shareCode"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'fileSelected'
        QtMocHelpers::SignalData<void(const FileInfo &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 4 },
        }}),
        // Signal 'openDirectoryRequested'
        QtMocHelpers::SignalData<void(quint32, const QString &)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 6 }, { QMetaType::QString, 7 },
        }}),
        // Signal 'uploadFileRequested'
        QtMocHelpers::SignalData<void(const QString &)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 9 },
        }}),
        // Signal 'downloadFileRequested'
        QtMocHelpers::SignalData<void(quint32)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 11 },
        }}),
        // Signal 'newFolderRequested'
        QtMocHelpers::SignalData<void(const QString &)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 13 },
        }}),
        // Signal 'deleteFileRequested'
        QtMocHelpers::SignalData<void(quint32)>(14, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 11 },
        }}),
        // Signal 'renameFileRequested'
        QtMocHelpers::SignalData<void(quint32, const QString &)>(15, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 11 }, { QMetaType::QString, 16 },
        }}),
        // Signal 'moveFileRequested'
        QtMocHelpers::SignalData<void(quint32, quint32)>(17, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 11 }, { QMetaType::UInt, 18 },
        }}),
        // Signal 'searchFileRequested'
        QtMocHelpers::SignalData<void(const QString &)>(19, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 20 },
        }}),
        // Signal 'shareFileRequested'
        QtMocHelpers::SignalData<void(quint32)>(21, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 11 },
        }}),
        // Slot 'on_listView_doubleClicked'
        QtMocHelpers::SlotData<void(const QModelIndex &)>(22, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 23, 24 },
        }}),
        // Slot 'on_iconView_doubleClicked'
        QtMocHelpers::SlotData<void(const QModelIndex &)>(25, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 23, 24 },
        }}),
        // Slot 'on_listView_clicked'
        QtMocHelpers::SlotData<void(const QModelIndex &)>(26, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 23, 24 },
        }}),
        // Slot 'on_iconView_clicked'
        QtMocHelpers::SlotData<void(const QModelIndex &)>(27, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 23, 24 },
        }}),
        // Slot 'on_pushButton_refresh_clicked'
        QtMocHelpers::SlotData<void()>(28, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButton_upload_clicked'
        QtMocHelpers::SlotData<void()>(29, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButton_download_clicked'
        QtMocHelpers::SlotData<void()>(30, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButton_new_folder_clicked'
        QtMocHelpers::SlotData<void()>(31, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButton_delete_clicked'
        QtMocHelpers::SlotData<void()>(32, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButton_rename_clicked'
        QtMocHelpers::SlotData<void()>(33, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButton_search_clicked'
        QtMocHelpers::SlotData<void()>(34, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'handleFileDrop'
        QtMocHelpers::SlotData<void(const QList<QUrl> &)>(35, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 36, 37 },
        }}),
        // Slot 'onSearchFileRequested'
        QtMocHelpers::SlotData<void(const QString &)>(38, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 20 },
        }}),
        // Slot 'onOpenDirectoryRequested'
        QtMocHelpers::SlotData<void(quint32, const QString &)>(39, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::UInt, 11 }, { QMetaType::QString, 40 },
        }}),
        // Slot 'onDownloadFileRequested'
        QtMocHelpers::SlotData<void(quint32)>(41, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::UInt, 11 },
        }}),
        // Slot 'onShareFileRequested'
        QtMocHelpers::SlotData<void(quint32)>(42, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::UInt, 11 },
        }}),
        // Slot 'onShareFileCompleted'
        QtMocHelpers::SlotData<void(bool, const QString &, const QString &)>(43, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 44 }, { QMetaType::QString, 45 }, { QMetaType::QString, 46 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<FileViewWidget, qt_meta_tag_ZN14FileViewWidgetE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject FileViewWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14FileViewWidgetE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14FileViewWidgetE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN14FileViewWidgetE_t>.metaTypes,
    nullptr
} };

void FileViewWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<FileViewWidget *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->fileSelected((*reinterpret_cast< std::add_pointer_t<FileInfo>>(_a[1]))); break;
        case 1: _t->openDirectoryRequested((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 2: _t->uploadFileRequested((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->downloadFileRequested((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1]))); break;
        case 4: _t->newFolderRequested((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 5: _t->deleteFileRequested((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1]))); break;
        case 6: _t->renameFileRequested((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 7: _t->moveFileRequested((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<quint32>>(_a[2]))); break;
        case 8: _t->searchFileRequested((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 9: _t->shareFileRequested((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1]))); break;
        case 10: _t->on_listView_doubleClicked((*reinterpret_cast< std::add_pointer_t<QModelIndex>>(_a[1]))); break;
        case 11: _t->on_iconView_doubleClicked((*reinterpret_cast< std::add_pointer_t<QModelIndex>>(_a[1]))); break;
        case 12: _t->on_listView_clicked((*reinterpret_cast< std::add_pointer_t<QModelIndex>>(_a[1]))); break;
        case 13: _t->on_iconView_clicked((*reinterpret_cast< std::add_pointer_t<QModelIndex>>(_a[1]))); break;
        case 14: _t->on_pushButton_refresh_clicked(); break;
        case 15: _t->on_pushButton_upload_clicked(); break;
        case 16: _t->on_pushButton_download_clicked(); break;
        case 17: _t->on_pushButton_new_folder_clicked(); break;
        case 18: _t->on_pushButton_delete_clicked(); break;
        case 19: _t->on_pushButton_rename_clicked(); break;
        case 20: _t->on_pushButton_search_clicked(); break;
        case 21: _t->handleFileDrop((*reinterpret_cast< std::add_pointer_t<QList<QUrl>>>(_a[1]))); break;
        case 22: _t->onSearchFileRequested((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 23: _t->onOpenDirectoryRequested((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 24: _t->onDownloadFileRequested((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1]))); break;
        case 25: _t->onShareFileRequested((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1]))); break;
        case 26: _t->onShareFileCompleted((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 21:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QList<QUrl> >(); break;
            }
            break;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (FileViewWidget::*)(const FileInfo & )>(_a, &FileViewWidget::fileSelected, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileViewWidget::*)(quint32 , const QString & )>(_a, &FileViewWidget::openDirectoryRequested, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileViewWidget::*)(const QString & )>(_a, &FileViewWidget::uploadFileRequested, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileViewWidget::*)(quint32 )>(_a, &FileViewWidget::downloadFileRequested, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileViewWidget::*)(const QString & )>(_a, &FileViewWidget::newFolderRequested, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileViewWidget::*)(quint32 )>(_a, &FileViewWidget::deleteFileRequested, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileViewWidget::*)(quint32 , const QString & )>(_a, &FileViewWidget::renameFileRequested, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileViewWidget::*)(quint32 , quint32 )>(_a, &FileViewWidget::moveFileRequested, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileViewWidget::*)(const QString & )>(_a, &FileViewWidget::searchFileRequested, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileViewWidget::*)(quint32 )>(_a, &FileViewWidget::shareFileRequested, 9))
            return;
    }
}

const QMetaObject *FileViewWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *FileViewWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14FileViewWidgetE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int FileViewWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 27)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 27;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 27)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 27;
    }
    return _id;
}

// SIGNAL 0
void FileViewWidget::fileSelected(const FileInfo & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void FileViewWidget::openDirectoryRequested(quint32 _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1, _t2);
}

// SIGNAL 2
void FileViewWidget::uploadFileRequested(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void FileViewWidget::downloadFileRequested(quint32 _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void FileViewWidget::newFolderRequested(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}

// SIGNAL 5
void FileViewWidget::deleteFileRequested(quint32 _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}

// SIGNAL 6
void FileViewWidget::renameFileRequested(quint32 _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1, _t2);
}

// SIGNAL 7
void FileViewWidget::moveFileRequested(quint32 _t1, quint32 _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 7, nullptr, _t1, _t2);
}

// SIGNAL 8
void FileViewWidget::searchFileRequested(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 8, nullptr, _t1);
}

// SIGNAL 9
void FileViewWidget::shareFileRequested(quint32 _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 9, nullptr, _t1);
}
QT_WARNING_POP
