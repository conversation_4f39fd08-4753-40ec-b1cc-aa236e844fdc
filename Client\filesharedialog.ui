<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FileShareDialog</class>
 <widget class="QDialog" name="FileShareDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>500</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>文件分享</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icons/share.png</normaloff>:/icons/share.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QFormLayout" name="formLayout">
     <property name="horizontalSpacing">
      <number>20</number>
     </property>
     <property name="verticalSpacing">
      <number>15</number>
     </property>
     <item row="0" column="0">
      <widget class="QLabel" name="label_file_name">
       <property name="text">
        <string>文件名：</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLabel" name="label_file_name_value">
       <property name="text">
        <string>-</string>
       </property>
       <property name="wordWrap">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="label_file_size">
       <property name="text">
        <string>文件大小：</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QLabel" name="label_file_size_value">
       <property name="text">
        <string>-</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="label_share_code">
       <property name="text">
        <string>分享码：</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QLineEdit" name="lineEdit_share_code">
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="3" column="0">
      <widget class="QLabel" name="label_expire_time">
       <property name="text">
        <string>有效期：</string>
       </property>
      </widget>
     </item>
     <item row="3" column="1">
      <widget class="QComboBox" name="comboBox_expire_time">
       <item>
        <property name="text">
         <string>永久有效</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>1天</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>3天</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>7天</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>30天</string>
        </property>
       </item>
      </widget>
     </item>
     <item row="4" column="0" colspan="2">
      <widget class="QLabel" name="label_share_hint">
       <property name="text">
        <string>分享码有效期：永久有效或指定天数后失效</string>
       </property>
       <property name="wordWrap">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_buttons">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_share">
       <property name="text">
        <string>分享</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/share.png</normaloff>:/icons/share.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_copy">
       <property name="text">
        <string>复制</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/copy.png</normaloff>:/icons/copy.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_close">
       <property name="text">
        <string>关闭</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/close.png</normaloff>:/icons/close.png</iconset>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
