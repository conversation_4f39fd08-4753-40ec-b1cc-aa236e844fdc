#include "logger.h"

// 初始化静态成员变量
Logger* Logger::m_instance = nullptr;
QMutex Logger::m_mutex;

// 获取单例实例
Logger* Logger::getInstance()
{
    if (m_instance == nullptr) {
        QMutexLocker locker(&m_mutex);
        if (m_instance == nullptr) {
            m_instance = new Logger();
        }
    }
    return m_instance;
}

// 私有构造函数
Logger::Logger(QObject *parent) : QObject(parent), m_logLevel(LOG_INFO)
{
    // 初始化日志系统
}

// 私有析构函数
Logger::~Logger()
{
    // 清理资源
}

// 设置日志级别
void Logger::setLogLevel(LogLevel level)
{
    m_logLevel = level;
}

// 记录调试级别的日志
void Logger::debug(const QString& module, const QString& message)
{
    log(LOG_DEBUG, module, message);
}

// 记录信息级别的日志
void Logger::info(const QString& module, const QString& message)
{
    log(LOG_INFO, module, message);
}

// 记录警告级别的日志
void Logger::warn(const QString& module, const QString& message)
{
    log(LOG_WARN, module, message);
}

// 记录错误级别的日志
void Logger::error(const QString& module, const QString& message)
{
    log(LOG_ERROR, module, message);
}

// 日志输出方法
void Logger::log(LogLevel level, const QString& module, const QString& message)
{
    // 如果日志级别低于当前设置的级别，则不输出
    if (level < m_logLevel) {
        return;
    }

    // 格式化日志消息
    QString formattedMessage = formatMessage(level, module, message);

    // 输出到控制台
    qDebug() << formattedMessage;
}

// 将日志级别转换为字符串
QString Logger::levelToString(LogLevel level)
{
    switch (level) {
    case LOG_DEBUG:
        return "DEBUG";
    case LOG_INFO:
        return "INFO";
    case LOG_WARN:
        return "WARN";
    case LOG_ERROR:
        return "ERROR";
    default:
        return "UNKNOWN";
    }
}

// 格式化日志消息
QString Logger::formatMessage(LogLevel level, const QString& module, const QString& message)
{
    // 获取当前时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    QString timeStr = currentDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz");

    // 格式化日志消息
    return QString("[%1] [%2] [%3] %4").arg(timeStr, levelToString(level), module, message);
}
