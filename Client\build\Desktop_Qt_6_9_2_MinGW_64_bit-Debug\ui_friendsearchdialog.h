/********************************************************************************
** Form generated from reading UI file 'friendsearchdialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FRIENDSEARCHDIALOG_H
#define UI_FRIENDSEARCHDIALOG_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_FriendSearchDialog
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout_search;
    QLineEdit *lineEdit_search;
    QPushButton *pushButton_search;
    QTableWidget *tableWidget_users;
    QHBoxLayout *horizontalLayout_buttons;
    QSpacerItem *horizontalSpacer;
    QPushButton *pushButton_add_friend;
    QPushButton *pushButton_close;

    void setupUi(QDialog *FriendSearchDialog)
    {
        if (FriendSearchDialog->objectName().isEmpty())
            FriendSearchDialog->setObjectName("FriendSearchDialog");
        FriendSearchDialog->resize(500, 400);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/search.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        FriendSearchDialog->setWindowIcon(icon);
        verticalLayout = new QVBoxLayout(FriendSearchDialog);
        verticalLayout->setObjectName("verticalLayout");
        horizontalLayout_search = new QHBoxLayout();
        horizontalLayout_search->setObjectName("horizontalLayout_search");
        lineEdit_search = new QLineEdit(FriendSearchDialog);
        lineEdit_search->setObjectName("lineEdit_search");

        horizontalLayout_search->addWidget(lineEdit_search);

        pushButton_search = new QPushButton(FriendSearchDialog);
        pushButton_search->setObjectName("pushButton_search");
        pushButton_search->setIcon(icon);

        horizontalLayout_search->addWidget(pushButton_search);


        verticalLayout->addLayout(horizontalLayout_search);

        tableWidget_users = new QTableWidget(FriendSearchDialog);
        if (tableWidget_users->columnCount() < 4)
            tableWidget_users->setColumnCount(4);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        tableWidget_users->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        tableWidget_users->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        tableWidget_users->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        tableWidget_users->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        tableWidget_users->setObjectName("tableWidget_users");
        tableWidget_users->setSelectionMode(QAbstractItemView::SingleSelection);
        tableWidget_users->setSelectionBehavior(QAbstractItemView::SelectRows);

        verticalLayout->addWidget(tableWidget_users);

        horizontalLayout_buttons = new QHBoxLayout();
        horizontalLayout_buttons->setObjectName("horizontalLayout_buttons");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer);

        pushButton_add_friend = new QPushButton(FriendSearchDialog);
        pushButton_add_friend->setObjectName("pushButton_add_friend");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/add_friend.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_add_friend->setIcon(icon1);

        horizontalLayout_buttons->addWidget(pushButton_add_friend);

        pushButton_close = new QPushButton(FriendSearchDialog);
        pushButton_close->setObjectName("pushButton_close");
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/close.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_close->setIcon(icon2);

        horizontalLayout_buttons->addWidget(pushButton_close);


        verticalLayout->addLayout(horizontalLayout_buttons);


        retranslateUi(FriendSearchDialog);

        QMetaObject::connectSlotsByName(FriendSearchDialog);
    } // setupUi

    void retranslateUi(QDialog *FriendSearchDialog)
    {
        FriendSearchDialog->setWindowTitle(QCoreApplication::translate("FriendSearchDialog", "\345\245\275\345\217\213\346\220\234\347\264\242", nullptr));
        lineEdit_search->setPlaceholderText(QCoreApplication::translate("FriendSearchDialog", "\350\276\223\345\205\245\347\224\250\346\210\267\345\220\215\346\210\226\351\202\256\347\256\261\350\277\233\350\241\214\346\220\234\347\264\242", nullptr));
        pushButton_search->setText(QCoreApplication::translate("FriendSearchDialog", "\346\220\234\347\264\242", nullptr));
        QTableWidgetItem *___qtablewidgetitem = tableWidget_users->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("FriendSearchDialog", "\347\224\250\346\210\267\345\220\215", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget_users->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("FriendSearchDialog", "\351\202\256\347\256\261", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = tableWidget_users->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QCoreApplication::translate("FriendSearchDialog", "\346\263\250\345\206\214\346\227\266\351\227\264", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = tableWidget_users->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QCoreApplication::translate("FriendSearchDialog", "\346\223\215\344\275\234", nullptr));
        pushButton_add_friend->setText(QCoreApplication::translate("FriendSearchDialog", "\346\267\273\345\212\240\345\245\275\345\217\213", nullptr));
        pushButton_close->setText(QCoreApplication::translate("FriendSearchDialog", "\345\205\263\351\227\255", nullptr));
    } // retranslateUi

};

namespace Ui {
    class FriendSearchDialog: public Ui_FriendSearchDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FRIENDSEARCHDIALOG_H
