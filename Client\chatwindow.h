#ifndef CHATWINDOW_H
#define CHATWINDOW_H

#include <QMainWindow>
#include "ui_chatwindow.h"
#include "../Common/common.h"

namespace Ui {
class ChatWindow;
}

class ChatWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit ChatWindow(QWidget *parent = nullptr);
    ~ChatWindow();

    // 设置好友ID
    void setFriendId(quint32 friendId);

    // 获取好友ID
    quint32 friendId() const;

    // 设置好友名称
    void setFriendName(const QString& friendName);

    // 获取好友名称
    QString friendName() const;

    // 设置用户ID
    void setUserId(quint32 userId);

    // 获取用户ID
    quint32 userId() const;

    // 设置用户名称
    void setUserName(const QString& userName);

    // 获取用户名称
    QString userName() const;

    // 添加消息
    void addMessage(const MessageInfo& messageInfo);

    // 设置历史消息
    void setHistoryMessages(const QList<MessageInfo>& messageList);

signals:
    // 发送消息请求信号
    void sendMessageRequested(quint32 receiverId, const QString& content);

    // 获取历史消息请求信号
    void getMessageHistoryRequested(quint32 friendId, quint32 offset = 0, quint32 count = 20);

private slots:
    // 发送按钮点击槽函数
    void on_pushButton_send_clicked();

    // 消息输入框回车键按下槽函数
    void on_lineEdit_message_returnPressed();

private:
    // 初始化UI
    void initUI();

    // 格式化消息时间
    QString formatMessageTime(const QString& timeStr) const;

    Ui::ChatWindow *ui;          // UI对象
    quint32 m_friendId;           // 好友ID
    QString m_friendName;         // 好友名称
    quint32 m_userId;             // 用户ID
    QString m_userName;           // 用户名称
};

#endif // CHATWINDOW_H
