#ifndef FILESEARCHDIALOG_H
#define FILESEARCHDIALOG_H

#include "../Common/common.h"
#include <QDialog>
#include <QListWidgetItem>
#include <QMessageBox>

namespace Ui {
class FileSearchDialog;
}

class FileSearchDialog : public QDialog {
  Q_OBJECT

public:
  explicit FileSearchDialog(QWidget *parent = nullptr);
  ~FileSearchDialog();

  // 设置用户ID
  void setUserId(quint32 userId);

  // 获取用户ID
  quint32 userId() const;

  // 设置父目录ID
  void setParentId(quint32 parentId);

  // 获取父目录ID
  quint32 parentId() const;

  // 设置搜索结果
  void setSearchResults(const QList<FileInfo> &searchResults);

signals:
  // 搜索文件请求信号
  void searchFileRequested(const QString &keyword, quint32 parentId);

  // 打开目录请求信号
  void openDirectoryRequested(quint32 fileId, const QString &dirName);

  // 下载文件请求信号
  void downloadFileRequested(quint32 fileId);

private slots:
  // 搜索按钮点击槽函数
  void on_pushButton_search_clicked();

  // 搜索结果双击槽函数
  void on_listWidget_results_itemDoubleClicked(QListWidgetItem *item);

  // 关闭按钮点击槽函数
  void on_pushButton_close_clicked();

private:
  // 初始化UI
  void initUI();

  // 加载搜索结果
  void loadSearchResults(const QList<FileInfo> &searchResults);

  // 获取文件图标
  QString getFileIcon(const QString &fileType) const;

  Ui::FileSearchDialog *ui;        // UI对象
  quint32 m_userId;                // 用户ID
  quint32 m_parentId;              // 父目录ID
  QList<FileInfo> m_searchResults; // 搜索结果
};

#endif // FILESEARCHDIALOG_H
