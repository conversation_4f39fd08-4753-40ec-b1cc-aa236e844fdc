#include "settingsdialog.h"
#include "logger.h"
#include <QDialogButtonBox>
#include <QDir>
#include <QIcon>
#include <QMessageBox>
#include <QSettings>
#include <QStandardPaths>
#include <QWidget>

// 构造函数
SettingsDialog::SettingsDialog(QWidget *parent)
    : QDialog(parent), ui(new Ui::SettingsDialog), m_configChanged(false) {
  // 初始化UI
  ui->setupUi(this);

  // 设置窗口标题
  setWindowTitle("设置");

  // 设置窗口图标
  setWindowIcon(QIcon(":/icons/settings.png"));

  // 初始化UI
  initUI();

  // 加载配置
  loadConfig();

  // 连接信号槽
  connect(ui->buttonBox, &QDialogButtonBox::accepted, this,
          &SettingsDialog::on_buttonBox_accepted);
  connect(ui->buttonBox, &QDialogButtonBox::rejected, this,
          &SettingsDialog::on_buttonBox_rejected);
  // 注释掉不存在的控件 - 这些控件在UI文件中不存在
  // connect(ui->lineEdit_serverHost, &QLineEdit::textChanged, this,
  // &SettingsDialog::on_lineEdit_serverHost_textChanged);
  // connect(ui->spinBox_serverPort,
  // QOverload<int>::of(&QSpinBox::valueChanged), this,
  // &SettingsDialog::on_spinBox_serverPort_valueChanged);
  // connect(ui->checkBox_rememberUsername, &QCheckBox::stateChanged, this,
  // &SettingsDialog::on_checkBox_rememberUsername_stateChanged);
  // connect(ui->checkBox_autoLogin, &QCheckBox::stateChanged, this,
  // &SettingsDialog::on_checkBox_autoLogin_stateChanged);

  // 注释掉不存在的控件
  // connect(ui->spinBox_uploadThreads,
  //         QOverload<int>::of(&QSpinBox::valueChanged), this,
  //         &SettingsDialog::on_spinBox_uploadThreads_valueChanged);
  // connect(ui->spinBox_downloadThreads,
  //         QOverload<int>::of(&QSpinBox::valueChanged), this,
  //         &SettingsDialog::on_spinBox_downloadThreads_valueChanged);
  // connect(ui->spinBox_cacheSize, QOverload<int>::of(&QSpinBox::valueChanged),
  //         this, &SettingsDialog::on_spinBox_cacheSize_valueChanged);
}

// 析构函数
SettingsDialog::~SettingsDialog() { delete ui; }

// 初始化UI
void SettingsDialog::initUI() {
  // 设置服务器端口范围 - 注释掉不存在的控件
  // ui->spinBox_serverPort->setRange(1, 65535);

  // 设置上传和下载线程数范围 - 注释掉不存在的控件
  // ui->spinBox_uploadThreads->setRange(1, 10);
  // ui->spinBox_downloadThreads->setRange(1, 10);

  // 设置缓存大小范围 - 注释掉不存在的控件
  // ui->spinBox_cacheSize->setRange(1, 10240); // 1MB到10GB

  // 设置实际存在的控件范围
  ui->spinBox_timeout->setRange(5, 300);

  // 更新UI状态
  updateUIState();
}

// 加载配置
void SettingsDialog::loadConfig() {
  // 获取客户端配置
  m_config = ConfigManager::getInstance()->getClientConfig();

  // 设置服务器地址 - 注释掉不存在的控件
  // ui->lineEdit_serverHost->setText(m_config.serverHost);

  // 设置服务器端口 - 注释掉不存在的控件
  // ui->spinBox_serverPort->setValue(m_config.serverPort);

  // 设置记住用户名选项 - 注释掉不存在的控件
  // ui->checkBox_rememberUsername->setChecked(m_config.rememberUsername);

  // 设置自动登录选项 - 注释掉不存在的控件
  // ui->checkBox_autoLogin->setChecked(m_config.autoLogin);

  // 设置上传线程数 - 注释掉不存在的控件
  // ui->spinBox_uploadThreads->setValue(m_config.uploadThreads);

  // 设置下载线程数 - 注释掉不存在的控件
  // ui->spinBox_downloadThreads->setValue(m_config.downloadThreads);

  // 设置缓存大小 - 注释掉不存在的控件
  // ui->spinBox_cacheSize->setValue(m_config.cacheSize /
  //                                 (1024 * 1024)); // 转换为MB

  // 重置配置更改标志
  m_configChanged = false;
}

// 保存配置
void SettingsDialog::saveConfig() {
  // 获取当前配置
  ClientConfig currentConfig = ConfigManager::getInstance()->getClientConfig();

  // 检查是否有更改
  if (m_configChanged) {
    // 保存服务器地址 - 注释掉不存在的控件
    // m_config.serverHost = ui->lineEdit_serverHost->text().trimmed();

    // 保存服务器端口 - 注释掉不存在的控件
    // m_config.serverPort = ui->spinBox_serverPort->value();

    // 保存记住用户名选项 - 注释掉不存在的控件
    // m_config.rememberUsername = ui->checkBox_rememberUsername->isChecked();

    // 保存自动登录选项 - 注释掉不存在的控件
    // m_config.autoLogin = ui->checkBox_autoLogin->isChecked();

    // 保存上传线程数 - 注释掉不存在的控件
    // m_config.uploadThreads = ui->spinBox_uploadThreads->value();

    // 保存下载线程数 - 注释掉不存在的控件
    // m_config.downloadThreads = ui->spinBox_downloadThreads->value();

    // 保存缓存大小 - 注释掉不存在的控件
    // m_config.cacheSize =
    //     ui->spinBox_cacheSize->value() * 1024 * 1024; // 转换为字节

    // 保存配置
    ConfigManager::getInstance()->setClientConfig(m_config);

    LOG_INFO("Settings", "配置已保存");

    // 显示提示
    QMessageBox::information(this, "成功", "设置已保存");
  }
}

// 验证输入
bool SettingsDialog::validateInput() {
  // 验证服务器地址 - 注释掉不存在的控件
  // QString serverHost = ui->lineEdit_serverHost->text().trimmed();
  // if (serverHost.isEmpty()) {
  //   QMessageBox::warning(this, "输入错误", "请输入服务器地址");
  //   ui->lineEdit_serverHost->setFocus();
  //   return false;
  // }

  // 验证服务器端口 - 注释掉不存在的控件
  // int serverPort = ui->spinBox_serverPort->value();
  // if (serverPort < 1 || serverPort > 65535) {
  //   QMessageBox::warning(this, "输入错误", "服务器端口必须在1-65535之间");
  //   ui->spinBox_serverPort->setFocus();
  //   return false;
  // }

  return true;
}

// 更新UI状态
void SettingsDialog::updateUIState() {
  // 根据输入状态更新确定按钮状态
  ui->buttonBox->button(QDialogButtonBox::Ok)->setEnabled(m_configChanged);
}

// 确定按钮点击槽函数
void SettingsDialog::on_buttonBox_accepted() {
  // 验证输入
  if (!validateInput()) {
    return;
  }

  // 保存配置
  saveConfig();

  // 接受对话框
  accept();
}

// 取消按钮点击槽函数
void SettingsDialog::on_buttonBox_rejected() {
  // 如果配置已更改，提示用户
  if (m_configChanged) {
    int ret =
        QMessageBox::question(this, "提示", "设置已更改，确定要放弃更改吗？",
                              QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::No) {
      return;
    }
  }

  // 拒绝对话框
  reject();
}

// 服务器地址输入框文本改变槽函数
void SettingsDialog::on_lineEdit_serverHost_textChanged(const QString &text) {
  // 检查是否有更改
  if (text != m_config.serverHost) {
    m_configChanged = true;
    updateUIState();
  }
}

// 服务器端口输入框文本改变槽函数
void SettingsDialog::on_spinBox_serverPort_valueChanged(int value) {
  // 检查是否有更改
  if (value != m_config.serverPort) {
    m_configChanged = true;
    updateUIState();
  }
}

// 记住用户名复选框状态改变槽函数
void SettingsDialog::on_checkBox_rememberUsername_stateChanged(int state) {
  // 检查是否有更改
  if (state == Qt::Checked && !m_config.rememberUsername) {
    m_configChanged = true;
    updateUIState();
  } else if (state == Qt::Unchecked && m_config.rememberUsername) {
    m_configChanged = true;
    updateUIState();
  }
}

// 自动登录复选框状态改变槽函数
void SettingsDialog::on_checkBox_autoLogin_stateChanged(int state) {
  // 检查是否有更改
  if (state == Qt::Checked && !m_config.autoLogin) {
    m_configChanged = true;
    updateUIState();
  } else if (state == Qt::Unchecked && m_config.autoLogin) {
    m_configChanged = true;
    updateUIState();
  }
}
