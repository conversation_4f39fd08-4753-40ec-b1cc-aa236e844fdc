#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QObject>
#include <QString>
#include <QSettings>
#include <QMutex>
#include <QMutexLocker>
#include "../Common/common.h"

// 配置管理器类（单例模式）
class ConfigManager : public QObject
{
    Q_OBJECT

public:
    // 获取单例实例
    static ConfigManager* getInstance();

    // 加载配置
    void loadConfig();

    // 保存配置
    void saveConfig();

    // 获取服务器配置
    ServerConfig getServerConfig() const;

    // 设置服务器配置
    void setServerConfig(const ServerConfig& config);

    // 获取特定配置项
    QString getHost() const;
    quint16 getPort() const;
    QString getDbPath() const;
    QString getFileDir() const;
    int getMaxConnections() const;
    int getHeartbeatInterval() const;
    int getSessionTimeout() const;
    quint64 getDefaultStorage() const;

    // 设置特定配置项
    void setHost(const QString& host);
    void setPort(quint16 port);
    void setDbPath(const QString& path);
    void setFileDir(const QString& dir);
    void setMaxConnections(int max);
    void setHeartbeatInterval(int interval);
    void setSessionTimeout(int timeout);
    void setDefaultStorage(quint64 storage);

private:
    // 私有构造函数
    ConfigManager(QObject *parent = nullptr);

    // 私有析构函数
    ~ConfigManager();

    // 禁止拷贝构造和赋值
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;

    // 设置默认配置
    void setDefaultConfig();

    static ConfigManager* m_instance;     // 单例实例
    static QMutex m_mutex;                // 互斥锁
    QSettings* m_settings;                // 配置设置
    ServerConfig m_config;                 // 服务器配置
    QString m_configPath;                  // 配置文件路径
};

#endif // CONFIGMANAGER_H
