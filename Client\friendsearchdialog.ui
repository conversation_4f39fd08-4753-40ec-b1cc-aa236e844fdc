<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FriendSearchDialog</class>
 <widget class="QDialog" name="FriendSearchDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>500</width>
    <height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>好友搜索</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icons/search.png</normaloff>:/icons/search.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_search">
     <item>
      <widget class="QLineEdit" name="lineEdit_search">
       <property name="placeholderText">
        <string>输入用户名或邮箱进行搜索</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_search">
       <property name="text">
        <string>搜索</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/search.png</normaloff>:/icons/search.png</iconset>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTableWidget" name="tableWidget_users">
     <property name="selectionMode">
      <enum>QAbstractItemView::SingleSelection</enum>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectRows</enum>
     </property>
     <column>
      <property name="text">
       <string>用户名</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>邮箱</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>注册时间</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>操作</string>
      </property>
     </column>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_buttons">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_add_friend">
       <property name="text">
        <string>添加好友</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/add_friend.png</normaloff>:/icons/add_friend.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_close">
       <property name="text">
        <string>关闭</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/close.png</normaloff>:/icons/close.png</iconset>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
