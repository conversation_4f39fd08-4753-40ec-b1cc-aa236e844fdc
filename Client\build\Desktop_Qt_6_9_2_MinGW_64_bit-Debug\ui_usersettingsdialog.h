/********************************************************************************
** Form generated from reading UI file 'usersettingsdialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_USERSETTINGSDIALOG_H
#define UI_USERSETTINGSDIALOG_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_UserSettingsDialog
{
public:
    QVBoxLayout *verticalLayout;
    QFormLayout *formLayout;
    QLabel *label_username;
    QLineEdit *lineEdit_username;
    QLabel *label_email;
    QLineEdit *lineEdit_email;
    QLabel *label_storage;
    QLabel *label_storage_info;
    QLabel *label_register_time;
    QLabel *label_register_time_info;
    QLabel *label_last_login_time;
    QLabel *label_last_login_time_info;
    QPushButton *pushButton_change_password;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *horizontalLayout_buttons;
    QSpacerItem *horizontalSpacer;
    QPushButton *pushButton_save;
    QPushButton *pushButton_cancel;

    void setupUi(QDialog *UserSettingsDialog)
    {
        if (UserSettingsDialog->objectName().isEmpty())
            UserSettingsDialog->setObjectName("UserSettingsDialog");
        UserSettingsDialog->resize(400, 300);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/settings.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        UserSettingsDialog->setWindowIcon(icon);
        verticalLayout = new QVBoxLayout(UserSettingsDialog);
        verticalLayout->setObjectName("verticalLayout");
        formLayout = new QFormLayout();
        formLayout->setObjectName("formLayout");
        formLayout->setHorizontalSpacing(20);
        formLayout->setVerticalSpacing(15);
        label_username = new QLabel(UserSettingsDialog);
        label_username->setObjectName("label_username");

        formLayout->setWidget(0, QFormLayout::ItemRole::LabelRole, label_username);

        lineEdit_username = new QLineEdit(UserSettingsDialog);
        lineEdit_username->setObjectName("lineEdit_username");
        lineEdit_username->setEnabled(false);

        formLayout->setWidget(0, QFormLayout::ItemRole::FieldRole, lineEdit_username);

        label_email = new QLabel(UserSettingsDialog);
        label_email->setObjectName("label_email");

        formLayout->setWidget(1, QFormLayout::ItemRole::LabelRole, label_email);

        lineEdit_email = new QLineEdit(UserSettingsDialog);
        lineEdit_email->setObjectName("lineEdit_email");
        lineEdit_email->setEnabled(false);

        formLayout->setWidget(1, QFormLayout::ItemRole::FieldRole, lineEdit_email);

        label_storage = new QLabel(UserSettingsDialog);
        label_storage->setObjectName("label_storage");

        formLayout->setWidget(2, QFormLayout::ItemRole::LabelRole, label_storage);

        label_storage_info = new QLabel(UserSettingsDialog);
        label_storage_info->setObjectName("label_storage_info");

        formLayout->setWidget(2, QFormLayout::ItemRole::FieldRole, label_storage_info);

        label_register_time = new QLabel(UserSettingsDialog);
        label_register_time->setObjectName("label_register_time");

        formLayout->setWidget(3, QFormLayout::ItemRole::LabelRole, label_register_time);

        label_register_time_info = new QLabel(UserSettingsDialog);
        label_register_time_info->setObjectName("label_register_time_info");

        formLayout->setWidget(3, QFormLayout::ItemRole::FieldRole, label_register_time_info);

        label_last_login_time = new QLabel(UserSettingsDialog);
        label_last_login_time->setObjectName("label_last_login_time");

        formLayout->setWidget(4, QFormLayout::ItemRole::LabelRole, label_last_login_time);

        label_last_login_time_info = new QLabel(UserSettingsDialog);
        label_last_login_time_info->setObjectName("label_last_login_time_info");

        formLayout->setWidget(4, QFormLayout::ItemRole::FieldRole, label_last_login_time_info);

        pushButton_change_password = new QPushButton(UserSettingsDialog);
        pushButton_change_password->setObjectName("pushButton_change_password");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/password.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_change_password->setIcon(icon1);

        formLayout->setWidget(5, QFormLayout::ItemRole::SpanningRole, pushButton_change_password);


        verticalLayout->addLayout(formLayout);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        horizontalLayout_buttons = new QHBoxLayout();
        horizontalLayout_buttons->setObjectName("horizontalLayout_buttons");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer);

        pushButton_save = new QPushButton(UserSettingsDialog);
        pushButton_save->setObjectName("pushButton_save");
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/save.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_save->setIcon(icon2);

        horizontalLayout_buttons->addWidget(pushButton_save);

        pushButton_cancel = new QPushButton(UserSettingsDialog);
        pushButton_cancel->setObjectName("pushButton_cancel");
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/icons/cancel.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_cancel->setIcon(icon3);

        horizontalLayout_buttons->addWidget(pushButton_cancel);


        verticalLayout->addLayout(horizontalLayout_buttons);


        retranslateUi(UserSettingsDialog);

        QMetaObject::connectSlotsByName(UserSettingsDialog);
    } // setupUi

    void retranslateUi(QDialog *UserSettingsDialog)
    {
        UserSettingsDialog->setWindowTitle(QCoreApplication::translate("UserSettingsDialog", "\347\224\250\346\210\267\350\256\276\347\275\256", nullptr));
        label_username->setText(QCoreApplication::translate("UserSettingsDialog", "\347\224\250\346\210\267\345\220\215\357\274\232", nullptr));
        label_email->setText(QCoreApplication::translate("UserSettingsDialog", "\351\202\256\347\256\261\357\274\232", nullptr));
        label_storage->setText(QCoreApplication::translate("UserSettingsDialog", "\345\255\230\345\202\250\347\251\272\351\227\264\357\274\232", nullptr));
        label_storage_info->setText(QCoreApplication::translate("UserSettingsDialog", "0 B / 0 B", nullptr));
        label_register_time->setText(QCoreApplication::translate("UserSettingsDialog", "\346\263\250\345\206\214\346\227\266\351\227\264\357\274\232", nullptr));
        label_register_time_info->setText(QString());
        label_last_login_time->setText(QCoreApplication::translate("UserSettingsDialog", "\346\234\200\345\220\216\347\231\273\345\275\225\346\227\266\351\227\264\357\274\232", nullptr));
        label_last_login_time_info->setText(QString());
        pushButton_change_password->setText(QCoreApplication::translate("UserSettingsDialog", "\344\277\256\346\224\271\345\257\206\347\240\201", nullptr));
        pushButton_save->setText(QCoreApplication::translate("UserSettingsDialog", "\344\277\235\345\255\230", nullptr));
        pushButton_cancel->setText(QCoreApplication::translate("UserSettingsDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class UserSettingsDialog: public Ui_UserSettingsDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_USERSETTINGSDIALOG_H
