#ifndef MAINWINDOW_H
#define MA<PERSON><PERSON><PERSON>OW_H

#include "../Common/common.h"
#include "chatwindow.h"
#include "configmanager.h"
#include "filepropertiesdialog.h"
#include "fileviewwidget.h"
#include "friendsearchdialog.h"
#include "networkmanager.h"
#include "settingsdialog.h"
#include "ui_mainwindow.h"
#include <QMainWindow>
#include <QShortcut>

class MainWindow : public QMainWindow {
  Q_OBJECT

public:
  explicit MainWindow(QWidget *parent = nullptr);
  ~MainWindow();

  // 设置当前用户信息
  void setCurrentUser(const UserInfo &userInfo);

private slots:
  // 文件菜单动作槽函数
  void on_action_upload_triggered();
  void on_action_new_folder_triggered();
  void on_action_refresh_triggered();
  void on_action_exit_triggered();

  // 编辑菜单动作槽函数
  void on_action_select_all_triggered();
  void on_action_copy_triggered();
  void on_action_cut_triggered();
  void on_action_paste_triggered();
  void on_action_delete_triggered();
  void on_action_rename_triggered();

  // 视图菜单动作槽函数
  void on_action_list_view_triggered();
  void on_action_icon_view_triggered();

  // 工具菜单动作槽函数
  void on_action_search_triggered();
  void on_action_share_triggered();
  void on_action_properties_triggered();
  void on_action_settings_triggered();

  // 帮助菜单动作槽函数
  void on_action_about_triggered();

  // 工具栏动作槽函数
  void on_action_upload_toolbar_triggered();
  void on_action_download_toolbar_triggered();
  void on_action_new_folder_toolbar_triggered();
  void on_action_delete_toolbar_triggered();
  void on_action_refresh_toolbar_triggered();

  // 文件树点击槽函数
  void on_treeWidget_files_itemClicked(QTreeWidgetItem *item, int column);

  // 好友列表点击槽函数
  void on_listWidget_friends_itemClicked(QListWidgetItem *item);

  // 消息列表点击槽函数
  void on_listWidget_messages_itemClicked(QListWidgetItem *item);

  // 标签页关闭槽函数
  void on_tabWidget_center_tabCloseRequested(int index);

  // 网络连接状态改变槽函数
  void onConnectionStateChanged(bool connected);

  // 文件列表响应槽函数
  void onFileListResponse(bool success, const QString &message,
                          const QList<FileInfo> &fileList);

  // 好友列表响应槽函数
  void onFriendListResponse(bool success, const QString &message,
                            const QList<FriendInfo> &friendList);

  // 好友状态变更通知槽函数
  void onFriendStatusNotify(quint32 friendId, bool online);

  // 接收消息槽函数
  void onMessageReceived(const MessageInfo &message);

  // 错误处理槽函数
  void onErrorOccurred(const QString &error);

  // 文件属性对话框槽函数
  void onFilePropertiesDialogAccepted();

  // 文件操作响应槽函数
  void onUploadFileResponse(bool success, const QString &message,
                            quint32 fileId, quint64 offset);
  void onDownloadFileResponse(bool success, const QString &message,
                              quint32 fileId, const QString &fileName,
                              quint64 fileSize);
  void onCreateDirectoryResponse(bool success, const QString &message);
  void onDeleteFileResponse(bool success, const QString &message);
  void onRenameFileResponse(bool success, const QString &message);
  void onMoveFileResponse(bool success, const QString &message);
  void onCopyFileResponse(bool success, const QString &message,
                          const QString &newFileName);
  void onShareFileResponse(bool success, const QString &message,
                           const QString &shareCode);
  void onSearchFileResponse(bool success, const QString &message,
                            const QList<FileInfo> &fileList);

private:
  // 初始化UI
  void initUI();

  // 初始化菜单
  void initMenus();

  // 初始化工具栏
  void initToolbar();

  // 初始化状态栏
  void initStatusbar();

  // 加载文件树
  void loadFileTree();

  // 加载好友列表
  void loadFriendList();

  // 加载消息列表
  void loadMessageList();

  // 创建文件视图标签页
  FileViewWidget *createFileViewTab(quint32 parentId, const QString &title);

  // 创建聊天窗口
  ChatWindow *createChatWindow(quint32 friendId, const QString &friendName);

  // 显示文件属性对话框
  void showFilePropertiesDialog(const FileInfo &fileInfo);

  // 更新文件信息面板
  void updateFileInfoPanel(const FileInfo &fileInfo);

  // 更新状态栏
  void updateStatusbar();

  // 应用主题
  void applyTheme(const QString &themeName);

  // 设置快捷键
  void setupShortcuts();

  // 更新存储状态
  void updateStorageStatus();

  // 刷新当前文件列表
  void refreshCurrentFileList();

  // 格式化文件大小
  QString formatFileSize(quint64 size) const;

  // 关闭事件
  void closeEvent(QCloseEvent *event) override;

  Ui::MainWindow *ui;                             // UI对象
  NetworkManager *m_networkManager;               // 网络管理器
  ConfigManager *m_configManager;                 // 配置管理器
  UserInfo m_currentUser;                         // 当前用户信息
  QMap<quint32, FileViewWidget *> m_fileViewTabs; // 文件视图标签页映射
  QMap<quint32, ChatWindow *> m_chatWindows;      // 聊天窗口映射
  QList<FileInfo> m_currentFileList;              // 当前文件列表
  QList<FriendInfo> m_friendList;                 // 好友列表
  QList<MessageInfo> m_messageList;               // 消息列表
  FileInfo m_selectedFile;                        // 选中的文件
  quint32 m_currentParentId;                      // 当前父目录ID
  FilePropertiesDialog *m_filePropertiesDialog;   // 文件属性对话框
  FriendSearchDialog *m_friendSearchDialog;       // 好友搜索对话框
  QLabel *m_connectionStatusLabel;                // 连接状态标签
  QLabel *m_storageStatusLabel;                   // 存储状态标签
};

#endif // MAINWINDOW_H
