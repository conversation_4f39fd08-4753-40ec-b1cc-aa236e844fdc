<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Cloud7</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icons/cloud.png</normaloff>:/icons/cloud.png</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QSplitter" name="splitter_main">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <widget class="QWidget" name="layout_widget_left">
       <layout class="QVBoxLayout" name="verticalLayout_left">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QTabWidget" name="tabWidget_left">
          <property name="currentIndex">
           <number>0</number>
          </property>
          <widget class="QWidget" name="tab_files">
           <attribute name="title">
            <string>文件</string>
           </attribute>
           <layout class="QVBoxLayout" name="verticalLayout_files">
            <property name="leftMargin">
             <number>5</number>
            </property>
            <property name="topMargin">
             <number>5</number>
            </property>
            <property name="rightMargin">
             <number>5</number>
            </property>
            <property name="bottomMargin">
             <number>5</number>
            </property>
            <item>
             <widget class="QTreeWidget" name="treeWidget_files">
              <property name="headerHidden">
               <bool>true</bool>
              </property>
              <column>
               <property name="text">
                <string notr="true">1</string>
               </property>
              </column>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="tab_friends">
           <attribute name="title">
            <string>好友</string>
           </attribute>
           <layout class="QVBoxLayout" name="verticalLayout_friends">
            <property name="leftMargin">
             <number>5</number>
            </property>
            <property name="topMargin">
             <number>5</number>
            </property>
            <property name="rightMargin">
             <number>5</number>
            </property>
            <property name="bottomMargin">
             <number>5</number>
            </property>
            <item>
             <widget class="QLineEdit" name="lineEdit_search_friends">
              <property name="placeholderText">
               <string>搜索好友...</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QListWidget" name="listWidget_friends"/>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="layout_widget_center">
       <layout class="QVBoxLayout" name="verticalLayout_center">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QTabWidget" name="tabWidget_center">
          <property name="tabsClosable">
           <bool>true</bool>
          </property>
          <property name="movable">
           <bool>true</bool>
          </property>
          <widget class="QWidget" name="tab_home">
           <attribute name="title">
            <string>主页</string>
           </attribute>
           <layout class="QVBoxLayout" name="verticalLayout_home">
            <item>
             <widget class="QLabel" name="label_welcome">
              <property name="text">
               <string>欢迎使用Cloud7网盘系统</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="verticalSpacer">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="layout_widget_right">
       <layout class="QVBoxLayout" name="verticalLayout_right">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QTabWidget" name="tabWidget_right">
          <property name="currentIndex">
           <number>0</number>
          </property>
          <widget class="QWidget" name="tab_file_info">
           <attribute name="title">
            <string>文件信息</string>
           </attribute>
           <layout class="QFormLayout" name="formLayout_file_info">
            <item row="0" column="0">
             <widget class="QLabel" name="label_file_name_title">
              <property name="text">
               <string>文件名：</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLabel" name="label_file_name">
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_file_size_title">
              <property name="text">
               <string>大小：</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QLabel" name="label_file_size">
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="label_file_type_title">
              <property name="text">
               <string>类型：</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QLabel" name="label_file_type">
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QLabel" name="label_file_modify_time_title">
              <property name="text">
               <string>修改时间：</string>
              </property>
             </widget>
            </item>
            <item row="3" column="1">
             <widget class="QLabel" name="label_file_modify_time">
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QLabel" name="label_file_path_title">
              <property name="text">
               <string>路径：</string>
              </property>
             </widget>
            </item>
            <item row="4" column="1">
             <widget class="QLabel" name="label_file_path">
              <property name="text">
               <string>-</string>
              </property>
              <property name="wordWrap">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="5" column="0" colspan="2">
             <spacer name="verticalSpacer_file_info">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="tab_messages">
           <attribute name="title">
            <string>消息</string>
           </attribute>
           <layout class="QVBoxLayout" name="verticalLayout_messages">
            <item>
             <widget class="QListWidget" name="listWidget_messages"/>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1000</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="menu_file">
    <property name="title">
     <string>文件</string>
    </property>
    <addaction name="action_upload"/>
    <addaction name="action_new_folder"/>
    <addaction name="separator"/>
    <addaction name="action_refresh"/>
    <addaction name="separator"/>
    <addaction name="action_exit"/>
   </widget>
   <widget class="QMenu" name="menu_edit">
    <property name="title">
     <string>编辑</string>
    </property>
    <addaction name="action_select_all"/>
    <addaction name="separator"/>
    <addaction name="action_copy"/>
    <addaction name="action_cut"/>
    <addaction name="action_paste"/>
    <addaction name="separator"/>
    <addaction name="action_delete"/>
    <addaction name="action_rename"/>
   </widget>

   <widget class="QMenu" name="menu_tools">
    <property name="title">
     <string>工具</string>
    </property>
    <addaction name="action_search"/>
    <addaction name="action_share"/>
    <addaction name="separator"/>
    <addaction name="action_settings"/>
   </widget>

   <addaction name="menu_file"/>
   <addaction name="menu_edit"/>
   <addaction name="menu_tools"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>工具栏</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="action_upload"/>
   <addaction name="action_new_folder"/>
   <addaction name="separator"/>
   <addaction name="action_refresh"/>
   <addaction name="separator"/>
   <addaction name="action_delete"/>
   <addaction name="action_rename"/>
   <addaction name="separator"/>
   <addaction name="action_share"/>
  </widget>
  <action name="action_upload">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/upload.png</normaloff>:/icons/upload.png</iconset>
   </property>
   <property name="text">
    <string>上传</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+U</string>
   </property>
  </action>
  <action name="action_new_folder">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/new_folder.png</normaloff>:/icons/new_folder.png</iconset>
   </property>
   <property name="text">
    <string>新建文件夹</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+N</string>
   </property>
  </action>
  <action name="action_refresh">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/refresh.png</normaloff>:/icons/refresh.png</iconset>
   </property>
   <property name="text">
    <string>刷新</string>
   </property>
   <property name="shortcut">
    <string>F5</string>
   </property>
  </action>
  <action name="action_exit">
   <property name="text">
    <string>退出</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Q</string>
   </property>
  </action>
  <action name="action_select_all">
   <property name="text">
    <string>全选</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+A</string>
   </property>
  </action>
  <action name="action_copy">
   <property name="text">
    <string>复制</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+C</string>
   </property>
  </action>
  <action name="action_cut">
   <property name="text">
    <string>剪切</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+X</string>
   </property>
  </action>
  <action name="action_paste">
   <property name="text">
    <string>粘贴</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+V</string>
   </property>
  </action>
  <action name="action_delete">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/delete.png</normaloff>:/icons/delete.png</iconset>
   </property>
   <property name="text">
    <string>删除</string>
   </property>
   <property name="shortcut">
    <string>Delete</string>
   </property>
  </action>
  <action name="action_rename">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/rename.png</normaloff>:/icons/rename.png</iconset>
   </property>
   <property name="text">
    <string>重命名</string>
   </property>
   <property name="shortcut">
    <string>F2</string>
   </property>
  </action>

  <action name="action_search">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/search.png</normaloff>:/icons/search.png</iconset>
   </property>
   <property name="text">
    <string>搜索</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+F</string>
   </property>
  </action>
  <action name="action_share">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/share.png</normaloff>:/icons/share.png</iconset>
   </property>
   <property name="text">
    <string>分享</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+S</string>
   </property>
  </action>
  <action name="action_settings">
   <property name="text">
    <string>设置</string>
   </property>
  </action>

 </widget>
 <resources/>
 <connections/>
</ui>
