#include "changepassworddialog.h"
#include "ui_changepassworddialog.h"
#include "logger.h"
#include "../Common/utils.h"
#include <QMessageBox>

// 构造函数
ChangePasswordDialog::ChangePasswordDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::ChangePasswordDialog),
    m_userId(0)
{
    // 初始化UI
    ui->setupUi(this);

    // 初始化UI
    initUI();
}

// 析构函数
ChangePasswordDialog::~ChangePasswordDialog()
{
    delete ui;
}

// 设置用户ID
void ChangePasswordDialog::setUserId(quint32 userId)
{
    m_userId = userId;
}

// 获取用户ID
quint32 ChangePasswordDialog::userId() const
{
    return m_userId;
}

// 确定按钮点击槽函数
void ChangePasswordDialog::on_pushButton_ok_clicked()
{
    // 验证输入
    if (!validateInput()) {
        return;
    }

    // 获取密码
    QString oldPassword = ui->lineEdit_old_password->text();
    QString newPassword = ui->lineEdit_new_password->text();

    // 发送修改密码请求信号
    emit changePasswordRequested(m_userId, oldPassword, newPassword);

    // 关闭对话框
    accept();
}

// 取消按钮点击槽函数
void ChangePasswordDialog::on_pushButton_cancel_clicked()
{
    // 关闭对话框
    reject();
}

// 初始化UI
void ChangePasswordDialog::initUI()
{
    // 设置窗口标题
    setWindowTitle("修改密码");

    // 设置窗口图标
    setWindowIcon(QIcon(":/icons/password.png"));

    // 设置窗口大小
    resize(350, 250);

    // 连接信号槽
    connect(ui->pushButton_ok, &QPushButton::clicked, this, &ChangePasswordDialog::on_pushButton_ok_clicked);
    connect(ui->pushButton_cancel, &QPushButton::clicked, this, &ChangePasswordDialog::on_pushButton_cancel_clicked);
}

// 验证输入
bool ChangePasswordDialog::validateInput()
{
    // 获取输入
    QString oldPassword = ui->lineEdit_old_password->text();
    QString newPassword = ui->lineEdit_new_password->text();
    QString confirmPassword = ui->lineEdit_confirm_password->text();

    // 验证旧密码
    if (oldPassword.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入旧密码");
        ui->lineEdit_old_password->setFocus();
        return false;
    }

    // 验证新密码
    if (newPassword.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入新密码");
        ui->lineEdit_new_password->setFocus();
        return false;
    }

    // 验证新密码长度
    if (newPassword.length() < 6) {
        QMessageBox::warning(this, "输入错误", "新密码长度至少为6位");
        ui->lineEdit_new_password->setFocus();
        return false;
    }

    // 验证密码强度
    if (!Utils::isStrongPassword(newPassword)) {
        QMessageBox::warning(this, "输入错误", "密码强度不够，请使用包含字母和数字的密码");
        ui->lineEdit_new_password->setFocus();
        return false;
    }

    // 验证确认密码
    if (confirmPassword.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请确认新密码");
        ui->lineEdit_confirm_password->setFocus();
        return false;
    }

    // 验证两次输入的密码是否一致
    if (newPassword != confirmPassword) {
        QMessageBox::warning(this, "输入错误", "两次输入的新密码不一致");
        ui->lineEdit_confirm_password->setFocus();
        return false;
    }

    // 验证新旧密码是否相同
    if (oldPassword == newPassword) {
        QMessageBox::warning(this, "输入错误", "新密码不能与旧密码相同");
        ui->lineEdit_new_password->setFocus();
        return false;
    }

    return true;
}
