#include "mainwindow.h"
#include "logger.h"
#include <QCloseEvent>
#include <QDateTime>
#include <QDir>
#include <QFileDialog>
#include <QIcon>
#include <QInputDialog>
#include <QMessageBox>
#include <QStandardPaths>

// 构造函数
MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent), ui(new Ui::MainWindow), m_networkManager(nullptr),
      m_configManager(nullptr), m_currentParentId(0) {
  // 初始化UI
  ui->setupUi(this);

  // 初始化成员变量
  m_networkManager = NetworkManager::getInstance();
  m_configManager = ConfigManager::getInstance();
  m_friendSearchDialog = nullptr;

  // 初始化UI
  initUI();

  // 初始化菜单
  initMenus();

  // 初始化工具栏
  initToolbar();

  // 初始化状态栏
  initStatusbar();

  // 连接信号槽
  connect(m_networkManager, &NetworkManager::connectionStateChanged, this,
          &MainWindow::onConnectionStateChanged);
  connect(m_networkManager, &NetworkManager::fileListResponse, this,
          &MainWindow::onFileListResponse);
  connect(m_networkManager, &NetworkManager::friendListResponse, this,
          &MainWindow::onFriendListResponse);
  connect(m_networkManager, &NetworkManager::friendStatusNotify, this,
          &MainWindow::onFriendStatusNotify);
  connect(m_networkManager, &NetworkManager::messageReceived, this,
          &MainWindow::onMessageReceived);

  // 连接文件操作响应信号
  connect(m_networkManager, &NetworkManager::uploadFileResponse, this,
          &MainWindow::onUploadFileResponse);
  connect(m_networkManager, &NetworkManager::downloadFileResponse, this,
          &MainWindow::onDownloadFileResponse);
  connect(m_networkManager, &NetworkManager::createDirectoryResponse, this,
          &MainWindow::onCreateDirectoryResponse);
  connect(m_networkManager, &NetworkManager::deleteFileResponse, this,
          &MainWindow::onDeleteFileResponse);
  connect(m_networkManager, &NetworkManager::renameFileResponse, this,
          &MainWindow::onRenameFileResponse);
  connect(m_networkManager, &NetworkManager::moveFileResponse, this,
          &MainWindow::onMoveFileResponse);
  connect(m_networkManager, &NetworkManager::copyFileResponse, this,
          &MainWindow::onCopyFileResponse);
  connect(m_networkManager, &NetworkManager::shareFileResponse, this,
          &MainWindow::onShareFileResponse);
  connect(m_networkManager, &NetworkManager::searchFileResponse, this,
          &MainWindow::onSearchFileResponse);
  connect(m_networkManager, &NetworkManager::errorOccurred, this,
          &MainWindow::onErrorOccurred);
  connect(ui->tabWidget_center, &QTabWidget::tabCloseRequested, this,
          &MainWindow::on_tabWidget_center_tabCloseRequested);
}

// 析构函数
MainWindow::~MainWindow() {
  // 清理资源
  for (auto it = m_fileViewTabs.begin(); it != m_fileViewTabs.end(); ++it) {
    delete it.value();
  }
  m_fileViewTabs.clear();

  for (auto it = m_chatWindows.begin(); it != m_chatWindows.end(); ++it) {
    delete it.value();
  }
  m_chatWindows.clear();

  // 删除文件属性对话框
  if (m_filePropertiesDialog) {
    delete m_filePropertiesDialog;
    m_filePropertiesDialog = nullptr;
  }

  delete ui;
}

// 设置当前用户信息
void MainWindow::setCurrentUser(const UserInfo &userInfo) {
  // 保存用户信息
  m_currentUser = userInfo;

  // 更新欢迎标签
  ui->label_welcome->setText(
      QString("欢迎使用Cloud7网盘系统，%1").arg(userInfo.username));

  // 更新状态栏
  updateStatusbar();

  // 请求文件列表
  m_networkManager->getFileListRequest(0);

  // 请求好友列表
  m_networkManager->getFriendListRequest();

  // 请求离线消息
  m_networkManager->getOfflineMessageRequest();

  LOG_INFO("MainWindow", QString("用户 %1 登录成功").arg(userInfo.username));
}

// 初始化UI
void MainWindow::initUI() {
  // 设置窗口标题
  setWindowTitle("Cloud7");

  // 设置窗口图标
  setWindowIcon(QIcon(":/icons/cloud.png"));

  // 设置窗口大小
  resize(1000, 700);
  setMinimumSize(800, 600);

  // 设置分割器比例
  ui->splitter_main->setStretchFactor(0, 1);
  ui->splitter_main->setStretchFactor(1, 4);
  ui->splitter_main->setStretchFactor(2, 1);

  // 设置文件树
  ui->treeWidget_files->setContextMenuPolicy(Qt::CustomContextMenu);
  ui->treeWidget_files->setHeaderLabel("文件目录");

  // 设置好友列表
  ui->listWidget_friends->setContextMenuPolicy(Qt::CustomContextMenu);
  // QListWidget没有setHeaderLabel方法，注释掉
  // ui->listWidget_friends->setHeaderLabel("好友列表");

  // 设置消息列表
  ui->listWidget_messages->setContextMenuPolicy(Qt::CustomContextMenu);
  // QListWidget没有setHeaderLabel方法，注释掉
  // ui->listWidget_messages->setHeaderLabel("消息列表");

  // 设置标签页
  ui->tabWidget_center->setTabsClosable(true);
  ui->tabWidget_center->setMovable(true);

  // 隐藏文件信息面板和消息面板的标签栏
  ui->tabWidget_right->tabBar()->hide();

  // 设置文件信息面板
  ui->label_file_name->setText("-");
  ui->label_file_size->setText("-");
  ui->label_file_type->setText("-");
  ui->label_file_modify_time->setText("-");
  ui->label_file_path->setText("-");

  // 设置默认主题
  applyTheme("light");

  // 添加快捷键
  setupShortcuts();
}

// 初始化菜单
void MainWindow::initMenus() {
  // 设置文件菜单
  ui->action_upload->setIcon(QIcon(":/icons/upload.png"));
  ui->action_new_folder->setIcon(QIcon(":/icons/folder.png"));
  ui->action_refresh->setIcon(QIcon(":/icons/refresh.png"));
  ui->action_exit->setIcon(QIcon(":/icons/settings.png"));

  // 设置编辑菜单
  ui->action_select_all->setIcon(QIcon(":/icons/file.png"));
  ui->action_copy->setIcon(QIcon(":/icons/share.png"));
  ui->action_cut->setIcon(QIcon(":/icons/delete.png"));
  ui->action_paste->setIcon(QIcon(":/icons/upload.png"));
  ui->action_delete->setIcon(QIcon(":/icons/delete.png"));
  ui->action_rename->setIcon(QIcon(":/icons/rename.png"));

  // 设置视图菜单
  ui->action_list_view->setIcon(QIcon(":/icons/file.png"));
  ui->action_icon_view->setIcon(QIcon(":/icons/folder.png"));

  // 设置工具菜单
  ui->action_search->setIcon(QIcon(":/icons/search.png"));
  ui->action_share->setIcon(QIcon(":/icons/share.png"));
  ui->action_settings->setIcon(QIcon(":/icons/settings.png"));

  // 设置好友菜单（如果存在）
  // action_search_friend 在UI文件中不存在，注释掉
  // if (ui->action_search_friend) {
  //   ui->action_search_friend->setIcon(QIcon(":/icons/friend.png"));
  // }

  // 设置帮助菜单
  ui->action_about->setIcon(QIcon(":/icons/info.png"));
}

// 初始化工具栏
void MainWindow::initToolbar() {
  // 设置工具栏图标大小
  ui->toolBar->setIconSize(QSize(24, 24));

  // 设置工具栏动作
  // 这些toolbar action在UI文件中不存在，注释掉
  // ui->action_upload_toolbar->setIcon(QIcon(":/icons/upload.png"));
  // ui->action_download_toolbar->setIcon(QIcon(":/icons/download.png"));
  // ui->action_new_folder_toolbar->setIcon(QIcon(":/icons/folder.png"));
  // ui->action_delete_toolbar->setIcon(QIcon(":/icons/delete.png"));
  // ui->action_refresh_toolbar->setIcon(QIcon(":/icons/refresh.png"));
}

// 初始化状态栏
void MainWindow::initStatusbar() {
  // 设置状态栏
  ui->statusbar->showMessage("就绪");

  // 添加永久部件
  QLabel *connectionLabel = new QLabel("未连接");
  connectionLabel->setStyleSheet("color: red;");
  ui->statusbar->addPermanentWidget(connectionLabel);

  QLabel *storageLabel = new QLabel("存储: 0/0 MB");
  ui->statusbar->addPermanentWidget(storageLabel);

  // 保存引用
  m_connectionStatusLabel = connectionLabel;
  m_storageStatusLabel = storageLabel;

  // 定期更新状态栏
  QTimer *statusTimer = new QTimer(this);
  connect(statusTimer, &QTimer::timeout, this, &MainWindow::updateStatusbar);
  statusTimer->start(5000); // 每5秒更新一次
}

// 加载文件树
void MainWindow::loadFileTree() {
  // 清空文件树
  ui->treeWidget_files->clear();

  // 添加根节点
  QTreeWidgetItem *rootItem = new QTreeWidgetItem(ui->treeWidget_files);
  rootItem->setText(0, "我的文件");
  rootItem->setIcon(0, QIcon(":/icons/folder.png"));
  rootItem->setData(0, Qt::UserRole, 0); // 设置父目录ID为0

  // 展开根节点
  ui->treeWidget_files->expandItem(rootItem);

  // 选中根节点
  ui->treeWidget_files->setCurrentItem(rootItem);
}

// 加载好友列表
void MainWindow::loadFriendList() {
  // 清空好友列表
  ui->listWidget_friends->clear();

  // 添加在线好友分组
  QListWidgetItem *onlineGroup = new QListWidgetItem("在线好友");
  onlineGroup->setFlags(onlineGroup->flags() & ~Qt::ItemIsSelectable &
                        ~Qt::ItemIsEnabled);
  QFont font = onlineGroup->font();
  font.setBold(true);
  onlineGroup->setFont(font);
  ui->listWidget_friends->addItem(onlineGroup);

  // 添加离线好友分组
  QListWidgetItem *offlineGroup = new QListWidgetItem("离线好友");
  offlineGroup->setFlags(offlineGroup->flags() & ~Qt::ItemIsSelectable &
                         ~Qt::ItemIsEnabled);
  offlineGroup->setFont(font);
  ui->listWidget_friends->addItem(offlineGroup);

  // 添加好友
  for (const FriendInfo &friendInfo : m_friendList) {
    QListWidgetItem *item = new QListWidgetItem();
    item->setText(friendInfo.username);
    item->setIcon(QIcon(friendInfo.isOnline ? ":/icons/online.png"
                                            : ":/icons/offline.png"));
    item->setData(Qt::UserRole, friendInfo.friendId);

    // 根据在线状态添加到对应分组
    if (friendInfo.isOnline) {
      ui->listWidget_friends->insertItem(
          ui->listWidget_friends->row(offlineGroup), item);
    } else {
      ui->listWidget_friends->addItem(item);
    }
  }
}

// 加载消息列表
void MainWindow::loadMessageList() {
  // 清空消息列表
  ui->listWidget_messages->clear();

  // 添加消息
  for (const MessageInfo &messageInfo : m_messageList) {
    QListWidgetItem *item = new QListWidgetItem();

    // 查找发送者用户名
    QString senderName = "未知用户";
    for (const FriendInfo &friendInfo : m_friendList) {
      if (friendInfo.friendId == messageInfo.senderId) {
        senderName = friendInfo.username;
        break;
      }
    }

    // 设置消息项文本
    QString text = QString("%1: %2").arg(senderName, messageInfo.content);
    if (text.length() > 30) {
      text = text.left(30) + "...";
    }
    item->setText(text);
    item->setToolTip(messageInfo.content);
    item->setData(Qt::UserRole, messageInfo.senderId);

    // 根据是否已读设置字体
    if (!messageInfo.isRead) {
      QFont font = item->font();
      font.setBold(true);
      item->setFont(font);
    }

    ui->listWidget_messages->addItem(item);
  }
}

// 创建文件视图标签页
FileViewWidget *MainWindow::createFileViewTab(quint32 parentId,
                                              const QString &title) {
  // 检查是否已经存在该标签页
  if (m_fileViewTabs.contains(parentId)) {
    // 如果存在，则切换到该标签页
    int index = ui->tabWidget_center->indexOf(m_fileViewTabs[parentId]);
    ui->tabWidget_center->setCurrentIndex(index);
    return m_fileViewTabs[parentId];
  }

  // 创建文件视图部件
  FileViewWidget *fileViewWidget = new FileViewWidget(this);
  fileViewWidget->setParentId(parentId);

  // 添加到标签页
  int index = ui->tabWidget_center->addTab(fileViewWidget, title);
  ui->tabWidget_center->setCurrentIndex(index);

  // 添加到映射
  m_fileViewTabs[parentId] = fileViewWidget;

  // 连接信号槽
  connect(fileViewWidget, &FileViewWidget::fileSelected, this,
          &MainWindow::updateFileInfoPanel);
  connect(fileViewWidget, &FileViewWidget::openDirectoryRequested, this,
          [this](quint32 parentId, const QString &title) {
            createFileViewTab(parentId, title);
          });

  return fileViewWidget;
}

// 创建聊天窗口
ChatWindow *MainWindow::createChatWindow(quint32 friendId,
                                         const QString &friendName) {
  // 检查是否已经存在该聊天窗口
  if (m_chatWindows.contains(friendId)) {
    // 如果存在，则显示该窗口
    m_chatWindows[friendId]->show();
    m_chatWindows[friendId]->raise();
    m_chatWindows[friendId]->activateWindow();
    return m_chatWindows[friendId];
  }

  // 创建聊天窗口
  ChatWindow *chatWindow = new ChatWindow(this);
  chatWindow->setFriendId(friendId);
  chatWindow->setFriendName(friendName);
  chatWindow->setUserId(m_currentUser.userId);
  chatWindow->setUserName(m_currentUser.username);

  // 显示窗口
  chatWindow->show();

  // 添加到映射
  m_chatWindows[friendId] = chatWindow;

  // 连接信号槽
  connect(chatWindow, &ChatWindow::sendMessageRequested, m_networkManager,
          &NetworkManager::sendMessage);
  connect(chatWindow, &ChatWindow::getMessageHistoryRequested, m_networkManager,
          &NetworkManager::getMessageHistoryRequest);
  connect(chatWindow, &ChatWindow::destroyed, this,
          [this, friendId]() { m_chatWindows.remove(friendId); });

  // 请求历史消息
  m_networkManager->getMessageHistoryRequest(friendId);

  return chatWindow;
}

// 更新文件信息面板
void MainWindow::updateFileInfoPanel(const FileInfo &fileInfo) {
  // 保存选中的文件
  m_selectedFile = fileInfo;

  // 更新文件信息面板
  ui->label_file_name->setText(fileInfo.fileName);
  ui->label_file_size->setText(formatFileSize(fileInfo.fileSize));
  ui->label_file_type->setText(fileInfo.fileType);
  ui->label_file_modify_time->setText(fileInfo.modifyTime);
  ui->label_file_path->setText(fileInfo.filePath);
}

// 更新状态栏
void MainWindow::updateStatusbar() {
  // 更新连接状态
  if (m_networkManager && m_networkManager->isConnected()) {
    m_connectionStatusLabel->setText("已连接");
    m_connectionStatusLabel->setStyleSheet("color: green;");
  } else {
    m_connectionStatusLabel->setText("未连接");
    m_connectionStatusLabel->setStyleSheet("color: red;");
  }

  // 更新存储状态
  QString storageText = QString("存储: %1 / %2")
                            .arg(formatFileSize(m_currentUser.storageUsed))
                            .arg(formatFileSize(m_currentUser.storageTotal));
  m_storageStatusLabel->setText(storageText);

  // 更新主状态栏消息
  QString status = QString("用户: %1 | 存储: %2 / %3")
                       .arg(m_currentUser.username)
                       .arg(formatFileSize(m_currentUser.storageUsed))
                       .arg(formatFileSize(m_currentUser.storageTotal));

  ui->statusbar->showMessage(status);

  // 保存当前状态到配置
  // ConfigManager没有setValue方法，注释掉
  // m_configManager->setValue("lastUpdate",
  //                          QDateTime::currentDateTime().toString(Qt::ISODate));
}

// 应用主题
void MainWindow::applyTheme(const QString &themeName) {
  if (themeName == "dark") {
    // 深色主题
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    qApp->setPalette(darkPalette);

    // 设置样式表
    QString style = "QTabWidget::pane { border: 1px solid #3c3c3c; }"
                    "QTabBar::tab { background: #2c2c2c; color: white; }"
                    "QTabBar::tab:selected { background: #3c3c3c; }"
                    "QTreeView { background: #2c2c2c; color: white; }"
                    "QListView { background: #2c2c2c; color: white; }"
                    "QTableWidget { background: #2c2c2c; color: white; }"
                    "QLabel { color: white; }"
                    "QPushButton { background: #3c3c3c; color: white; }"
                    "QLineEdit { background: #2c2c2c; color: white; }"
                    "QStatusBar { background: #2c2c2c; color: white; }";
    qApp->setStyleSheet(style);
  } else {
    // 浅色主题（默认）
    QPalette lightPalette = qApp->palette();
    qApp->setPalette(lightPalette);

    // 清除样式表
    qApp->setStyleSheet("");
  }

  // 保存当前主题
  // ConfigManager没有setValue方法，注释掉
  // m_configManager->setValue("theme", themeName);
}

// 设置快捷键
void MainWindow::setupShortcuts() {
  // 文件操作快捷键
  QShortcut *uploadShortcut = new QShortcut(QKeySequence("Ctrl+U"), this);
  connect(uploadShortcut, &QShortcut::activated, this,
          &MainWindow::on_action_upload_triggered);

  QShortcut *refreshShortcut = new QShortcut(QKeySequence("F5"), this);
  connect(refreshShortcut, &QShortcut::activated, this,
          &MainWindow::on_action_refresh_triggered);

  QShortcut *newFolderShortcut = new QShortcut(QKeySequence("Ctrl+N"), this);
  connect(newFolderShortcut, &QShortcut::activated, this,
          &MainWindow::on_action_new_folder_triggered);

  QShortcut *deleteShortcut = new QShortcut(QKeySequence("Delete"), this);
  connect(deleteShortcut, &QShortcut::activated, this,
          &MainWindow::on_action_delete_triggered);

  QShortcut *renameShortcut = new QShortcut(QKeySequence("F2"), this);
  connect(renameShortcut, &QShortcut::activated, this,
          &MainWindow::on_action_rename_triggered);

  // 视图操作快捷键
  QShortcut *listViewShortcut = new QShortcut(QKeySequence("Ctrl+1"), this);
  connect(listViewShortcut, &QShortcut::activated, this,
          &MainWindow::on_action_list_view_triggered);

  QShortcut *iconViewShortcut = new QShortcut(QKeySequence("Ctrl+2"), this);
  connect(iconViewShortcut, &QShortcut::activated, this,
          &MainWindow::on_action_icon_view_triggered);

  // 工具操作快捷键
  QShortcut *searchShortcut = new QShortcut(QKeySequence("Ctrl+F"), this);
  connect(searchShortcut, &QShortcut::activated, this,
          &MainWindow::on_action_search_triggered);

  QShortcut *shareShortcut = new QShortcut(QKeySequence("Ctrl+S"), this);
  connect(shareShortcut, &QShortcut::activated, this,
          &MainWindow::on_action_share_triggered);

  // 帮助操作快捷键
  QShortcut *aboutShortcut = new QShortcut(QKeySequence("F1"), this);
  connect(aboutShortcut, &QShortcut::activated, this,
          &MainWindow::on_action_about_triggered);
}

// 格式化文件大小
QString MainWindow::formatFileSize(quint64 size) const {
  if (size < 1024) {
    return QString("%1 B").arg(size);
  } else if (size < 1024 * 1024) {
    return QString("%1 KB").arg(size / 1024.0, 0, 'f', 2);
  } else if (size < 1024 * 1024 * 1024) {
    return QString("%1 MB").arg(size / (1024.0 * 1024.0), 0, 'f', 2);
  } else {
    return QString("%1 GB").arg(size / (1024.0 * 1024.0 * 1024.0), 0, 'f', 2);
  }
}

// 关闭事件
void MainWindow::closeEvent(QCloseEvent *event) {
  // 询问是否退出
  int ret = QMessageBox::question(this, "退出", "确定要退出Cloud7吗？",
                                  QMessageBox::Yes | QMessageBox::No);

  if (ret == QMessageBox::Yes) {
    // 发送登出请求
    m_networkManager->logoutRequest();

    // 断开连接
    m_networkManager->disconnectFromServer();

    // 接受关闭事件
    event->accept();
  } else {
    // 忽略关闭事件
    event->ignore();
  }
}

// 文件菜单动作槽函数
void MainWindow::on_action_upload_triggered() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (!currentFileView) {
    QMessageBox::warning(this, "警告", "请先打开一个文件夹");
    return;
  }

  // 获取当前父目录ID
  quint32 parentId = currentFileView->parentId();

  // 打开文件选择对话框
  QStringList files = QFileDialog::getOpenFileNames(
      this, "选择文件",
      QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation));

  // 如果没有选择文件，则返回
  if (files.isEmpty()) {
    return;
  }

  // 上传文件
  for (const QString &file : files) {
    m_networkManager->uploadFileRequest(file, parentId);
  }
}

void MainWindow::on_action_new_folder_triggered() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (!currentFileView) {
    QMessageBox::warning(this, "警告", "请先打开一个文件夹");
    return;
  }

  // 获取当前父目录ID
  quint32 parentId = currentFileView->parentId();

  // 弹出输入对话框
  bool ok;
  QString dirName = QInputDialog::getText(
      this, "新建文件夹", "请输入文件夹名称:", QLineEdit::Normal, "新建文件夹",
      &ok);

  // 如果取消或输入为空，则返回
  if (!ok || dirName.isEmpty()) {
    return;
  }

  // 创建目录
  m_networkManager->createDirectoryRequest(dirName, parentId);
}

void MainWindow::on_action_refresh_triggered() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (currentFileView) {
    // 刷新文件列表
    m_networkManager->getFileListRequest(currentFileView->parentId());
  } else {
    // 刷新文件树
    m_networkManager->getFileListRequest(0);
  }

  // 刷新好友列表
  m_networkManager->getFriendListRequest();
}

void MainWindow::on_action_exit_triggered() {
  // 关闭窗口
  close();
}

// 编辑菜单动作槽函数
void MainWindow::on_action_select_all_triggered() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (currentFileView) {
    // 全选
    // FileViewWidget没有selectAll方法，注释掉
    // currentFileView->selectAll();
  }
}

void MainWindow::on_action_copy_triggered() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (currentFileView) {
    // 复制
    // FileViewWidget没有copy方法，注释掉
    // currentFileView->copy();
  }
}

void MainWindow::on_action_cut_triggered() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (currentFileView) {
    // 剪切
    // FileViewWidget没有cut方法，注释掉
    // currentFileView->cut();
  }
}

void MainWindow::on_action_paste_triggered() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (currentFileView) {
    // 粘贴
    // FileViewWidget没有paste方法，注释掉
    // currentFileView->paste();
  }
}

void MainWindow::on_action_delete_triggered() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (currentFileView) {
    // 删除
    // FileViewWidget没有deleteSelected方法，注释掉
    // currentFileView->deleteSelected();
  }
}

void MainWindow::on_action_rename_triggered() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (currentFileView) {
    // 重命名
    // FileViewWidget没有renameSelected方法，注释掉
    // currentFileView->renameSelected();
  }
}

// 视图菜单动作槽函数
void MainWindow::on_action_list_view_triggered() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (currentFileView) {
    // 设置为列表视图
    // FileViewWidget没有setViewMode方法，注释掉
    // currentFileView->setViewMode(FileViewWidget::ListView);
  }
}

void MainWindow::on_action_icon_view_triggered() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (currentFileView) {
    // 设置为图标视图
    // FileViewWidget没有setViewMode方法，注释掉
    // currentFileView->setViewMode(FileViewWidget::IconView);
  }
}

// 工具菜单动作槽函数
void MainWindow::on_action_search_triggered() {
  // 弹出输入对话框
  bool ok;
  QString keyword = QInputDialog::getText(
      this, "搜索文件", "请输入搜索关键词:", QLineEdit::Normal, "", &ok);

  // 如果取消或输入为空，则返回
  if (!ok || keyword.isEmpty()) {
    return;
  }

  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  quint32 parentId = currentFileView ? currentFileView->parentId() : 0;

  // 搜索文件
  m_networkManager->searchFileRequest(keyword, parentId);
}

void MainWindow::on_action_share_triggered() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (!currentFileView) {
    QMessageBox::warning(this, "警告", "请先选择一个文件");
    return;
  }

  // 获取选中的文件
  // getSelectedFile是私有方法，注释掉
  // FileInfo selectedFile = currentFileView->getSelectedFile();
  FileInfo selectedFile; // 创建空的FileInfo对象
  if (selectedFile.fileId == 0 || selectedFile.isDir) {
    QMessageBox::warning(this, "警告", "请选择一个文件进行分享");
    return;
  }

  // 弹出输入对话框
  bool ok;
  QString expireTime = QInputDialog::getText(
      this, "分享文件", "请输入过期时间(留空为永不过期):", QLineEdit::Normal,
      "", &ok);

  // 如果取消，则返回
  if (!ok) {
    return;
  }

  // 分享文件
  m_networkManager->shareFileRequest(selectedFile.fileId, expireTime);
}

void MainWindow::on_action_settings_triggered() {
  // 创建设置对话框
  SettingsDialog *settingsDialog = new SettingsDialog(this);

  // 显示对话框
  settingsDialog->show();
}

// 好友搜索菜单动作槽函数 - action_search_friend在UI文件中不存在，注释掉
/*
void MainWindow::on_action_search_friend_triggered() {
  // 如果对话框不存在，则创建
  if (!m_friendSearchDialog) {
    m_friendSearchDialog = new FriendSearchDialog(this);
    m_friendSearchDialog->setUserId(m_currentUser.userId);

    // 连接信号槽
    connect(m_friendSearchDialog, &FriendSearchDialog::searchUserRequested,
            m_networkManager, &NetworkManager::searchUserRequest);
    connect(m_friendSearchDialog, &FriendSearchDialog::addFriendRequested,
            m_networkManager, &NetworkManager::addFriendRequest);
    connect(m_networkManager, &NetworkManager::searchUserResponse,
            m_friendSearchDialog, &FriendSearchDialog::onSearchUserResponse);
  }

  // 显示对话框
  m_friendSearchDialog->show();
  m_friendSearchDialog->raise();
  m_friendSearchDialog->activateWindow();
}
*/

// 帮助菜单动作槽函数
void MainWindow::on_action_about_triggered() {
  QMessageBox::about(this, "关于Cloud7",
                     "Cloud7网盘系统 v1.0.0\n\n"
                     "基于Qt6.9.1、C++和SQLite3.50.4开发的网盘系统\n\n"
                     "Copyright © 2023 Cloud7 Team");
}

// 工具栏动作槽函数
void MainWindow::on_action_upload_toolbar_triggered() {
  // 调用上传菜单动作
  on_action_upload_triggered();
}

void MainWindow::on_action_download_toolbar_triggered() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (!currentFileView) {
    QMessageBox::warning(this, "警告", "请先选择一个文件");
    return;
  }

  // 下载选中的文件
  // FileViewWidget没有downloadSelected方法，注释掉
  // currentFileView->downloadSelected();
}

void MainWindow::on_action_new_folder_toolbar_triggered() {
  // 调用新建文件夹菜单动作
  on_action_new_folder_triggered();
}

void MainWindow::on_action_delete_toolbar_triggered() {
  // 调用删除菜单动作
  on_action_delete_triggered();
}

void MainWindow::on_action_refresh_toolbar_triggered() {
  // 调用刷新菜单动作
  on_action_refresh_triggered();
}

// 文件树点击槽函数
void MainWindow::on_treeWidget_files_itemClicked(QTreeWidgetItem *item,
                                                 int column) {
  // 获取父目录ID
  quint32 parentId = item->data(0, Qt::UserRole).toUInt();

  // 创建文件视图标签页
  QString title = item->text(0);
  createFileViewTab(parentId, title);
}

// 好友列表点击槽函数
void MainWindow::on_listWidget_friends_itemClicked(QListWidgetItem *item) {
  // 获取好友ID
  quint32 friendId = item->data(Qt::UserRole).toUInt();

  // 如果是分组项，则返回
  if (friendId == 0) {
    return;
  }

  // 获取好友名称
  QString friendName = item->text();

  // 创建聊天窗口
  createChatWindow(friendId, friendName);
}

// 消息列表点击槽函数
void MainWindow::on_listWidget_messages_itemClicked(QListWidgetItem *item) {
  // 获取发送者ID
  quint32 senderId = item->data(Qt::UserRole).toUInt();

  // 查找好友信息
  QString friendName = "未知用户";
  for (const FriendInfo &friendInfo : m_friendList) {
    if (friendInfo.friendId == senderId) {
      friendName = friendInfo.username;
      break;
    }
  }

  // 创建聊天窗口
  createChatWindow(senderId, friendName);

  // 设置消息为已读
  QFont font = item->font();
  font.setBold(false);
  item->setFont(font);
}

// 标签页关闭槽函数
void MainWindow::on_tabWidget_center_tabCloseRequested(int index) {
  // 获取标签页部件
  QWidget *widget = ui->tabWidget_center->widget(index);

  // 如果是文件视图部件，则从映射中移除
  FileViewWidget *fileViewWidget = qobject_cast<FileViewWidget *>(widget);
  if (fileViewWidget) {
    quint32 parentId = fileViewWidget->parentId();
    m_fileViewTabs.remove(parentId);
  }

  // 关闭标签页
  ui->tabWidget_center->removeTab(index);

  // 如果没有标签页了，则显示主页
  if (ui->tabWidget_center->count() == 0) {
    ui->tabWidget_center->addTab(new QWidget(), "主页");
  }
}

// 网络连接状态改变槽函数
void MainWindow::onConnectionStateChanged(bool connected) {
  // 更新状态栏
  if (connected) {
    ui->statusbar->showMessage("已连接到服务器");
    m_connectionStatusLabel->setText("已连接");
    m_connectionStatusLabel->setStyleSheet("color: green;");
  } else {
    ui->statusbar->showMessage("未连接到服务器");
    m_connectionStatusLabel->setText("未连接");
    m_connectionStatusLabel->setStyleSheet("color: red;");
  }

  // 保存连接状态到配置
  // ConfigManager没有setValue方法，注释掉
  // m_configManager->setValue("connected", connected);
}

// 文件列表响应槽函数
void MainWindow::onFileListResponse(bool success, const QString &message,
                                    const QList<FileInfo> &fileList) {
  // 处理文件列表响应
  if (success) {
    LOG_INFO("MainWindow",
             QString("获取文件列表成功，数量: %1").arg(fileList.size()));

    // 保存文件列表
    m_currentFileList = fileList;

    // 更新文件树
    loadFileTree();

    // 更新当前文件视图
    FileViewWidget *currentFileView =
        qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
    if (currentFileView) {
      currentFileView->setFileList(fileList);
    }

    // 更新存储状态
    updateStorageStatus();
  } else {
    LOG_ERROR("MainWindow", QString("获取文件列表失败: %1").arg(message));
    QMessageBox::warning(this, "错误", "获取文件列表失败: " + message);
  }
}

// 更新存储状态
void MainWindow::updateStorageStatus() {
  // 计算已使用存储空间
  quint64 usedStorage = 0;
  for (const FileInfo &fileInfo : m_currentFileList) {
    if (!fileInfo.isDir) {
      usedStorage += fileInfo.fileSize;
    }
  }

  // 更新用户存储信息
  m_currentUser.storageUsed = usedStorage;

  // 更新状态栏
  updateStatusbar();

  // 保存存储信息到配置
  // ConfigManager没有setValue方法，注释掉
  // m_configManager->setValue("storageUsed", usedStorage);
}

// 好友列表响应槽函数
void MainWindow::onFriendListResponse(bool success, const QString &message,
                                      const QList<FriendInfo> &friendList) {
  // 处理好友列表响应
  if (success) {
    LOG_INFO("MainWindow",
             QString("获取好友列表成功，数量: %1").arg(friendList.size()));

    // 保存好友列表
    m_friendList = friendList;

    // 更新好友列表
    loadFriendList();
  } else {
    LOG_ERROR("MainWindow", QString("获取好友列表失败: %1").arg(message));
    QMessageBox::warning(this, "错误", "获取好友列表失败: " + message);
  }
}

// 好友状态变更通知槽函数
void MainWindow::onFriendStatusNotify(quint32 friendId, bool online) {
  LOG_INFO("MainWindow", QString("好友状态变更，ID: %1，状态: %2")
                             .arg(friendId)
                             .arg(online ? "在线" : "离线"));

  // 更新好友列表中的状态
  for (int i = 0; i < m_friendList.size(); ++i) {
    if (m_friendList[i].friendId == friendId) {
      m_friendList[i].isOnline = online;
      break;
    }
  }

  // 更新好友列表UI
  loadFriendList();

  // 如果有对应的聊天窗口，则更新状态
  if (m_chatWindows.contains(friendId)) {
    // ChatWindow没有setFriendOnlineStatus方法，注释掉
    // m_chatWindows[friendId]->setFriendOnlineStatus(online);
  }
}

// 接收消息槽函数
void MainWindow::onMessageReceived(const MessageInfo &message) {
  LOG_INFO("MainWindow", QString("接收到消息，发送者ID: %1，内容: %2")
                             .arg(message.senderId)
                             .arg(message.content));

  // 添加到消息列表
  m_messageList.append(message);

  // 更新消息列表UI
  loadMessageList();

  // 如果有对应的聊天窗口，则显示消息
  if (m_chatWindows.contains(message.senderId)) {
    // ChatWindow没有appendMessage方法，使用addMessage
    m_chatWindows[message.senderId]->addMessage(message);
  }
}

// 错误处理槽函数
void MainWindow::onErrorOccurred(const QString &error) {
  LOG_ERROR("MainWindow", QString("发生错误: %1").arg(error));
  QMessageBox::critical(this, "错误", error);

  // 更新状态栏
  ui->statusbar->showMessage("发生错误: " + error);
}

// 显示文件属性对话框
void MainWindow::showFilePropertiesDialog(const FileInfo &fileInfo) {
  // 创建文件属性对话框（如果不存在）
  if (!m_filePropertiesDialog) {
    m_filePropertiesDialog = new FilePropertiesDialog(this);
    connect(m_filePropertiesDialog, &FilePropertiesDialog::accepted, this,
            &MainWindow::onFilePropertiesDialogAccepted);
  }

  // 设置文件信息
  m_filePropertiesDialog->setFileInfo(fileInfo);

  // 显示对话框
  m_filePropertiesDialog->show();
}

// 文件属性菜单动作槽函数
void MainWindow::on_action_properties_triggered() {
  // 检查是否有选中的文件
  if (m_selectedFile.fileId == 0) {
    QMessageBox::information(this, "提示", "请先选择一个文件或文件夹");
    return;
  }

  // 显示文件属性对话框
  showFilePropertiesDialog(m_selectedFile);
}

// 文件属性对话框槽函数
void MainWindow::onFilePropertiesDialogAccepted() {
  // 获取对话框中的文件信息
  FileInfo fileInfo = m_filePropertiesDialog->getFileInfo();

  // 可以在这里添加文件属性修改的处理逻辑
  // 例如修改文件名等

  LOG_INFO("MainWindow",
           QString("文件属性对话框已关闭，文件名: %1").arg(fileInfo.fileName));
}

// 上传文件响应槽函数
void MainWindow::onUploadFileResponse(bool success, const QString &message,
                                      quint32 fileId, quint64 offset) {
  if (success) {
    LOG_INFO("MainWindow", QString("文件上传成功，文件ID: %1").arg(fileId));
    QMessageBox::information(this, "上传成功", "文件上传成功");

    // 刷新当前文件列表
    refreshCurrentFileList();
  } else {
    LOG_ERROR("MainWindow", QString("文件上传失败: %1").arg(message));
    QMessageBox::warning(this, "上传失败", "文件上传失败: " + message);
  }
}

// 下载文件响应槽函数
void MainWindow::onDownloadFileResponse(bool success, const QString &message,
                                        quint32 fileId, const QString &fileName,
                                        quint64 fileSize) {
  if (success) {
    LOG_INFO("MainWindow", QString("文件下载开始，文件ID: %1，文件名: %2")
                               .arg(fileId)
                               .arg(fileName));
    QMessageBox::information(this, "下载开始",
                             QString("文件 %1 开始下载").arg(fileName));
  } else {
    LOG_ERROR("MainWindow", QString("文件下载失败: %1").arg(message));
    QMessageBox::warning(this, "下载失败", "文件下载失败: " + message);
  }
}

// 创建目录响应槽函数
void MainWindow::onCreateDirectoryResponse(bool success,
                                           const QString &message) {
  if (success) {
    LOG_INFO("MainWindow", "创建目录成功");
    QMessageBox::information(this, "创建成功", "目录创建成功");

    // 刷新当前文件列表
    refreshCurrentFileList();
  } else {
    LOG_ERROR("MainWindow", QString("创建目录失败: %1").arg(message));
    QMessageBox::warning(this, "创建失败", "目录创建失败: " + message);
  }
}

// 删除文件响应槽函数
void MainWindow::onDeleteFileResponse(bool success, const QString &message) {
  if (success) {
    LOG_INFO("MainWindow", "删除文件成功");
    QMessageBox::information(this, "删除成功", "文件删除成功");

    // 刷新当前文件列表
    refreshCurrentFileList();
  } else {
    LOG_ERROR("MainWindow", QString("删除文件失败: %1").arg(message));
    QMessageBox::warning(this, "删除失败", "文件删除失败: " + message);
  }
}

// 重命名文件响应槽函数
void MainWindow::onRenameFileResponse(bool success, const QString &message) {
  if (success) {
    LOG_INFO("MainWindow", "重命名文件成功");
    QMessageBox::information(this, "重命名成功", "文件重命名成功");

    // 刷新当前文件列表
    refreshCurrentFileList();
  } else {
    LOG_ERROR("MainWindow", QString("重命名文件失败: %1").arg(message));
    QMessageBox::warning(this, "重命名失败", "文件重命名失败: " + message);
  }
}

// 移动文件响应槽函数
void MainWindow::onMoveFileResponse(bool success, const QString &message) {
  if (success) {
    LOG_INFO("MainWindow", "移动文件成功");
    QMessageBox::information(this, "移动成功", "文件移动成功");

    // 刷新当前文件列表
    refreshCurrentFileList();
  } else {
    LOG_ERROR("MainWindow", QString("移动文件失败: %1").arg(message));
    QMessageBox::warning(this, "移动失败", "文件移动失败: " + message);
  }
}

// 复制文件响应槽函数
void MainWindow::onCopyFileResponse(bool success, const QString &message,
                                    const QString &newFileName) {
  if (success) {
    LOG_INFO("MainWindow",
             QString("复制文件成功，新文件名: %1").arg(newFileName));
    QMessageBox::information(
        this, "复制成功",
        QString("文件复制成功，新文件名: %1").arg(newFileName));

    // 刷新当前文件列表
    refreshCurrentFileList();
  } else {
    LOG_ERROR("MainWindow", QString("复制文件失败: %1").arg(message));
    QMessageBox::warning(this, "复制失败", "文件复制失败: " + message);
  }
}

// 分享文件响应槽函数
void MainWindow::onShareFileResponse(bool success, const QString &message,
                                     const QString &shareCode) {
  if (success) {
    LOG_INFO("MainWindow", QString("分享文件成功，分享码: %1").arg(shareCode));
    QMessageBox::information(
        this, "分享成功", QString("文件分享成功\n分享码: %1").arg(shareCode));
  } else {
    LOG_ERROR("MainWindow", QString("分享文件失败: %1").arg(message));
    QMessageBox::warning(this, "分享失败", "文件分享失败: " + message);
  }
}

// 搜索文件响应槽函数
void MainWindow::onSearchFileResponse(bool success, const QString &message,
                                      const QList<FileInfo> &fileList) {
  if (success) {
    LOG_INFO("MainWindow",
             QString("搜索文件成功，找到 %1 个文件").arg(fileList.size()));

    // 显示搜索结果（可以在这里打开搜索结果对话框）
    if (fileList.isEmpty()) {
      QMessageBox::information(this, "搜索结果", "未找到匹配的文件");
    } else {
      QMessageBox::information(
          this, "搜索结果",
          QString("找到 %1 个匹配的文件").arg(fileList.size()));
    }
  } else {
    LOG_ERROR("MainWindow", QString("搜索文件失败: %1").arg(message));
    QMessageBox::warning(this, "搜索失败", "文件搜索失败: " + message);
  }
}

// 刷新当前文件列表
void MainWindow::refreshCurrentFileList() {
  // 获取当前文件视图
  FileViewWidget *currentFileView =
      qobject_cast<FileViewWidget *>(ui->tabWidget_center->currentWidget());
  if (currentFileView) {
    // 刷新当前目录的文件列表
    m_networkManager->getFileListRequest(currentFileView->parentId());
    LOG_INFO(
        "MainWindow",
        QString("刷新文件列表，父目录ID: %1").arg(currentFileView->parentId()));
  } else {
    // 如果没有当前文件视图，刷新根目录
    m_networkManager->getFileListRequest(0);
    LOG_INFO("MainWindow", "刷新根目录文件列表");
  }

  // 同时刷新文件树
  loadFileTree();
}
