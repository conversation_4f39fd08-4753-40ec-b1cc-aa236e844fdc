#include "fileviewwidget.h"
#include "logger.h"
#include "networkmanager.h"
#include "ui_fileviewwidget.h"
#include <QFileDialog>
#include <QInputDialog>
#include <QListView>
#include <QMessageBox>
#include <QSortFilterProxyModel>
#include <QStandardItem>
#include <QStandardItemModel>

// 构造函数
FileViewWidget::FileViewWidget(QWidget *parent)
    : QWidget(parent), ui(new Ui::FileViewWidget), m_parentId(0),
      m_contextMenu(nullptr) {
  // 初始化UI
  ui->setupUi(this);

  // 初始化UI
  initUI();

  // 初始化上下文菜单
  initContextMenu();
}

// 析构函数
FileViewWidget::~FileViewWidget() { delete ui; }

// 设置父目录ID
void FileViewWidget::setParentId(quint32 parentId) { m_parentId = parentId; }

// 获取父目录ID
quint32 FileViewWidget::parentId() const { return m_parentId; }

// 设置文件列表
void FileViewWidget::setFileList(const QList<FileInfo> &fileList) {
  // 保存文件列表
  m_fileList = fileList;

  // 更新视图
  updateView();
}

// 初始化UI
void FileViewWidget::initUI() {
  // 创建标准项模型
  QStandardItemModel *listModel = new QStandardItemModel(this);
  QStandardItemModel *iconModel = new QStandardItemModel(this);

  // 设置模型
  ui->listView->setModel(listModel);
  ui->iconView->setModel(iconModel);

  // 连接信号槽
  connect(ui->listView, &QListView::doubleClicked, this,
          &FileViewWidget::on_listView_doubleClicked);
  connect(ui->iconView, &QListView::doubleClicked, this,
          &FileViewWidget::on_iconView_doubleClicked);
  connect(ui->listView, &QListView::clicked, this,
          &FileViewWidget::on_listView_clicked);
  connect(ui->iconView, &QListView::clicked, this,
          &FileViewWidget::on_iconView_clicked);

  // 设置按钮状态
  ui->pushButton_download->setEnabled(false);
  ui->pushButton_delete->setEnabled(false);
  ui->pushButton_rename->setEnabled(false);
}

// 初始化上下文菜单
void FileViewWidget::initContextMenu() {
  // 创建上下文菜单
  m_contextMenu = new QMenu(this);

  // 添加动作
  m_actionOpen = m_contextMenu->addAction(QIcon(":/icons/folder.png"), "打开");
  m_actionDownload =
      m_contextMenu->addAction(QIcon(":/icons/download.png"), "下载");
  m_actionShare = m_contextMenu->addAction(QIcon(":/icons/share.png"), "分享");
  m_contextMenu->addSeparator();
  m_actionRename =
      m_contextMenu->addAction(QIcon(":/icons/rename.png"), "重命名");
  m_actionDelete =
      m_contextMenu->addAction(QIcon(":/icons/delete.png"), "删除");
  m_actionCancel =
      m_contextMenu->addAction(QIcon(":/icons/error.png"), "取消操作");

  // 连接信号槽
  connect(m_actionOpen, &QAction::triggered, [this]() {
    FileInfo fileInfo = getSelectedFile();
    if (fileInfo.isDir) {
      emit openDirectoryRequested(fileInfo.fileId, fileInfo.fileName);
    } else {
      emit downloadFileRequested(fileInfo.fileId);
    }
  });

  connect(m_actionDownload, &QAction::triggered, [this]() {
    FileInfo fileInfo = getSelectedFile();
    emit downloadFileRequested(fileInfo.fileId);
  });

  connect(m_actionShare, &QAction::triggered, [this]() {
    onShareFileRequested(0); // 0表示使用当前选中的文件
  });

  connect(m_actionCancel, &QAction::triggered,
          [this]() { handleCancelOperation(); });

  connect(m_actionRename, &QAction::triggered, [this]() {
    FileInfo fileInfo = getSelectedFile();
    bool ok;
    QString newName = QInputDialog::getText(this, "重命名",
                                            "请输入新名称:", QLineEdit::Normal,
                                            fileInfo.fileName, &ok);
    if (ok && !newName.isEmpty()) {
      emit renameFileRequested(fileInfo.fileId, newName);
    }
  });

  connect(m_actionDelete, &QAction::triggered, [this]() {
    FileInfo fileInfo = getSelectedFile();
    int ret = QMessageBox::question(
        this, "删除", QString("确定要删除 %1 吗？").arg(fileInfo.fileName),
        QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
      emit deleteFileRequested(fileInfo.fileId);
    }
  });
}

// 更新视图
void FileViewWidget::updateView() {
  // 获取模型
  QStandardItemModel *listModel =
      qobject_cast<QStandardItemModel *>(ui->listView->model());
  QStandardItemModel *iconModel =
      qobject_cast<QStandardItemModel *>(ui->iconView->model());

  // 清空模型
  listModel->clear();
  iconModel->clear();

  // 添加文件项
  for (const FileInfo &fileInfo : m_fileList) {
    // 创建列表项
    QStandardItem *listItem = new QStandardItem();
    listItem->setText(fileInfo.fileName);

    // 创建图标项
    QStandardItem *iconItem = new QStandardItem();
    iconItem->setText(fileInfo.fileName);

    // 设置图标
    if (fileInfo.isDir) {
      listItem->setIcon(QIcon(":/icons/folder.png"));
      iconItem->setIcon(QIcon(":/icons/folder.png"));
    } else {
      // 根据文件类型设置图标
      QString fileType = fileInfo.fileType.toLower();
      if (fileType == "txt") {
        listItem->setIcon(QIcon(":/icons/file_text.png"));
        iconItem->setIcon(QIcon(":/icons/file_text.png"));
      } else if (fileType == "pdf") {
        listItem->setIcon(QIcon(":/icons/file_text.png"));
        iconItem->setIcon(QIcon(":/icons/file_text.png"));
      } else if (fileType == "doc" || fileType == "docx") {
        listItem->setIcon(QIcon(":/icons/file_text.png"));
        iconItem->setIcon(QIcon(":/icons/file_text.png"));
      } else if (fileType == "xls" || fileType == "xlsx") {
        listItem->setIcon(QIcon(":/icons/file_text.png"));
        iconItem->setIcon(QIcon(":/icons/file_text.png"));
      } else if (fileType == "ppt" || fileType == "pptx") {
        listItem->setIcon(QIcon(":/icons/file_text.png"));
        iconItem->setIcon(QIcon(":/icons/file_text.png"));
      } else if (fileType == "jpg" || fileType == "jpeg" || fileType == "png" ||
                 fileType == "gif" || fileType == "bmp") {
        listItem->setIcon(QIcon(":/icons/file_image.png"));
        iconItem->setIcon(QIcon(":/icons/file_image.png"));
      } else if (fileType == "mp3" || fileType == "wav" || fileType == "flac") {
        listItem->setIcon(QIcon(":/icons/file_audio.png"));
        iconItem->setIcon(QIcon(":/icons/file_audio.png"));
      } else if (fileType == "mp4" || fileType == "avi" || fileType == "mkv") {
        listItem->setIcon(QIcon(":/icons/file_video.png"));
        iconItem->setIcon(QIcon(":/icons/file_video.png"));
      } else if (fileType == "zip" || fileType == "rar" || fileType == "7z") {
        listItem->setIcon(QIcon(":/icons/file_archive.png"));
        iconItem->setIcon(QIcon(":/icons/file_archive.png"));
      } else {
        listItem->setIcon(QIcon(":/icons/file.png"));
        iconItem->setIcon(QIcon(":/icons/file.png"));
      }
    }

    // 设置数据
    listItem->setData(QVariant::fromValue(fileInfo), Qt::UserRole);
    iconItem->setData(QVariant::fromValue(fileInfo), Qt::UserRole);

    // 添加到模型
    listModel->appendRow(listItem);
    iconModel->appendRow(iconItem);
  }

  // 更新状态栏
  ui->label_count->setText(QString("共 %1 项").arg(m_fileList.size()));
}

// 获取选中的文件
FileInfo FileViewWidget::getSelectedFile() const {
  // 获取当前视图
  QListView *currentView =
      (ui->stackedWidget->currentIndex() == 0) ? ui->listView : ui->iconView;

  // 获取选中的索引
  QModelIndexList selectedIndexes =
      currentView->selectionModel()->selectedIndexes();
  if (selectedIndexes.isEmpty()) {
    return FileInfo();
  }

  // 获取选中的项
  QModelIndex selectedIndex = selectedIndexes.first();
  QStandardItemModel *model =
      qobject_cast<QStandardItemModel *>(currentView->model());
  QStandardItem *item = model->itemFromIndex(selectedIndex);

  // 返回文件信息
  return item->data(Qt::UserRole).value<FileInfo>();
}

// 上下文菜单事件
void FileViewWidget::contextMenuEvent(QContextMenuEvent *event) {
  // 获取选中的文件
  FileInfo fileInfo = getSelectedFile();

  // 如果没有选中文件，则不显示上下文菜单
  if (fileInfo.fileId == 0) {
    return;
  }

  // 根据文件类型设置菜单项
  if (fileInfo.isDir) {
    m_actionOpen->setText("打开");
    m_actionDownload->setVisible(false);
    m_actionShare->setVisible(false);
  } else {
    m_actionOpen->setText("下载");
    m_actionDownload->setVisible(true);
    m_actionShare->setVisible(true);
  }

  // 根据是否有正在进行的操作设置取消菜单项
  bool hasActiveOperation = false;
  if (m_networkManager) {
    hasActiveOperation = (m_networkManager->getFileOperationType(
                              fileInfo.fileId) != UploadOperation ||
                          m_networkManager->getFileOperationQueueSize() > 0);
  }
  m_actionCancel->setVisible(hasActiveOperation);

  // 显示上下文菜单
  m_contextMenu->popup(event->globalPos());
}

// 列表视图双击槽函数
void FileViewWidget::on_listView_doubleClicked(const QModelIndex &index) {
  // 获取选中的项
  QStandardItemModel *model =
      qobject_cast<QStandardItemModel *>(ui->listView->model());
  QStandardItem *item = model->itemFromIndex(index);
  FileInfo fileInfo = item->data(Qt::UserRole).value<FileInfo>();

  // 如果是目录，则打开目录
  if (fileInfo.isDir) {
    emit openDirectoryRequested(fileInfo.fileId, fileInfo.fileName);
  } else {
    // 如果是文件，则下载
    emit downloadFileRequested(fileInfo.fileId);
  }
}

// 图标视图双击槽函数
void FileViewWidget::on_iconView_doubleClicked(const QModelIndex &index) {
  // 获取选中的项
  QStandardItemModel *model =
      qobject_cast<QStandardItemModel *>(ui->iconView->model());
  QStandardItem *item = model->itemFromIndex(index);
  FileInfo fileInfo = item->data(Qt::UserRole).value<FileInfo>();

  // 如果是目录，则打开目录
  if (fileInfo.isDir) {
    emit openDirectoryRequested(fileInfo.fileId, fileInfo.fileName);
  } else {
    // 如果是文件，则下载
    emit downloadFileRequested(fileInfo.fileId);
  }
}

// 列表视图点击槽函数
void FileViewWidget::on_listView_clicked(const QModelIndex &index) {
  // 获取选中的项
  QStandardItemModel *model =
      qobject_cast<QStandardItemModel *>(ui->listView->model());
  QStandardItem *item = model->itemFromIndex(index);
  FileInfo fileInfo = item->data(Qt::UserRole).value<FileInfo>();

  // 发送文件选中信号
  emit fileSelected(fileInfo);

  // 更新按钮状态
  ui->pushButton_download->setEnabled(!fileInfo.isDir);
  ui->pushButton_delete->setEnabled(true);
  ui->pushButton_rename->setEnabled(true);
  // ui->pushButton_share->setVisible(!fileInfo.isDir); //
  // 暂时注释，UI中没有此按钮

  // 根据是否有正在进行的操作设置取消按钮
  bool hasActiveOperation = false;
  if (m_networkManager) {
    hasActiveOperation = (m_networkManager->getFileOperationType(
                              fileInfo.fileId) != UploadOperation ||
                          m_networkManager->getFileOperationQueueSize() > 0);
  }
  // ui->pushButton_cancel->setVisible(hasActiveOperation); //
  // 暂时注释，UI中没有此按钮
}

// 图标视图点击槽函数
void FileViewWidget::on_iconView_clicked(const QModelIndex &index) {
  // 获取选中的项
  QStandardItemModel *model =
      qobject_cast<QStandardItemModel *>(ui->iconView->model());
  QStandardItem *item = model->itemFromIndex(index);
  FileInfo fileInfo = item->data(Qt::UserRole).value<FileInfo>();

  // 发送文件选中信号
  emit fileSelected(fileInfo);

  // 更新按钮状态
  ui->pushButton_download->setEnabled(!fileInfo.isDir);
  ui->pushButton_delete->setEnabled(true);
  ui->pushButton_rename->setEnabled(true);
  // ui->pushButton_share->setVisible(!fileInfo.isDir); //
  // 暂时注释，UI中没有此按钮

  // 根据是否有正在进行的操作设置取消按钮
  bool hasActiveOperation = false;
  if (m_networkManager) {
    hasActiveOperation = (m_networkManager->getFileOperationType(
                              fileInfo.fileId) != UploadOperation ||
                          m_networkManager->getFileOperationQueueSize() > 0);
  }
  // ui->pushButton_cancel->setVisible(hasActiveOperation); //
  // 暂时注释，UI中没有此按钮
}

// 刷新按钮点击槽函数
void FileViewWidget::on_pushButton_refresh_clicked() {
  // 请求刷新文件列表
  // 这里应该通过信号通知父窗口刷新文件列表
  // 由于FileViewWidget不知道如何刷新，所以需要父窗口处理
}

// 上传按钮点击槽函数
void FileViewWidget::on_pushButton_upload_clicked() {
  // 打开文件选择对话框
  QStringList filePaths = QFileDialog::getOpenFileNames(
      this, "选择文件",
      QStandardPaths::writableLocation(QStandardPaths::HomeLocation));

  // 如果没有选择文件，则返回
  if (filePaths.isEmpty()) {
    return;
  }

  // 发送上传文件请求
  for (const QString &filePath : filePaths) {
    emit uploadFileRequested(filePath);
  }
}

// 下载按钮点击槽函数
void FileViewWidget::on_pushButton_download_clicked() {
  // 获取选中的文件
  FileInfo fileInfo = getSelectedFile();

  // 如果没有选中文件，则返回
  if (fileInfo.fileId == 0) {
    return;
  }

  // 发送下载文件请求
  emit downloadFileRequested(fileInfo.fileId);
}

// 新建文件夹按钮点击槽函数
void FileViewWidget::on_pushButton_new_folder_clicked() {
  // 输入文件夹名称
  bool ok;
  QString folderName = QInputDialog::getText(
      this, "新建文件夹", "请输入文件夹名称:", QLineEdit::Normal, "新建文件夹",
      &ok);

  // 如果取消或名称为空，则返回
  if (!ok || folderName.isEmpty()) {
    return;
  }

  // 发送新建文件夹请求
  emit newFolderRequested(folderName);
}

// 删除按钮点击槽函数
void FileViewWidget::on_pushButton_delete_clicked() {
  // 获取选中的文件
  FileInfo fileInfo = getSelectedFile();

  // 如果没有选中文件，则返回
  if (fileInfo.fileId == 0) {
    return;
  }

  // 确认删除
  int ret = QMessageBox::question(
      this, "删除", QString("确定要删除 %1 吗？").arg(fileInfo.fileName),
      QMessageBox::Yes | QMessageBox::No);

  // 如果取消，则返回
  if (ret != QMessageBox::Yes) {
    return;
  }

  // 发送删除文件请求
  emit deleteFileRequested(fileInfo.fileId);
}

// 重命名按钮点击槽函数
void FileViewWidget::on_pushButton_rename_clicked() {
  // 获取选中的文件
  FileInfo fileInfo = getSelectedFile();

  // 如果没有选中文件，则返回
  if (fileInfo.fileId == 0) {
    return;
  }

  // 输入新名称
  bool ok;
  QString newName =
      QInputDialog::getText(this, "重命名", "请输入新名称:", QLineEdit::Normal,
                            fileInfo.fileName, &ok);

  // 如果取消或名称为空，则返回
  if (!ok || newName.isEmpty()) {
    return;
  }

  // 发送重命名文件请求
  emit renameFileRequested(fileInfo.fileId, newName);
}

// 搜索按钮点击槽函数
void FileViewWidget::on_pushButton_search_clicked() {
  // 获取搜索关键词
  QString keyword = ui->lineEdit_search->text().trimmed();

  // 如果关键词为空，则返回
  if (keyword.isEmpty()) {
    return;
  }

  // 创建文件搜索对话框
  FileSearchDialog *searchDialog = new FileSearchDialog(this);
  searchDialog->setParentId(m_parentId);

  // 连接信号槽
  connect(searchDialog, &FileSearchDialog::searchFileRequested, this,
          &FileViewWidget::onSearchFileRequested);
  connect(searchDialog, &FileSearchDialog::openDirectoryRequested, this,
          &FileViewWidget::onOpenDirectoryRequested);
  connect(searchDialog, &FileSearchDialog::downloadFileRequested, this,
          &FileViewWidget::onDownloadFileRequested);

  // 显示对话框
  searchDialog->show();
}

// 取消文件操作
void FileViewWidget::cancelFileOperation(quint32 fileId) {
  if (m_networkManager) {
    m_networkManager->cancelFileOperation(fileId);
  }
}

// 文件搜索对话框槽函数
void FileViewWidget::onSearchFileRequested(const QString &keyword) {
  // 发送搜索文件请求
  emit searchFileRequested(keyword);
}

// 取消操作的处理逻辑
void FileViewWidget::handleCancelOperation() {
  FileInfo fileInfo = getSelectedFile();
  if (fileInfo.fileId != 0) {
    // 取消文件操作
    m_networkManager->cancelFileOperation(fileInfo.fileId);
    QMessageBox::information(this, "取消操作", "已取消文件操作");
  } else {
    QMessageBox::information(this, "取消操作", "请先选择一个文件");
  }
}

// 打开目录请求槽函数
void FileViewWidget::onOpenDirectoryRequested(quint32 fileId,
                                              const QString &dirName) {
  // 打开目录
  emit openDirectoryRequested(fileId, dirName);
}

// 下载文件请求槽函数
void FileViewWidget::onDownloadFileRequested(quint32 fileId) {
  // 下载文件
  emit downloadFileRequested(fileId);
}

// 文件分享对话框槽函数
void FileViewWidget::onShareFileRequested(quint32 fileId) {
  // 获取选中的文件
  FileInfo fileInfo = getSelectedFile();
  if (fileInfo.fileId == 0 || fileInfo.isDir) {
    QMessageBox::warning(this, "警告", "请选择一个文件进行分享");
    return;
  }

  // 创建文件分享对话框
  FileShareDialog *shareDialog = new FileShareDialog(this);
  shareDialog->setFileInfo(fileInfo);

  // 连接信号槽
  connect(shareDialog, &FileShareDialog::shareFileRequested, this,
          [this, fileId](quint32, const QString &) {
            emit shareFileRequested(fileId);
          });

  // 显示对话框
  shareDialog->show();
}

// 文件分享完成槽函数
void FileViewWidget::onShareFileCompleted(bool success, const QString &message,
                                          const QString &shareCode) {
  if (success) {
    QMessageBox::information(
        this, "分享成功", QString("文件分享成功，分享码：%1").arg(shareCode));
  } else {
    QMessageBox::warning(this, "分享失败", message);
  }
}

// 拖放事件处理
void FileViewWidget::dragEnterEvent(QDragEnterEvent *event) {
  // 检查是否是拖放文件
  if (event->mimeData()->hasUrls()) {
    // 接受拖放
    event->acceptProposedAction();
  }
}

// 拖放事件处理
void FileViewWidget::dropEvent(QDropEvent *event) {
  // 获取拖放的URL列表
  QList<QUrl> urls = event->mimeData()->urls();

  // 处理拖放的文件
  handleDroppedFiles(urls);

  // 接受拖放
  event->acceptProposedAction();
}

// 处理拖放文件
void FileViewWidget::handleDroppedFiles(const QList<QUrl> &urls) {
  // 遍历所有URL
  for (const QUrl &url : urls) {
    // 获取本地文件路径
    QString localPath = url.toLocalFile();

    // 检查是否是文件
    QFileInfo fileInfo(localPath);
    if (fileInfo.isFile()) {
      // 检查文件是否存在
      if (!fileInfo.exists()) {
        QMessageBox::warning(this, "错误",
                             QString("文件不存在: %1").arg(localPath));
        continue;
      }

      // 检查文件是否可读
      if (!fileInfo.isReadable()) {
        QMessageBox::warning(this, "错误",
                             QString("文件不可读: %1").arg(localPath));
        continue;
      }

      // 检查文件大小是否超过限制（例如100MB）
      qint64 maxSize = 100 * 1024 * 1024; // 100MB
      if (fileInfo.size() > maxSize) {
        int ret = QMessageBox::warning(
            this, "文件过大",
            QString("文件 %1 大小超过100MB限制，是否继续上传？")
                .arg(fileInfo.fileName()),
            QMessageBox::Yes | QMessageBox::No);
        if (ret == QMessageBox::No) {
          continue;
        }
      }

      // 发送上传文件请求
      emit uploadFileRequested(localPath);
    }
  }
}

// 文件拖放槽函数
void FileViewWidget::handleFileDrop(const QList<QUrl> &urls) {
  // 处理拖放的文件
  handleDroppedFiles(urls);
}
