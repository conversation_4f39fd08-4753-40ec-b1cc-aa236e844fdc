#ifndef FRIENDSEARCHDIALOG_H
#define FRIENDSEARCHDIALOG_H

#include "../Common/common.h"
#include <QDialog>
#include <QListWidgetItem>
#include <QMessageBox>

QT_BEGIN_NAMESPACE
namespace Ui {
class FriendSearchDialog;
}
QT_END_NAMESPACE

class FriendSearchDialog : public QDialog {
  Q_OBJECT

public:
  explicit FriendSearchDialog(QWidget *parent = nullptr);
  ~FriendSearchDialog();

  // 设置用户ID
  void setUserId(quint32 userId);

  // 获取用户ID
  quint32 userId() const;

  // 设置搜索结果
  void setSearchResults(const QList<UserInfo> &userList);

signals:
  // 搜索用户请求信号
  void searchUserRequested(const QString &keyword);

  // 添加好友请求信号
  void addFriendRequested(quint32 friendId);

private slots:
  // 搜索按钮点击槽函数
  void on_pushButton_search_clicked();

  // 添加好友按钮点击槽函数
  void on_pushButton_add_clicked();

  // 用户列表双击槽函数
  void on_listWidget_users_itemDoubleClicked(QListWidgetItem *item);

  // 搜索用户槽函数
  void searchUsers();

  // 搜索结果响应槽函数
  void onSearchUserResponse(bool success, const QString &message,
                            const QList<UserInfo> &userList);

  // 添加好友按钮点击槽函数
  void on_pushButton_add_friend_clicked();

  // 关闭按钮点击槽函数
  void on_pushButton_close_clicked();

private:
  // 初始化UI
  void initUI();

  // 加载用户列表
  void loadUserList(const QList<UserInfo> &userList);

  // 获取选中的用户
  UserInfo getSelectedUser() const;

  Ui::FriendSearchDialog *ui; // UI对象
  quint32 m_userId;           // 当前用户ID
  QList<UserInfo> m_userList; // 搜索结果用户列表
};

#endif // FRIENDSEARCHDIALOG_H
