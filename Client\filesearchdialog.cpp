#include "filesearchdialog.h"
#include "ui_filesearchdialog.h"
#include "logger.h"
#include <QDateTime>

// 构造函数
FileSearchDialog::FileSearchDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::FileSearchDialog),
    m_userId(0),
    m_parentId(0)
{
    // 初始化UI
    ui->setupUi(this);

    // 初始化UI
    initUI();
}

// 析构函数
FileSearchDialog::~FileSearchDialog()
{
    delete ui;
}

// 设置用户ID
void FileSearchDialog::setUserId(quint32 userId)
{
    m_userId = userId;
}

// 获取用户ID
quint32 FileSearchDialog::userId() const
{
    return m_userId;
}

// 设置父目录ID
void FileSearchDialog::setParentId(quint32 parentId)
{
    m_parentId = parentId;
}

// 获取父目录ID
quint32 FileSearchDialog::parentId() const
{
    return m_parentId;
}

// 设置搜索结果
void FileSearchDialog::setSearchResults(const QList<FileInfo>& searchResults)
{
    // 保存搜索结果
    m_searchResults = searchResults;

    // 加载搜索结果
    loadSearchResults(searchResults);
}

// 搜索按钮点击槽函数
void FileSearchDialog::on_pushButton_search_clicked()
{
    // 获取搜索关键词
    QString keyword = ui->lineEdit_keyword->text().trimmed();

    // 检查关键词是否为空
    if (keyword.isEmpty()) {
        QMessageBox::warning(this, "搜索提示", "请输入搜索关键词");
        return;
    }

    // 发送搜索文件请求信号
    emit searchFileRequested(keyword, m_parentId);
}

// 搜索结果双击槽函数
void FileSearchDialog::on_listWidget_results_itemDoubleClicked(QListWidgetItem *item)
{
    // 获取文件ID
    quint32 fileId = item->data(Qt::UserRole).toUInt();

    // 检查是否是目录
    bool isDir = item->data(Qt::UserRole + 1).toBool();

    if (isDir) {
        // 如果是目录，发送打开目录请求信号
        emit openDirectoryRequested(fileId, item->text());
    } else {
        // 如果是文件，发送下载文件请求信号
        emit downloadFileRequested(fileId);
    }
}

// 关闭按钮点击槽函数
void FileSearchDialog::on_pushButton_close_clicked()
{
    // 关闭对话框
    reject();
}

// 初始化UI
void FileSearchDialog::initUI()
{
    // 设置窗口标题
    setWindowTitle("文件搜索");

    // 设置窗口图标
    setWindowIcon(QIcon(":/icons/search.png"));

    // 设置窗口大小
    resize(600, 400);

    // 设置列表属性
    ui->listWidget_results->setAlternatingRowColors(true);
    ui->listWidget_results->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->listWidget_results->setSelectionBehavior(QAbstractItemView::SelectRows);

    // 连接信号槽
    connect(ui->pushButton_search, &QPushButton::clicked, this, &FileSearchDialog::on_pushButton_search_clicked);
    connect(ui->listWidget_results, &QListWidget::itemDoubleClicked, this, &FileSearchDialog::on_listWidget_results_itemDoubleClicked);
    connect(ui->pushButton_close, &QPushButton::clicked, this, &FileSearchDialog::on_pushButton_close_clicked);
}

// 加载搜索结果
void FileSearchDialog::loadSearchResults(const QList<FileInfo>& searchResults)
{
    // 清空列表
    ui->listWidget_results->clear();

    // 添加搜索结果到列表
    for (const FileInfo& fileInfo : searchResults) {
        // 创建列表项
        QListWidgetItem* item = new QListWidgetItem();

        // 设置文件名
        item->setText(fileInfo.fileName);

        // 设置文件ID
        item->setData(Qt::UserRole, fileInfo.fileId);

        // 设置是否为目录
        item->setData(Qt::UserRole + 1, fileInfo.isDir);

        // 设置文件图标
        QString iconPath = getFileIcon(fileInfo.fileType);
        if (!iconPath.isEmpty()) {
            item->setIcon(QIcon(iconPath));
        } else {
            // 如果没有特定图标，使用默认图标
            item->setIcon(QIcon(":/icons/file.png"));
        }

        // 添加到列表
        ui->listWidget_results->addItem(item);
    }

    LOG_INFO("FileSearch", QString("加载搜索结果完成，共 %1 个文件").arg(searchResults.size()));
}

// 获取文件图标
QString FileSearchDialog::getFileIcon(const QString& fileType) const
{
    // 根据文件类型返回图标路径
    QString type = fileType.toLower();

    if (type == "txt") {
        return ":/icons/file_text.png";
    } else if (type == "pdf") {
        return ":/icons/file_text.png";
    } else if (type == "doc" || type == "docx") {
        return ":/icons/file_text.png";
    } else if (type == "xls" || type == "xlsx") {
        return ":/icons/file_text.png";
    } else if (type == "ppt" || type == "pptx") {
        return ":/icons/file_text.png";
    } else if (type == "jpg" || type == "jpeg" || type == "png" || 
               type == "gif" || type == "bmp") {
        return ":/icons/file_image.png";
    } else if (type == "mp3" || type == "wav" || type == "flac") {
        return ":/icons/file_audio.png";
    } else if (type == "mp4" || type == "avi" || type == "mkv") {
        return ":/icons/file_video.png";
    } else if (type == "zip" || type == "rar" || type == "7z") {
        return ":/icons/file_archive.png";
    } else {
        return QString();
    }
}
