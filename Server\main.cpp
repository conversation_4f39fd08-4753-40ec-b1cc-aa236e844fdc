#include <QCoreApplication>
#include "server.h"
#include "configmanager.h"
#include "logger.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);

    // 设置应用程序信息
    app.setApplicationName("Cloud7Server");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("Cloud7Team");

    // 初始化配置管理器
    ConfigManager::getInstance()->loadConfig();

    // 初始化日志系统
    Logger::getInstance()->setLogLevel(LOG_INFO);

    // 创建并启动服务器
    Server server;
    if (!server.start()) {
        LOG_ERROR("Main", "服务器启动失败");
        return -1;
    }

    LOG_INFO("Main", "服务器启动成功");

    // 运行应用程序
    return app.exec();
}
