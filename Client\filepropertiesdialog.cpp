#include "filepropertiesdialog.h"
#include "ui_filepropertiesdialog.h"
#include "../Common/utils.h"

// 构造函数
FilePropertiesDialog::FilePropertiesDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::FilePropertiesDialog)
{
    // 初始化UI
    ui->setupUi(this);

    // 初始化UI
    initUI();
}

// 析构函数
FilePropertiesDialog::~FilePropertiesDialog()
{
    delete ui;
}

// 设置文件信息
void FilePropertiesDialog::setFileInfo(const FileInfo& fileInfo)
{
    // 保存文件信息
    m_fileInfo = fileInfo;

    // 加载文件信息
    loadFileInfo();
}

// 获取文件信息
FileInfo FilePropertiesDialog::getFileInfo() const
{
    return m_fileInfo;
}

// 确定按钮点击槽函数
void FilePropertiesDialog::on_pushButton_ok_clicked()
{
    // 关闭对话框
    accept();
}

// 初始化UI
void FilePropertiesDialog::initUI()
{
    // 设置窗口标题
    setWindowTitle("文件属性");

    // 设置窗口图标
    setWindowIcon(QIcon(":/icons/properties.png"));

    // 设置窗口大小
    resize(400, 350);

    // 连接信号槽
    connect(ui->pushButton_ok, &QPushButton::clicked, this, &FilePropertiesDialog::on_pushButton_ok_clicked);
}

// 加载文件信息
void FilePropertiesDialog::loadFileInfo()
{
    // 设置文件名
    ui->label_name_value->setText(m_fileInfo.fileName);

    // 设置文件类型
    QString fileType = m_fileInfo.fileType.toUpper();
    if (fileType.isEmpty()) {
        fileType = m_fileInfo.isDir ? "文件夹" : "文件";
    }
    ui->label_type_value->setText(fileType);

    // 设置文件大小
    ui->label_size_value->setText(formatFileSize(m_fileInfo.fileSize));

    // 设置文件哈希值
    ui->label_hash_value->setText(m_fileInfo.fileHash.isEmpty() ? "-" : m_fileInfo.fileHash);

    // 设置创建时间
    ui->label_create_time_value->setText(m_fileInfo.createTime);

    // 设置修改时间
    ui->label_modify_time_value->setText(m_fileInfo.modifyTime);

    // 设置文件路径
    ui->label_path_value->setText(m_fileInfo.filePath);
}

// 格式化文件大小
QString FilePropertiesDialog::formatFileSize(quint64 size) const
{
    if (size < 1024) {
        return QString("%1 B").arg(size);
    } else if (size < 1024 * 1024) {
        return QString("%1 KB").arg(size / 1024.0, 0, 'f', 2);
    } else if (size < 1024 * 1024 * 1024) {
        return QString("%1 MB").arg(size / (1024.0 * 1024.0), 0, 'f', 2);
    } else if (size < 1024 * 1024 * 1024 * 1024) {
        return QString("%1 GB").arg(size / (1024.0 * 1024.0 * 1024.0), 0, 'f', 2);
    } else {
        return QString("%1 TB").arg(size / (1024.0 * 1024.0 * 1024.0 * 1024.0), 0, 'f', 2);
    }
}
