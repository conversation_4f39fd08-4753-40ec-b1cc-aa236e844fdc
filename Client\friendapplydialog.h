#ifndef FRIENDAPPLYDIALOG_H
#define FRIENDAPPLYDIALOG_H

#include "../Common/common.h"
#include <QDialog>


namespace Ui {
class FriendApplyDialog;
}

class FriendApplyDialog : public QDialog {
  Q_OBJECT

public:
  explicit FriendApplyDialog(QWidget *parent = nullptr);
  ~FriendApplyDialog();

  // 设置用户ID
  void setUserId(quint32 userId);

  // 获取用户ID
  quint32 userId() const;

  // 添加好友申请
  void addFriendApply(const UserInfo &applicant, const QString &message);

signals:
  // 同意添加好友请求信号
  void agreeAddFriendRequested(quint32 userId);

  // 拒绝添加好友请求信号
  void rejectAddFriendRequested(quint32 userId);

private slots:
  // 同意按钮点击槽函数
  void on_pushButton_agree_clicked();

  // 拒绝按钮点击槽函数
  void on_pushButton_reject_clicked();

  // 关闭按钮点击槽函数
  void on_pushButton_close_clicked();

private:
  // 初始化UI
  void initUI();

  // 加载好友申请列表
  void loadFriendApplyList(const QList<FriendApplyInfo> &applyList);

  // 生成申请ID（临时方案）
  quint32 generateApplyId();

  Ui::FriendApplyDialog *ui;          // UI对象
  quint32 m_userId;                   // 用户ID
  QList<FriendApplyInfo> m_applyList; // 好友申请列表
};

#endif // FRIENDAPPLYDIALOG_H
