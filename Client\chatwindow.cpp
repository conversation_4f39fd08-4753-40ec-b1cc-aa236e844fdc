#include "chatwindow.h"
#include "logger.h"
#include <QDateTime>
#include <QScrollBar>

// 构造函数
ChatWindow::ChatWindow(QWidget *parent)
    : QMainWindow(parent), ui(new Ui::ChatWindow), m_friendId(0), m_userId(0) {
  // 初始化UI
  ui->setupUi(this);

  // 初始化UI
  initUI();
}

// 析构函数
ChatWindow::~ChatWindow() { delete ui; }

// 设置好友ID
void ChatWindow::setFriendId(quint32 friendId) { m_friendId = friendId; }

// 获取好友ID
quint32 ChatWindow::friendId() const { return m_friendId; }

// 设置好友名称
void ChatWindow::setFriendName(const QString &friendName) {
  m_friendName = friendName;

  // 更新窗口标题
  setWindowTitle(QString("与 %1 的聊天").arg(friendName));
}

// 获取好友名称
QString ChatWindow::friendName() const { return m_friendName; }

// 设置用户ID
void ChatWindow::setUserId(quint32 userId) { m_userId = userId; }

// 获取用户ID
quint32 ChatWindow::userId() const { return m_userId; }

// 设置用户名称
void ChatWindow::setUserName(const QString &userName) { m_userName = userName; }

// 获取用户名称
QString ChatWindow::userName() const { return m_userName; }

// 添加消息
void ChatWindow::addMessage(const MessageInfo &messageInfo) {
  // 获取当前光标位置
  QTextCursor cursor = ui->textEdit_chat->textCursor();
  cursor.movePosition(QTextCursor::End);

  // 根据发送者设置不同的格式
  QTextCharFormat format;
  if (messageInfo.senderId == m_userId) {
    // 自己发送的消息，右对齐，蓝色
    format.setForeground(QColor(0, 0, 255));
    cursor.insertText(
        QString("[%1] %2:\n")
            .arg(formatMessageTime(messageInfo.sendTime), m_userName),
        format);
  } else {
    // 接收的消息，左对齐，绿色
    format.setForeground(QColor(0, 128, 0));
    cursor.insertText(
        QString("[%1] %2:\n")
            .arg(formatMessageTime(messageInfo.sendTime), m_friendName),
        format);
  }

  // 插入消息内容
  format.setForeground(Qt::black);
  cursor.insertText(messageInfo.content + "\n\n", format);

  // 滚动到底部
  QScrollBar *scrollBar = ui->textEdit_chat->verticalScrollBar();
  scrollBar->setValue(scrollBar->maximum());
}

// 设置历史消息
void ChatWindow::setHistoryMessages(const QList<MessageInfo> &messageList) {
  // 清空聊天记录
  ui->textEdit_chat->clear();

  // 添加历史消息
  for (const MessageInfo &messageInfo : messageList) {
    addMessage(messageInfo);
  }
}

// 初始化UI
void ChatWindow::initUI() {
  // 设置窗口图标
  setWindowIcon(QIcon(":/icons/chat.png"));

  // 设置窗口大小
  resize(500, 600);

  // 设置聊天文本框
  ui->textEdit_chat->setReadOnly(true);

  // 连接信号槽
  connect(ui->pushButton_send, &QPushButton::clicked, this,
          &ChatWindow::on_pushButton_send_clicked);
  connect(ui->lineEdit_message, &QLineEdit::returnPressed, this,
          &ChatWindow::on_lineEdit_message_returnPressed);
}

// 格式化消息时间
QString ChatWindow::formatMessageTime(const QString &timeStr) const {
  // 解析时间字符串
  QDateTime dateTime = QDateTime::fromString(timeStr, "yyyy-MM-dd hh:mm:ss");

  // 如果解析失败，返回原字符串
  if (!dateTime.isValid()) {
    return timeStr;
  }

  // 获取当前日期时间
  QDateTime currentDateTime = QDateTime::currentDateTime();

  // 如果是今天的消息，只显示时间
  if (dateTime.date() == currentDateTime.date()) {
    return dateTime.toString("hh:mm:ss");
  }

  // 如果是昨天的消息，显示"昨天 时间"
  if (dateTime.date().addDays(1) == currentDateTime.date()) {
    return QString("昨天 %1").arg(dateTime.toString("hh:mm:ss"));
  }

  // 其他情况，显示完整日期和时间
  return dateTime.toString("yyyy-MM-dd hh:mm:ss");
}

// 发送按钮点击槽函数
void ChatWindow::on_pushButton_send_clicked() {
  // 获取消息内容
  QString content = ui->lineEdit_message->text().trimmed();

  // 如果消息为空，则返回
  if (content.isEmpty()) {
    return;
  }

  // 发送消息
  emit sendMessageRequested(m_friendId, content);

  // 清空输入框
  ui->lineEdit_message->clear();

  // 设置焦点到输入框
  ui->lineEdit_message->setFocus();
}

// 消息输入框回车键按下槽函数
void ChatWindow::on_lineEdit_message_returnPressed() {
  // 调用发送按钮点击槽函数
  on_pushButton_send_clicked();
}
