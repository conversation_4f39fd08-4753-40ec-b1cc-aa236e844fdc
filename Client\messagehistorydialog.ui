<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MessageHistoryDialog</class>
 <widget class="QDialog" name="MessageHistoryDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>消息历史</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icons/history.png</normaloff>:/icons/history.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="label_friend_name">
     <property name="text">
      <string>好友名称：</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QListWidget" name="listWidget_messages">
     <property name="selectionMode">
      <enum>QAbstractItemView::NoSelection</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_buttons">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_load_more">
       <property name="text">
        <string>加载更多</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/load_more.png</normaloff>:/icons/load_more.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_close">
       <property name="text">
        <string>关闭</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/close.png</normaloff>:/icons/close.png</iconset>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
