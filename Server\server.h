#ifndef SERVER_H
#define SERVER_H

#include "../Common/common.h"
#include <QFile>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QMap>
#include <QObject>
#include <QSet>
#include <QTcpServer>
#include <QTcpSocket>
#include <QTimer>

class ClientSession;
class ConfigManager;
class DatabaseManager;

// 服务器类
class Server : public QObject {
  Q_OBJECT

public:
  // 构造函数
  explicit Server(QObject *parent = nullptr);

  // 析构函数
  ~Server();

  // 启动服务器
  bool start();

  // 停止服务器
  void stop();

private slots:
  // 新连接槽函数
  void onNewConnection();

  // 会话超时检查槽函数
  void onSessionTimeoutCheck();

private:
  // 初始化服务器
  bool initialize();

  // 处理客户端断开连接
  void handleClientDisconnected(ClientSession *session);

  // 处理客户端消息
  void handleClientMessage(ClientSession *session, const Packet &packet);

  // 处理注册请求
  void handleRegisterRequest(ClientSession *session,
                             const QJsonObject &jsonObj);

  // 处理登录请求
  void handleLoginRequest(ClientSession *session, const QJsonObject &jsonObj);

  // 处理登出请求
  void handleLogoutRequest(ClientSession *session, const QJsonObject &jsonObj);

  // 处理心跳包
  void handleHeartbeat(ClientSession *session);

  // 处理获取文件列表请求
  void handleGetFileListRequest(ClientSession *session,
                                const QJsonObject &jsonObj);

  // 处理上传文件请求
  void handleUploadFileRequest(ClientSession *session,
                               const QJsonObject &jsonObj);

  // 处理文件数据
  void handleFileData(ClientSession *session, const QByteArray &data);

  // 处理完成文件上传
  void handleCompleteFileUpload(ClientSession *session,
                                const QJsonObject &jsonObj);

  // 处理下载文件请求
  void handleDownloadFileRequest(ClientSession *session,
                                 const QJsonObject &jsonObj);

  // 处理创建目录请求
  void handleCreateDirectoryRequest(ClientSession *session,
                                    const QJsonObject &jsonObj);

  // 处理删除文件请求
  void handleDeleteFileRequest(ClientSession *session,
                               const QJsonObject &jsonObj);

  // 处理重命名文件请求
  void handleRenameFileRequest(ClientSession *session,
                               const QJsonObject &jsonObj);

  // 处理移动文件请求
  void handleMoveFileRequest(ClientSession *session,
                             const QJsonObject &jsonObj);

  // 处理复制文件请求
  void handleCopyFileRequest(ClientSession *session,
                             const QJsonObject &jsonObj);

  // 处理文件分享请求
  void handleShareFileRequest(ClientSession *session,
                              const QJsonObject &jsonObj);

  // 处理搜索文件请求
  void handleSearchFileRequest(ClientSession *session,
                               const QJsonObject &jsonObj);

  // 处理通过分享码获取文件请求
  void handleGetFileByShareCodeRequest(ClientSession *session,
                                       const QJsonObject &jsonObj);

  // 处理搜索用户请求
  void handleSearchUserRequest(ClientSession *session,
                               const QJsonObject &jsonObj);

  // 处理添加好友请求
  void handleAddFriendRequest(ClientSession *session,
                              const QJsonObject &jsonObj);

  // 处理同意添加好友
  void handleAgreeAddFriend(ClientSession *session, const QJsonObject &jsonObj);

  // 处理拒绝添加好友
  void handleRejectAddFriend(ClientSession *session,
                             const QJsonObject &jsonObj);

  // 处理删除好友请求
  void handleDeleteFriendRequest(ClientSession *session,
                                 const QJsonObject &jsonObj);

  // 处理获取好友列表请求
  void handleGetFriendListRequest(ClientSession *session,
                                  const QJsonObject &jsonObj);

  // 处理发送消息
  void handleSendMessage(ClientSession *session, const QJsonObject &jsonObj);

  // 处理获取历史消息请求
  void handleGetMessageHistoryRequest(ClientSession *session,
                                      const QJsonObject &jsonObj);

  // 处理获取离线消息请求
  void handleGetOfflineMessageRequest(ClientSession *session,
                                      const QJsonObject &jsonObj);

  // 发送响应
  void sendResponse(ClientSession *session, quint32 msgType,
                    const QJsonObject &jsonObj);

  // 发送错误响应
  void sendErrorResponse(ClientSession *session, quint32 originalMsgType,
                         const QString &message);

  // 计算密码哈希
  QString calculatePasswordHash(const QString &password, const QString &salt);

  // 生成随机盐值
  QString generateSalt();

  // 生成会话ID
  QString generateSessionId();

  // 计算文件哈希
  QString calculateFileHash(const QString &filePath);

  // 格式化文件大小
  QString formatFileSize(quint64 size) const;

  // 检查用户权限
  bool checkUserPermission(ClientSession *session, quint32 userId);

  // 检查文件权限
  bool checkFilePermission(ClientSession *session, quint32 fileId);

  // 检查存储空间
  bool checkStorageSpace(ClientSession *session, quint64 fileSize);

  // 获取文件路径
  QString getFilePath(quint32 fileId);

  // 创建文件目录
  bool createFileDirectory();

  // 清理过期会话
  void cleanupExpiredSessions();

  // 清理过期分享
  void cleanupExpiredShares();

  QTcpServer *m_tcpServer;                   // TCP服务器
  QMap<QString, ClientSession *> m_sessions; // 会话映射（会话ID -> 会话对象）
  QMap<quint32, ClientSession *>
      m_userSessions;             // 用户会话映射（用户ID -> 会话对象）
  QTimer *m_sessionTimeoutTimer;  // 会话超时检查定时器
  ConfigManager *m_configManager; // 配置管理器
  DatabaseManager *m_dbManager;   // 数据库管理器
  QString m_fileDir;              // 文件存储目录
  bool m_running;                 // 服务器运行状态

  // 文件上传进度跟踪
  QMap<quint32, quint64> m_fileUploadProgress; // 文件上传进度映射
  QMap<quint32, quint64> m_fileUploadSizes;    // 文件上传大小映射
};

// 客户端会话类
class ClientSession : public QObject {
  Q_OBJECT

public:
  // 构造函数
  explicit ClientSession(QTcpSocket *socket, QObject *parent = nullptr);

  // 析构函数
  ~ClientSession();

  // 获取套接字
  QTcpSocket *socket() const;

  // 获取会话ID
  QString sessionId() const;

  // 设置会话ID
  void setSessionId(const QString &sessionId);

  // 获取用户ID
  quint32 userId() const;

  // 设置用户ID
  void setUserId(quint32 userId);

  // 获取用户信息
  UserInfo userInfo() const;

  // 设置用户信息
  void setUserInfo(const UserInfo &userInfo);

  // 获取最后心跳时间
  QDateTime lastHeartbeat() const;

  // 更新心跳时间
  void updateHeartbeat();

  // 发送数据包
  bool sendPacket(const Packet &packet);

  // 发送JSON消息
  bool sendJsonMessage(quint32 msgType, const QJsonObject &jsonObj);

  // 发送文件数据
  bool sendFileData(quint32 msgType, const QByteArray &fileData);

signals:
  // 断开连接信号
  void disconnected(ClientSession *session);

  // 接收到消息信号
  void messageReceived(ClientSession *session, const Packet &packet);

private slots:
  // 接收数据槽函数
  void onReadyRead();

  // 错误处理槽函数
  void onError(QAbstractSocket::SocketError socketError);

  // 断开连接槽函数
  void onDisconnected();

public:
  // 文件传输相关的公有成员
  QMap<quint32, QFile *> m_uploadFiles;   // 上传文件映射
  QMap<quint32, QFile *> m_downloadFiles; // 下载文件映射

private:
  // 处理接收到的数据
  void processData(const QByteArray &data);

  // 处理数据包
  void processPacket(const Packet &packet);

  // 计算校验和
  quint32 calculateChecksum(const QByteArray &data) const;

  QTcpSocket *m_socket;      // TCP套接字
  QByteArray m_buffer;       // 接收缓冲区
  quint32 m_expectedSize;    // 期望接收的数据大小
  QString m_sessionId;       // 会话ID
  quint32 m_userId;          // 用户ID
  UserInfo m_userInfo;       // 用户信息
  QDateTime m_lastHeartbeat; // 最后心跳时间
};

#endif // SERVER_H
