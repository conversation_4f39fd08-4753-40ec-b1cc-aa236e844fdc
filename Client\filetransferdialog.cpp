#include "filetransferdialog.h"
#include "logger.h"
#include "ui_filetransferdialog.h"
#include <QDateTime>
#include <QHeaderView>

// 构造函数
FileTransferDialog::FileTransferDialog(QWidget *parent)
    : QDialog(parent), ui(new Ui::FileTransferDialog),
      m_transferManager(nullptr) {
  // 初始化UI
  ui->setupUi(this);

  // 初始化成员变量
  m_transferManager = FileTransferManager::getInstance();

  // 初始化UI
  initUI();

  // 初始化表格
  initTable();

  // 连接信号槽
  connect(m_transferManager, &FileTransferManager::taskStatusChanged, this,
          &FileTransferDialog::onTaskStatusChanged);
  connect(m_transferManager, &FileTransferManager::taskProgressUpdated, this,
          &FileTransferDialog::onTaskProgressUpdated);
  connect(m_transferManager, &FileTransferManager::taskSpeedUpdated, this,
          &FileTransferDialog::onTaskSpeedUpdated);

  // 更新任务列表
  updateTaskList();
}

// 析构函数
FileTransferDialog::~FileTransferDialog() { delete ui; }

// 添加上传任务
quint32 FileTransferDialog::addUploadTask(const QString &localPath,
                                          quint32 parentId) {
  // 添加任务
  quint32 taskId = m_transferManager->addUploadTask(localPath, parentId);

  // 更新任务列表
  updateTaskList();

  return taskId;
}

// 添加下载任务
quint32 FileTransferDialog::addDownloadTask(quint32 fileId,
                                            const QString &localPath) {
  // 添加任务
  quint32 taskId = m_transferManager->addDownloadTask(fileId, localPath);

  // 更新任务列表
  updateTaskList();

  return taskId;
}

// 初始化UI
void FileTransferDialog::initUI() {
  // 设置窗口标题
  setWindowTitle("文件传输");

  // 设置窗口图标
  setWindowIcon(QIcon(":/icons/transfer.png"));

  // 设置窗口大小
  resize(800, 500);

  // 设置按钮状态
  ui->pushButton_start->setEnabled(false);
  ui->pushButton_pause->setEnabled(false);
  ui->pushButton_resume->setEnabled(false);
  ui->pushButton_cancel->setEnabled(false);
}

// 初始化表格
void FileTransferDialog::initTable() {
  // 设置表格属性
  ui->tableWidget_tasks->setAlternatingRowColors(true);
  ui->tableWidget_tasks->setSelectionMode(QAbstractItemView::SingleSelection);
  ui->tableWidget_tasks->setSelectionBehavior(QAbstractItemView::SelectRows);
  ui->tableWidget_tasks->setEditTriggers(QAbstractItemView::NoEditTriggers);

  // 设置表头
  QHeaderView *header = ui->tableWidget_tasks->horizontalHeader();
  header->setSectionResizeMode(0, QHeaderView::ResizeToContents); // 任务ID
  header->setSectionResizeMode(1, QHeaderView::Stretch);          // 文件名
  header->setSectionResizeMode(2, QHeaderView::ResizeToContents); // 类型
  header->setSectionResizeMode(3, QHeaderView::ResizeToContents); // 大小
  header->setSectionResizeMode(4, QHeaderView::ResizeToContents); // 进度
  header->setSectionResizeMode(5, QHeaderView::ResizeToContents); // 速度
  header->setSectionResizeMode(6, QHeaderView::ResizeToContents); // 剩余时间
  header->setSectionResizeMode(7, QHeaderView::ResizeToContents); // 状态

  // 连接信号槽
  connect(ui->tableWidget_tasks, &QTableWidget::itemClicked, this,
          &FileTransferDialog::on_tableWidget_tasks_itemClicked);
}

// 更新任务列表
void FileTransferDialog::updateTaskList() {
  // 清空映射
  m_taskRowMap.clear();

  // 清空表格
  ui->tableWidget_tasks->setRowCount(0);

  // 获取任务列表
  QList<TransferTask> taskList = m_transferManager->getTaskList();

  // 添加任务到表格
  for (const TransferTask &task : taskList) {
    // 添加行
    int row = ui->tableWidget_tasks->rowCount();
    ui->tableWidget_tasks->insertRow(row);

    // 添加映射
    m_taskRowMap[task.taskId] = row;

    // 更新任务行
    updateTaskRow(task.taskId);
  }
}

// 更新任务行
void FileTransferDialog::updateTaskRow(quint32 taskId) {
  // 检查映射是否存在
  if (!m_taskRowMap.contains(taskId)) {
    return;
  }

  // 获取行号
  int row = m_taskRowMap[taskId];

  // 获取任务
  TransferTask task = m_transferManager->getTask(taskId);

  // 设置任务ID
  QTableWidgetItem *taskIdItem =
      new QTableWidgetItem(QString::number(task.taskId));
  taskIdItem->setTextAlignment(Qt::AlignCenter);
  ui->tableWidget_tasks->setItem(row, 0, taskIdItem);

  // 设置文件名
  QTableWidgetItem *fileNameItem = new QTableWidgetItem(task.fileName);
  ui->tableWidget_tasks->setItem(row, 1, fileNameItem);

  // 设置类型
  QTableWidgetItem *typeItem =
      new QTableWidgetItem(task.type == TRANSFER_UPLOAD ? "上传" : "下载");
  typeItem->setTextAlignment(Qt::AlignCenter);
  ui->tableWidget_tasks->setItem(row, 2, typeItem);

  // 设置大小
  QTableWidgetItem *sizeItem =
      new QTableWidgetItem(formatFileSize(task.fileSize));
  sizeItem->setTextAlignment(Qt::AlignCenter);
  ui->tableWidget_tasks->setItem(row, 3, sizeItem);

  // 设置进度
  int progress = 0;
  if (task.fileSize > 0) {
    progress = static_cast<int>(task.bytesTransferred * 100 / task.fileSize);
  }

  QProgressBar *progressBar = new QProgressBar();
  progressBar->setRange(0, 100);
  progressBar->setValue(progress);
  progressBar->setAlignment(Qt::AlignCenter);
  ui->tableWidget_tasks->setCellWidget(row, 4, progressBar);

  // 设置速度
  QTableWidgetItem *speedItem =
      new QTableWidgetItem(formatTransferSpeed(task.speed));
  speedItem->setTextAlignment(Qt::AlignCenter);
  ui->tableWidget_tasks->setItem(row, 5, speedItem);

  // 设置剩余时间
  QTableWidgetItem *remainingTimeItem =
      new QTableWidgetItem(formatRemainingTime(task.remainingTime));
  remainingTimeItem->setTextAlignment(Qt::AlignCenter);
  ui->tableWidget_tasks->setItem(row, 6, remainingTimeItem);

  // 设置状态
  QTableWidgetItem *statusItem =
      new QTableWidgetItem(getStatusString(task.status));
  statusItem->setTextAlignment(Qt::AlignCenter);
  ui->tableWidget_tasks->setItem(row, 7, statusItem);

  // 根据状态设置颜色
  QColor color;
  switch (task.status) {
  case TRANSFER_PENDING:
    color = QColor(128, 128, 128); // 灰色
    break;
  case TRANSFER_TRANSFERING:
    color = QColor(0, 0, 255); // 蓝色
    break;
  case TRANSFER_PAUSED:
    color = QColor(255, 165, 0); // 橙色
    break;
  case TRANSFER_COMPLETED:
    color = QColor(0, 128, 0); // 绿色
    break;
  case TRANSFER_FAILED:
    color = QColor(255, 0, 0); // 红色
    break;
  case TRANSFER_CANCELED:
    color = QColor(128, 128, 128); // 灰色
    break;
  default:
    color = QColor(0, 0, 0); // 黑色
    break;
  }

  for (int col = 0; col < ui->tableWidget_tasks->columnCount(); ++col) {
    QTableWidgetItem *item = ui->tableWidget_tasks->item(row, col);
    if (item) {
      item->setForeground(color);
    }
  }
}

// 获取选中的任务ID
quint32 FileTransferDialog::getSelectedTaskId() const {
  // 获取选中的行
  QList<QTableWidgetItem *> selectedItems =
      ui->tableWidget_tasks->selectedItems();
  if (selectedItems.isEmpty()) {
    return 0;
  }

  // 获取行号
  int row = selectedItems.first()->row();

  // 获取任务ID
  QTableWidgetItem *taskIdItem = ui->tableWidget_tasks->item(row, 0);
  if (!taskIdItem) {
    return 0;
  }

  return taskIdItem->text().toUInt();
}

// 格式化文件大小
QString FileTransferDialog::formatFileSize(quint64 size) const {
  if (size < 1024) {
    return QString("%1 B").arg(size);
  } else if (size < 1024 * 1024) {
    return QString("%1 KB").arg(size / 1024.0, 0, 'f', 2);
  } else if (size < 1024 * 1024 * 1024) {
    return QString("%1 MB").arg(size / (1024.0 * 1024.0), 0, 'f', 2);
  } else {
    return QString("%1 GB").arg(size / (1024.0 * 1024.0 * 1024.0), 0, 'f', 2);
  }
}

// 格式化传输速度
QString FileTransferDialog::formatTransferSpeed(quint32 speed) const {
  if (speed < 1024) {
    return QString("%1 B/s").arg(speed);
  } else if (speed < 1024 * 1024) {
    return QString("%1 KB/s").arg(speed / 1024.0, 0, 'f', 2);
  } else if (speed < 1024 * 1024 * 1024) {
    return QString("%1 MB/s").arg(speed / (1024.0 * 1024.0), 0, 'f', 2);
  } else {
    return QString("%1 GB/s").arg(speed / (1024.0 * 1024.0 * 1024.0), 0, 'f',
                                  2);
  }
}

// 格式化剩余时间
QString FileTransferDialog::formatRemainingTime(int seconds) const {
  if (seconds < 0) {
    return "--:--:--";
  }

  int hours = seconds / 3600;
  int minutes = (seconds % 3600) / 60;
  int secs = seconds % 60;

  return QString("%1:%2:%3")
      .arg(hours, 2, 10, QLatin1Char('0'))
      .arg(minutes, 2, 10, QLatin1Char('0'))
      .arg(secs, 2, 10, QLatin1Char('0'));
}

// 获取状态字符串
QString FileTransferDialog::getStatusString(TransferStatus status) const {
  switch (status) {
  case TRANSFER_PENDING:
    return "等待中";
  case TRANSFER_TRANSFERING:
    return "传输中";
  case TRANSFER_PAUSED:
    return "已暂停";
  case TRANSFER_COMPLETED:
    return "已完成";
  case TRANSFER_FAILED:
    return "失败";
  case TRANSFER_CANCELED:
    return "已取消";
  default:
    return "未知";
  }
}

// 刷新按钮点击槽函数
void FileTransferDialog::on_pushButton_refresh_clicked() {
  // 更新任务列表
  updateTaskList();
}

// 清理按钮点击槽函数
void FileTransferDialog::on_pushButton_cleanup_clicked() {
  // 清理已完成任务
  m_transferManager->cleanupCompletedTasks();

  // 更新任务列表
  updateTaskList();
}

// 开始按钮点击槽函数
void FileTransferDialog::on_pushButton_start_clicked() {
  // 获取选中的任务ID
  quint32 taskId = getSelectedTaskId();
  if (taskId == 0) {
    return;
  }

  // 开始任务
  m_transferManager->startTask(taskId);
}

// 暂停按钮点击槽函数
void FileTransferDialog::on_pushButton_pause_clicked() {
  // 获取选中的任务ID
  quint32 taskId = getSelectedTaskId();
  if (taskId == 0) {
    return;
  }

  // 暂停任务
  m_transferManager->pauseTask(taskId);
}

// 继续按钮点击槽函数
void FileTransferDialog::on_pushButton_resume_clicked() {
  // 获取选中的任务ID
  quint32 taskId = getSelectedTaskId();
  if (taskId == 0) {
    return;
  }

  // 继续任务
  m_transferManager->resumeTask(taskId);
}

// 取消按钮点击槽函数
void FileTransferDialog::on_pushButton_cancel_clicked() {
  // 获取选中的任务ID
  quint32 taskId = getSelectedTaskId();
  if (taskId == 0) {
    return;
  }

  // 取消任务
  m_transferManager->cancelTask(taskId);
}

// 任务表格点击槽函数
void FileTransferDialog::on_tableWidget_tasks_itemClicked(
    QTableWidgetItem *item) {
  // 获取选中的任务ID
  quint32 taskId = getSelectedTaskId();
  if (taskId == 0) {
    ui->pushButton_start->setEnabled(false);
    ui->pushButton_pause->setEnabled(false);
    ui->pushButton_resume->setEnabled(false);
    ui->pushButton_cancel->setEnabled(false);
    return;
  }

  // 获取任务
  TransferTask task = m_transferManager->getTask(taskId);

  // 根据任务状态设置按钮状态
  ui->pushButton_start->setEnabled(task.status == TRANSFER_PENDING ||
                                   task.status == TRANSFER_PAUSED);
  ui->pushButton_pause->setEnabled(task.status == TRANSFER_TRANSFERING);
  ui->pushButton_resume->setEnabled(task.status == TRANSFER_PAUSED);
  ui->pushButton_cancel->setEnabled(task.status != TRANSFER_COMPLETED &&
                                    task.status != TRANSFER_FAILED &&
                                    task.status != TRANSFER_CANCELED);
}

// 任务状态改变槽函数
void FileTransferDialog::onTaskStatusChanged(quint32 taskId,
                                             TransferStatus status) {
  // 更新任务行
  updateTaskRow(taskId);

  // 如果任务被选中，则更新按钮状态
  quint32 selectedTaskId = getSelectedTaskId();
  if (selectedTaskId == taskId) {
    on_tableWidget_tasks_itemClicked(nullptr);
  }
}

// 任务进度更新槽函数
void FileTransferDialog::onTaskProgressUpdated(quint32 taskId,
                                               quint64 bytesTransferred,
                                               quint64 fileSize) {
  // 更新任务行
  updateTaskRow(taskId);
}

// 任务速度更新槽函数
void FileTransferDialog::onTaskSpeedUpdated(quint32 taskId, quint32 speed,
                                            int remainingTime) {
  // 更新任务行
  updateTaskRow(taskId);
}
