<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FileViewWidget</class>
 <widget class="QWidget" name="FileViewWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>500</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_toolbar">
     <item>
      <widget class="QPushButton" name="pushButton_back">
       <property name="toolTip">
        <string>返回上一级</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/back.png</normaloff>:/icons/back.png</iconset>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="Line" name="line">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_upload">
       <property name="toolTip">
        <string>上传文件</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/upload.png</normaloff>:/icons/upload.png</iconset>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_download">
       <property name="toolTip">
        <string>下载文件</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/download.png</normaloff>:/icons/download.png</iconset>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_new_folder">
       <property name="toolTip">
        <string>新建文件夹</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/new_folder.png</normaloff>:/icons/new_folder.png</iconset>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_delete">
       <property name="toolTip">
        <string>删除</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/delete.png</normaloff>:/icons/delete.png</iconset>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_rename">
       <property name="toolTip">
        <string>重命名</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/rename.png</normaloff>:/icons/rename.png</iconset>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="Line" name="line_2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_refresh">
       <property name="toolTip">
        <string>刷新</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/refresh.png</normaloff>:/icons/refresh.png</iconset>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit_search">
       <property name="placeholderText">
        <string>搜索文件...</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_search">
       <property name="toolTip">
        <string>搜索</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/search.png</normaloff>:/icons/search.png</iconset>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QStackedWidget" name="stackedWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="page_list">
      <layout class="QVBoxLayout" name="verticalLayout_list">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QListView" name="listView">
         <property name="contextMenuPolicy">
          <enum>Qt::CustomContextMenu</enum>
         </property>
         <property name="editTriggers">
          <set>QAbstractItemView::NoEditTriggers</set>
         </property>
         <property name="showDropIndicator" stdset="0">
          <bool>true</bool>
         </property>
         <property name="dragDropMode">
          <enum>QAbstractItemView::DragOnly</enum>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="page_icon">
      <layout class="QVBoxLayout" name="verticalLayout_icon">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QListView" name="iconView">
         <property name="contextMenuPolicy">
          <enum>Qt::CustomContextMenu</enum>
         </property>
         <property name="editTriggers">
          <set>QAbstractItemView::NoEditTriggers</set>
         </property>
         <property name="viewMode">
          <enum>QListView::IconMode</enum>
         </property>
         <property name="uniformItemSizes">
          <bool>true</bool>
         </property>
         <property name="wordWrap">
          <bool>true</bool>
         </property>
         <property name="selectionRectVisible">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_status">
     <item>
      <widget class="QLabel" name="label_path">
       <property name="text">
        <string>路径: /</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_count">
       <property name="text">
        <string>共 0 项</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
