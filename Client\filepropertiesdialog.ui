<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FilePropertiesDialog</class>
 <widget class="QDialog" name="FilePropertiesDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>350</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>文件属性</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icons/properties.png</normaloff>:/icons/properties.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QFormLayout" name="formLayout">
     <property name="horizontalSpacing">
      <number>20</number>
     </property>
     <property name="verticalSpacing">
      <number>15</number>
     </property>
     <item row="0" column="0">
      <widget class="QLabel" name="label_name">
       <property name="text">
        <string>文件名：</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLabel" name="label_name_value">
       <property name="text">
        <string>-</string>
       </property>
       <property name="wordWrap">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="label_type">
       <property name="text">
        <string>类型：</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QLabel" name="label_type_value">
       <property name="text">
        <string>-</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="label_size">
       <property name="text">
        <string>大小：</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QLabel" name="label_size_value">
       <property name="text">
        <string>-</string>
       </property>
      </widget>
     </item>
     <item row="3" column="0">
      <widget class="QLabel" name="label_hash">
       <property name="text">
        <string>哈希值：</string>
       </property>
      </widget>
     </item>
     <item row="3" column="1">
      <widget class="QLabel" name="label_hash_value">
       <property name="text">
        <string>-</string>
       </property>
       <property name="wordWrap">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="4" column="0">
      <widget class="QLabel" name="label_create_time">
       <property name="text">
        <string>创建时间：</string>
       </property>
      </widget>
     </item>
     <item row="4" column="1">
      <widget class="QLabel" name="label_create_time_value">
       <property name="text">
        <string>-</string>
       </property>
      </widget>
     </item>
     <item row="5" column="0">
      <widget class="QLabel" name="label_modify_time">
       <property name="text">
        <string>修改时间：</string>
       </property>
      </widget>
     </item>
     <item row="5" column="1">
      <widget class="QLabel" name="label_modify_time_value">
       <property name="text">
        <string>-</string>
       </property>
      </widget>
     </item>
     <item row="6" column="0">
      <widget class="QLabel" name="label_path">
       <property name="text">
        <string>路径：</string>
       </property>
      </widget>
     </item>
     <item row="6" column="1">
      <widget class="QLabel" name="label_path_value">
       <property name="text">
        <string>-</string>
       </property>
       <property name="wordWrap">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_button">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_ok">
       <property name="text">
        <string>确定</string>
       </property>
       <property name="default">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
