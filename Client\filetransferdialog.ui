<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FileTransferDialog</class>
 <widget class="QDialog" name="FileTransferDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>500</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>文件传输</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icons/transfer.png</normaloff>:/icons/transfer.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTableWidget" name="tableWidget_tasks">
     <property name="selectionMode">
      <enum>QAbstractItemView::SingleSelection</enum>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectRows</enum>
     </property>
     <column>
      <property name="text">
       <string>任务ID</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>文件名</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>类型</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>大小</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>进度</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>速度</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>剩余时间</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>状态</string>
      </property>
     </column>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_buttons">
     <item>
      <widget class="QPushButton" name="pushButton_start">
       <property name="text">
        <string>开始</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/start.png</normaloff>:/icons/start.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_pause">
       <property name="text">
        <string>暂停</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/pause.png</normaloff>:/icons/pause.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_resume">
       <property name="text">
        <string>继续</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/resume.png</normaloff>:/icons/resume.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_cancel">
       <property name="text">
        <string>取消</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/cancel.png</normaloff>:/icons/cancel.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_refresh">
       <property name="text">
        <string>刷新</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/refresh.png</normaloff>:/icons/refresh.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_cleanup">
       <property name="text">
        <string>清理已完成</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/cleanup.png</normaloff>:/icons/cleanup.png</iconset>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
