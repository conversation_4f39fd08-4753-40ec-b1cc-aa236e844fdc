QT       += core gui network sql

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    ../Common/errorhandler.cpp \
    ../Common/exception.cpp \
    ../Common/utils.cpp \
    configmanager.cpp \
    databasemanager.cpp \
    filehandler.cpp \
    logger.cpp \
    main.cpp \
    server.cpp

HEADERS += \
    ../Common/common.h \
    ../Common/errorhandler.h \
    ../Common/exception.h \
    ../Common/utils.h \
    configmanager.h \
    databasemanager.h \
    filehandler.h \
    logger.h \
    server.h

FORMS +=

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

DISTFILES += \
    server.config
