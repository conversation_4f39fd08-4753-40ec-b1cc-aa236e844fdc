#include "configmanager.h"
#include <QCoreApplication>
#include <QDebug>
#include <QDir>

// 初始化静态成员变量
ConfigManager *ConfigManager::m_instance = nullptr;
QMutex ConfigManager::m_mutex;

// 获取单例实例
ConfigManager *ConfigManager::getInstance() {
  if (m_instance == nullptr) {
    QMutexLocker locker(&m_mutex);
    if (m_instance == nullptr) {
      m_instance = new ConfigManager();
    }
  }
  return m_instance;
}

// 私有构造函数
ConfigManager::ConfigManager(QObject *parent)
    : QObject(parent), m_settings(nullptr) {
  // 设置配置文件路径
  QString appDir = QCoreApplication::applicationDirPath();
  m_configPath = QDir(appDir).filePath("client.config");

  // 初始化QSettings对象
  m_settings = new QSettings(m_configPath, QSettings::IniFormat, this);

  // 设置默认配置
  setDefaultConfig();
}

// 私有析构函数
ConfigManager::~ConfigManager() {
  // 保存配置
  saveConfig();

  // 清理资源
  if (m_settings) {
    delete m_settings;
    m_settings = nullptr;
  }
}

// 设置默认配置
void ConfigManager::setDefaultConfig() {
  // 服务器默认配置
  m_config.serverHost = "127.0.0.1";
  m_config.serverPort = 8888;

  // 下载目录默认配置
  QString downloadDir = QDir::homePath() + "/Downloads";
  m_config.downloadDir = downloadDir;

  // 网络设置默认配置
  m_config.heartbeatInterval = 30;  // 心跳间隔30秒
  m_config.reconnectInterval = 5;   // 重连间隔5秒
  m_config.maxRetries = 3;          // 最大重试次数3次
  m_config.chunkSize = 1024 * 1024; // 文件传输块大小1MB
}

// 加载配置
void ConfigManager::loadConfig() {
  // 从配置文件读取配置
  m_settings->beginGroup("Server");
  m_config.serverHost =
      m_settings->value("Host", m_config.serverHost).toString();
  m_config.serverPort = m_settings->value("Port", m_config.serverPort).toUInt();
  m_settings->endGroup();

  m_settings->beginGroup("Download");
  m_config.downloadDir =
      m_settings->value("Directory", m_config.downloadDir).toString();
  m_settings->endGroup();

  m_settings->beginGroup("Network");
  m_config.heartbeatInterval =
      m_settings->value("HeartbeatInterval", m_config.heartbeatInterval)
          .toInt();
  m_config.reconnectInterval =
      m_settings->value("ReconnectInterval", m_config.reconnectInterval)
          .toInt();
  m_config.maxRetries =
      m_settings->value("MaxRetries", m_config.maxRetries).toInt();
  m_config.chunkSize =
      m_settings->value("ChunkSize", m_config.chunkSize).toInt();
  m_settings->endGroup();
}

// 保存配置
void ConfigManager::saveConfig() {
  // 保存配置到文件
  m_settings->beginGroup("Server");
  m_settings->setValue("Host", m_config.serverHost);
  m_settings->setValue("Port", m_config.serverPort);
  m_settings->endGroup();

  m_settings->beginGroup("Download");
  m_settings->setValue("Directory", m_config.downloadDir);
  m_settings->endGroup();

  m_settings->beginGroup("Network");
  m_settings->setValue("HeartbeatInterval", m_config.heartbeatInterval);
  m_settings->setValue("ReconnectInterval", m_config.reconnectInterval);
  m_settings->setValue("MaxRetries", m_config.maxRetries);
  m_settings->setValue("ChunkSize", m_config.chunkSize);
  m_settings->endGroup();

  // 立即写入文件
  m_settings->sync();
}

// 获取客户端配置
ClientConfig ConfigManager::getClientConfig() const { return m_config; }

// 设置客户端配置
void ConfigManager::setClientConfig(const ClientConfig &config) {
  m_config = config;
  saveConfig();
}

// 获取服务器主机地址
QString ConfigManager::getServerHost() const { return m_config.serverHost; }

// 获取服务器端口
quint16 ConfigManager::getServerPort() const { return m_config.serverPort; }

// 获取下载目录
QString ConfigManager::getDownloadDir() const { return m_config.downloadDir; }

// 获取心跳间隔
int ConfigManager::getHeartbeatInterval() const {
  return m_config.heartbeatInterval;
}

// 获取重连间隔
int ConfigManager::getReconnectInterval() const {
  return m_config.reconnectInterval;
}

// 获取最大重试次数
int ConfigManager::getMaxRetries() const { return m_config.maxRetries; }

// 获取文件传输块大小
int ConfigManager::getChunkSize() const { return m_config.chunkSize; }

// 设置服务器主机地址
void ConfigManager::setServerHost(const QString &host) {
  m_config.serverHost = host;
  saveConfig();
}

// 设置服务器端口
void ConfigManager::setServerPort(quint16 port) {
  m_config.serverPort = port;
  saveConfig();
}

// 设置下载目录
void ConfigManager::setDownloadDir(const QString &dir) {
  m_config.downloadDir = dir;
  saveConfig();
}

// 设置心跳间隔
void ConfigManager::setHeartbeatInterval(int interval) {
  m_config.heartbeatInterval = interval;
  saveConfig();
}

// 设置重连间隔
void ConfigManager::setReconnectInterval(int interval) {
  m_config.reconnectInterval = interval;
  saveConfig();
}

// 设置最大重试次数
void ConfigManager::setMaxRetries(int retries) {
  m_config.maxRetries = retries;
  saveConfig();
}

// 设置文件传输块大小
void ConfigManager::setChunkSize(int size) {
  m_config.chunkSize = size;
  saveConfig();
}
