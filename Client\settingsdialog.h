#ifndef SETTINGSDIALOG_H
#define SETTINGSDIALOG_H

#include "configmanager.h"
#include "ui_settingsdialog.h"
#include <QDialog>
#include <QObject>
#include <QSpinBox>
#include <QWidget>
#include <QtCore/qglobal.h>
#include <QtCore/qobjectdefs.h>

namespace Ui {
class SettingsDialog;
}

class SettingsDialog : public QDialog {
  Q_OBJECT

public:
  explicit SettingsDialog(QWidget *parent = nullptr);
  ~SettingsDialog();

private slots:
  // 确定按钮点击槽函数
  void on_buttonBox_accepted();

  // 取消按钮点击槽函数
  void on_buttonBox_rejected();

  // 服务器地址输入框文本改变槽函数
  void on_lineEdit_serverHost_textChanged(const QString &text);

  // 服务器端口输入框文本改变槽函数
  void on_spinBox_serverPort_valueChanged(int value);

  // 记住用户名复选框状态改变槽函数
  void on_checkBox_rememberUsername_stateChanged(int state);

  // 自动登录复选框状态改变槽函数
  void on_checkBox_autoLogin_stateChanged(int state);

private:
  // 初始化UI
  void initUI();

  // 加载配置
  void loadConfig();

  // 保存配置
  void saveConfig();

  // 验证输入
  bool validateInput();

  // 更新UI状态
  void updateUIState();

  Ui::SettingsDialog *ui; // UI对象
  ClientConfig m_config;  // 客户端配置
  bool m_configChanged;   // 配置是否已更改
};

#endif // SETTINGSDIALOG_H
