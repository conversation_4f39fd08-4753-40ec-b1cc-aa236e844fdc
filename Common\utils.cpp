#include "utils.h"
#include <QByteArray>
#include <QDataStream>
#include <QFile>
#include <QFileInfo>
#include <QNetworkInterface>
#include <QRegularExpression>
#include <QTcpSocket>
#include <QTextStream>
#include <QUrl>

// 计算密码哈希
QString Utils::calculatePasswordHash(const QString &password,
                                     const QString &salt) {
  // 组合密码和盐值
  QString passwordWithSalt = password + salt;

  // 计算SHA256哈希
  QByteArray hash = QCryptographicHash::hash(passwordWithSalt.toUtf8(),
                                             QCryptographicHash::Sha256);

  // 转换为十六进制字符串
  return hash.toHex();
}

// 生成随机盐值
QString Utils::generateSalt(int length) {
  // 定义字符集
  const QString possibleCharacters =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

  // 生成随机字符串
  QString randomString;
  for (int i = 0; i < length; ++i) {
    int index =
        QRandomGenerator::global()->bounded(0, possibleCharacters.length());
    QChar nextChar = possibleCharacters.at(index);
    randomString.append(nextChar);
  }

  return randomString;
}

// 生成随机字符串
QString Utils::generateRandomString(int length) {
  // 定义字符集
  const QString possibleCharacters =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

  // 生成随机字符串
  QString randomString;
  for (int i = 0; i < length; ++i) {
    int index =
        QRandomGenerator::global()->bounded(0, possibleCharacters.length());
    QChar nextChar = possibleCharacters.at(index);
    randomString.append(nextChar);
  }

  return randomString;
}

// 计算文件哈希
QString Utils::calculateFileHash(const QString &filePath) {
  QFile file(filePath);
  if (!file.open(QIODevice::ReadOnly)) {
    return QString();
  }

  // 计算MD5哈希
  QCryptographicHash hash(QCryptographicHash::Md5);
  while (!file.atEnd()) {
    hash.addData(file.read(8192));
  }

  file.close();

  // 转换为十六进制字符串
  return hash.result().toHex();
}

// 格式化文件大小
QString Utils::formatFileSize(quint64 size) {
  if (size < 1024) {
    return QString("%1 B").arg(size);
  } else if (size < 1024 * 1024) {
    return QString("%1 KB").arg(size / 1024.0, 0, 'f', 2);
  } else if (size < 1024 * 1024 * 1024) {
    return QString("%1 MB").arg(size / (1024.0 * 1024.0), 0, 'f', 2);
  } else if (size < 1024LL * 1024 * 1024 * 1024) {
    return QString("%1 GB").arg(size / (1024.0 * 1024.0 * 1024.0), 0, 'f', 2);
  } else {
    return QString("%1 TB").arg(size / (1024.0 * 1024.0 * 1024.0 * 1024.0), 0,
                                'f', 2);
  }
}

// 格式化日期时间
QString Utils::formatDateTime(const QDateTime &dateTime) {
  return dateTime.toString("yyyy-MM-dd hh:mm:ss");
}

// 解析日期时间
QDateTime Utils::parseDateTime(const QString &dateTimeStr) {
  return QDateTime::fromString(dateTimeStr, "yyyy-MM-dd hh:mm:ss");
}

// 格式化持续时间
QString Utils::formatDuration(int seconds) {
  if (seconds < 0) {
    return "--:--:--";
  }

  int hours = seconds / 3600;
  int minutes = (seconds % 3600) / 60;
  int secs = seconds % 60;

  return QString("%1:%2:%3")
      .arg(hours, 2, 10, QLatin1Char('0'))
      .arg(minutes, 2, 10, QLatin1Char('0'))
      .arg(secs, 2, 10, QLatin1Char('0'));
}

// 检查邮箱格式
bool Utils::isValidEmail(const QString &email) {
  // 使用正则表达式验证邮箱格式
  QRegularExpression regex("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
  return regex.match(email).hasMatch();
}

// 检查密码强度
bool Utils::isStrongPassword(const QString &password) {
  // 密码长度至少为6位
  if (password.length() < 6) {
    return false;
  }

  // 检查是否包含至少一个数字
  bool hasDigit = false;
  // 检查是否包含至少一个小写字母
  bool hasLower = false;
  // 检查是否包含至少一个大写字母
  bool hasUpper = false;
  // 检查是否包含至少一个特殊字符
  bool hasSpecial = false;

  for (const QChar &c : password) {
    if (c.isDigit()) {
      hasDigit = true;
    } else if (c.isLower()) {
      hasLower = true;
    } else if (c.isUpper()) {
      hasUpper = true;
    } else if (!c.isLetterOrNumber()) {
      hasSpecial = true;
    }
  }

  // 至少满足其中三项
  int strength = 0;
  if (hasDigit)
    strength++;
  if (hasLower)
    strength++;
  if (hasUpper)
    strength++;
  if (hasSpecial)
    strength++;

  return strength >= 2;
}

// 获取应用数据目录
QString Utils::getAppDataDir() {
  // 获取标准路径
  QString dataDir =
      QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);

  // 如果目录不存在，则创建
  if (!QDir(dataDir).exists()) {
    QDir().mkpath(dataDir);
  }

  return dataDir;
}

// 创建目录
bool Utils::createDirectory(const QString &path) {
  QDir dir(path);
  if (dir.exists()) {
    return true;
  }

  return dir.mkpath(path);
}

// 删除文件或目录
bool Utils::deleteFile(const QString &path) {
  QFileInfo fileInfo(path);

  if (fileInfo.isDir()) {
    // 如果是目录，递归删除
    QDir dir(path);
    return dir.removeRecursively();
  } else {
    // 如果是文件，直接删除
    QFile file(path);
    return file.remove();
  }
}

// 复制文件
bool Utils::copyFile(const QString &srcPath, const QString &dstPath) {
  QFile srcFile(srcPath);
  if (!srcFile.open(QIODevice::ReadOnly)) {
    return false;
  }

  QFile dstFile(dstPath);
  if (!dstFile.open(QIODevice::WriteOnly)) {
    srcFile.close();
    return false;
  }

  // 复制文件内容
  QByteArray buffer = srcFile.readAll();
  dstFile.write(buffer);

  srcFile.close();
  dstFile.close();

  return true;
}

// 移动文件
bool Utils::moveFile(const QString &srcPath, const QString &dstPath) {
  // 先复制文件
  if (!copyFile(srcPath, dstPath)) {
    return false;
  }

  // 再删除源文件
  if (!deleteFile(srcPath)) {
    return false;
  }

  return true;
}

// 获取文件扩展名
QString Utils::getFileExtension(const QString &fileName) {
  int dotIndex = fileName.lastIndexOf('.');
  if (dotIndex == -1) {
    return QString();
  }

  return fileName.mid(dotIndex + 1).toLower();
}

// 获取文件类型
QString Utils::getFileType(const QString &fileName) {
  QString extension = getFileExtension(fileName);

  if (extension.isEmpty()) {
    return "未知";
  }

  // 根据扩展名返回文件类型
  if (extension == "txt") {
    return "文本文件";
  } else if (extension == "pdf") {
    return "PDF文档";
  } else if (extension == "doc" || extension == "docx") {
    return "Word文档";
  } else if (extension == "xls" || extension == "xlsx") {
    return "Excel表格";
  } else if (extension == "ppt" || extension == "pptx") {
    return "PowerPoint演示文稿";
  } else if (extension == "jpg" || extension == "jpeg" || extension == "png" ||
             extension == "gif" || extension == "bmp") {
    return "图片";
  } else if (extension == "mp3" || extension == "wav" || extension == "flac") {
    return "音频";
  } else if (extension == "mp4" || extension == "avi" || extension == "mkv") {
    return "视频";
  } else if (extension == "zip" || extension == "rar" || extension == "7z") {
    return "压缩文件";
  } else {
    return extension.toUpper() + "文件";
  }
}

// JSON对象转字符串
QString Utils::jsonToString(const QJsonObject &jsonObj) {
  QJsonDocument jsonDoc(jsonObj);
  return jsonDoc.toJson(QJsonDocument::Compact);
}

// 字符串转JSON对象
QJsonObject Utils::stringToJson(const QString &str) {
  QJsonDocument jsonDoc = QJsonDocument::fromJson(str.toUtf8());
  return jsonDoc.object();
}

// URL编码
QString Utils::urlEncode(const QString &str) {
  return QUrl::toPercentEncoding(str.toUtf8());
}

// URL解码
QString Utils::urlDecode(const QString &str) {
  return QUrl::fromPercentEncoding(str.toUtf8());
}

// Base64编码
QByteArray Utils::base64Encode(const QByteArray &data) {
  return data.toBase64();
}

// Base64解码
QByteArray Utils::base64Decode(const QByteArray &data) {
  return QByteArray::fromBase64(data);
}

// 获取本机IP地址
QString Utils::getLocalIpAddress() {
  // 获取所有网络接口
  QList<QHostAddress> ipAddressesList = QNetworkInterface::allAddresses();

  // 遍历所有IP地址
  for (const QHostAddress &ipAddress : ipAddressesList) {
    // 过滤掉IPv6地址和本地回环地址
    if (ipAddress != QHostAddress::LocalHost && ipAddress.toIPv4Address()) {
      return ipAddress.toString();
    }
  }

  // 如果没有找到合适的IP地址，返回本地回环地址
  return QHostAddress(QHostAddress::LocalHost).toString();
}

// 检查端口是否被占用
bool Utils::isPortInUse(quint16 port) {
  // 创建TCP套接字
  QTcpSocket socket;

  // 尝试连接本地端口
  socket.connectToHost("127.0.0.1", port);

  // 等待连接结果
  bool isConnected = socket.waitForConnected(1000);

  // 关闭套接字
  socket.close();

  // 如果连接成功，说明端口被占用
  return isConnected;
}

// 生成分享码
QString Utils::generateShareCode() {
  // 生成8位随机字符串
  return generateRandomString(8);
}

// 验证分享码
bool Utils::validateShareCode(const QString &shareCode) {
  // 分享码必须是8位字母数字
  QRegularExpression regex("^[a-zA-Z0-9]{8}$");
  return regex.match(shareCode).hasMatch();
}
