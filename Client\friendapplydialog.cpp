#include "friendapplydialog.h"
#include "logger.h"
#include "ui_friendapplydialog.h"
#include <QMessageBox>

// 构造函数
FriendApplyDialog::FriendApplyDialog(QWidget *parent)
    : QDialog(parent), ui(new Ui::FriendApplyDialog), m_userId(0) {
  // 初始化UI
  ui->setupUi(this);

  // 初始化UI
  initUI();
}

// 析构函数
FriendApplyDialog::~FriendApplyDialog() { delete ui; }

// 设置用户ID
void FriendApplyDialog::setUserId(quint32 userId) { m_userId = userId; }

// 获取用户ID
quint32 FriendApplyDialog::userId() const { return m_userId; }

// 添加好友申请
void FriendApplyDialog::addFriendApply(const UserInfo &applicant,
                                       const QString &message) {
  // 创建好友申请信息
  FriendApplyInfo applyInfo;
  applyInfo.applyId = generateApplyId(); // 生成申请ID
  applyInfo.applicantId = applicant.userId;
  applyInfo.applicantName = applicant.username;
  applyInfo.applyMessage = message;
  applyInfo.applyTime =
      QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
  applyInfo.status = APPLY_PENDING;

  // 添加到申请列表
  m_applyList.append(applyInfo);

  // 加载好友申请列表
  loadFriendApplyList(m_applyList);

  // 显示对话框
  show();
}

// 同意按钮点击槽函数
void FriendApplyDialog::on_pushButton_agree_clicked() {
  // 获取选中的申请
  QList<QTableWidgetItem *> selectedItems =
      ui->tableWidget_applies->selectedItems();
  if (selectedItems.isEmpty()) {
    QMessageBox::warning(this, "操作提示", "请选择要处理的申请");
    return;
  }

  // 获取行号
  int row = selectedItems.first()->row();

  // 获取申请ID
  QTableWidgetItem *applyIdItem = ui->tableWidget_applies->item(row, 0);
  if (!applyIdItem) {
    QMessageBox::warning(this, "操作提示", "无法获取申请信息");
    return;
  }

  quint32 applicantId = applyIdItem->data(Qt::UserRole).toUInt();

  // 发送同意添加好友请求信号
  emit agreeAddFriendRequested(applicantId);

  // 从列表中移除该申请
  for (int i = 0; i < m_applyList.size(); ++i) {
    if (m_applyList[i].applicantId == applicantId) {
      m_applyList.removeAt(i);
      break;
    }
  }

  // 刷新列表
  loadFriendApplyList(m_applyList);
}

// 拒绝按钮点击槽函数
void FriendApplyDialog::on_pushButton_reject_clicked() {
  // 获取选中的申请
  QList<QTableWidgetItem *> selectedItems =
      ui->tableWidget_applies->selectedItems();
  if (selectedItems.isEmpty()) {
    QMessageBox::warning(this, "操作提示", "请选择要处理的申请");
    return;
  }

  // 获取行号
  int row = selectedItems.first()->row();

  // 获取申请ID
  QTableWidgetItem *applyIdItem = ui->tableWidget_applies->item(row, 0);
  if (!applyIdItem) {
    QMessageBox::warning(this, "操作提示", "无法获取申请信息");
    return;
  }

  quint32 applicantId = applyIdItem->data(Qt::UserRole).toUInt();

  // 发送拒绝添加好友请求信号
  emit rejectAddFriendRequested(applicantId);

  // 从列表中移除该申请
  for (int i = 0; i < m_applyList.size(); ++i) {
    if (m_applyList[i].applicantId == applicantId) {
      m_applyList.removeAt(i);
      break;
    }
  }

  // 刷新列表
  loadFriendApplyList(m_applyList);
}

// 关闭按钮点击槽函数
void FriendApplyDialog::on_pushButton_close_clicked() {
  // 关闭对话框
  reject();
}

// 初始化UI
void FriendApplyDialog::initUI() {
  // 设置窗口标题
  setWindowTitle("好友申请");

  // 设置窗口图标
  setWindowIcon(QIcon(":/icons/friend_apply.png"));

  // 设置窗口大小
  resize(500, 400);

  // 设置表格属性
  ui->tableWidget_applies->setAlternatingRowColors(true);
  ui->tableWidget_applies->setSelectionMode(QAbstractItemView::SingleSelection);
  ui->tableWidget_applies->setSelectionBehavior(QAbstractItemView::SelectRows);
  ui->tableWidget_applies->setEditTriggers(QAbstractItemView::NoEditTriggers);

  // 设置表头
  ui->tableWidget_applies->horizontalHeader()->setSectionResizeMode(
      0, QHeaderView::Stretch); // 申请人
  ui->tableWidget_applies->horizontalHeader()->setSectionResizeMode(
      1, QHeaderView::ResizeToContents); // 申请时间
  ui->tableWidget_applies->horizontalHeader()->setSectionResizeMode(
      2, QHeaderView::Stretch); // 申请信息
  ui->tableWidget_applies->horizontalHeader()->setSectionResizeMode(
      3, QHeaderView::ResizeToContents); // 操作

  // 连接信号槽
  connect(ui->pushButton_agree, &QPushButton::clicked, this,
          &FriendApplyDialog::on_pushButton_agree_clicked);
  connect(ui->pushButton_reject, &QPushButton::clicked, this,
          &FriendApplyDialog::on_pushButton_reject_clicked);
  connect(ui->pushButton_close, &QPushButton::clicked, this,
          &FriendApplyDialog::on_pushButton_close_clicked);
}

// 加载好友申请列表
void FriendApplyDialog::loadFriendApplyList(
    const QList<FriendApplyInfo> &applyList) {
  // 清空表格
  ui->tableWidget_applies->setRowCount(0);

  // 添加申请到表格
  for (const FriendApplyInfo &applyInfo : applyList) {
    // 添加行
    int row = ui->tableWidget_applies->rowCount();
    ui->tableWidget_applies->insertRow(row);

    // 设置申请人
    QTableWidgetItem *applicantItem =
        new QTableWidgetItem(applyInfo.applicantName);
    applicantItem->setData(Qt::UserRole, applyInfo.applicantId);
    ui->tableWidget_applies->setItem(row, 0, applicantItem);

    // 设置申请时间
    QTableWidgetItem *applyTimeItem = new QTableWidgetItem(applyInfo.applyTime);
    ui->tableWidget_applies->setItem(row, 1, applyTimeItem);

    // 设置申请信息
    QTableWidgetItem *messageItem =
        new QTableWidgetItem(applyInfo.applyMessage);
    ui->tableWidget_applies->setItem(row, 2, messageItem);

    // 设置操作按钮
    QTableWidgetItem *actionItem = new QTableWidgetItem("处理");
    ui->tableWidget_applies->setItem(row, 3, actionItem);
  }

  LOG_INFO("FriendApply",
           QString("加载好友申请列表完成，共 %1 个申请").arg(applyList.size()));
}

// 生成申请ID（临时方案）
quint32 FriendApplyDialog::generateApplyId() {
  // 这是一个临时方案，使用递增ID生成
  // 在实际应用中，应该从数据库的自增字段获取
  static quint32 lastApplyId = 1000; // 起始ID
  return ++lastApplyId;
}
