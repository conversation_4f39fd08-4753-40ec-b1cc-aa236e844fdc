#include "usersettingsdialog.h"
#include "logger.h"
#include "networkmanager.h"
#include "ui_usersettingsdialog.h"
#include "../Common/utils.h"
#include <QInputDialog>
#include <QMessageBox>

// 构造函数
UserSettingsDialog::UserSettingsDialog(QWidget *parent)
    : QDialog(parent), ui(new Ui::UserSettingsDialog) {
  // 初始化UI
  ui->setupUi(this);

  // 初始化网络管理器
  m_networkManager = NetworkManager::getInstance();

  // 连接信号槽
  connect(m_networkManager, &NetworkManager::changePasswordResponse, this,
          &UserSettingsDialog::onChangePasswordResponse);
  connect(m_networkManager, &NetworkManager::updateUserInfoResponse, this,
          &UserSettingsDialog::onUpdateUserInfoResponse);

  // 初始化UI
  initUI();
}

// 析构函数
UserSettingsDialog::~UserSettingsDialog() { delete ui; }

// 设置用户信息
void UserSettingsDialog::setUserInfo(const UserInfo &userInfo) {
  // 保存用户信息
  m_userInfo = userInfo;

  // 加载用户信息
  loadUserInfo();
}

// 获取用户信息
UserInfo UserSettingsDialog::getUserInfo() const { return m_userInfo; }

// 修改密码按钮点击槽函数
void UserSettingsDialog::on_pushButton_change_password_clicked() {
  // 这里应该打开修改密码对话框
  // 由于时间关系，我们暂时用一个简单的输入框代替
  bool ok;
  QString oldPassword = QInputDialog::getText(
      this, "修改密码", "请输入旧密码:", QLineEdit::Password, "", &ok);
  if (!ok || oldPassword.isEmpty()) {
    return;
  }

  QString newPassword = QInputDialog::getText(
      this, "修改密码", "请输入新密码:", QLineEdit::Password, "", &ok);
  if (!ok || newPassword.isEmpty()) {
    return;
  }

  QString confirmPassword = QInputDialog::getText(
      this, "修改密码", "请确认新密码:", QLineEdit::Password, "", &ok);
  if (!ok || confirmPassword.isEmpty()) {
    return;
  }

  // 检查新密码是否一致
  if (newPassword != confirmPassword) {
    QMessageBox::warning(this, "修改密码", "两次输入的新密码不一致");
    return;
  }

  // 检查密码强度
  if (!Utils::isStrongPassword(newPassword)) {
    QMessageBox::warning(this, "修改密码",
                         "密码强度不够，请使用至少6位，包含字母和数字的密码");
    return;
  }

  // 发送修改密码请求到服务器
  m_networkManager->changePasswordRequest(oldPassword, newPassword);

  // 禁用按钮防止重复点击
  ui->pushButton_change_password->setEnabled(false);
  ui->pushButton_change_password->setText("修改中...");
}

// 保存按钮点击槽函数
void UserSettingsDialog::on_pushButton_save_clicked() {
  // 验证输入
  if (!validateInput()) {
    return;
  }

  // 保存用户信息
  saveUserInfo();

  // 关闭对话框
  accept();
}

// 取消按钮点击槽函数
void UserSettingsDialog::on_pushButton_cancel_clicked() {
  // 关闭对话框
  reject();
}

// 初始化UI
void UserSettingsDialog::initUI() {
  // 设置窗口标题
  setWindowTitle("用户设置");

  // 设置窗口图标
  setWindowIcon(QIcon(":/icons/settings.png"));

  // 设置窗口大小
  resize(400, 300);

  // 连接信号槽
  connect(ui->pushButton_save, &QPushButton::clicked, this,
          &UserSettingsDialog::on_pushButton_save_clicked);
  connect(ui->pushButton_cancel, &QPushButton::clicked, this,
          &UserSettingsDialog::on_pushButton_cancel_clicked);
  connect(ui->pushButton_change_password, &QPushButton::clicked, this,
          &UserSettingsDialog::on_pushButton_change_password_clicked);
}

// 加载用户信息
void UserSettingsDialog::loadUserInfo() {
  // 设置用户名
  ui->lineEdit_username->setText(m_userInfo.username);

  // 设置邮箱
  ui->lineEdit_email->setText(m_userInfo.email);

  // 设置存储空间信息
  QString storageInfo =
      QString("%1 / %2")
          .arg(Utils::formatFileSize(m_userInfo.storageUsed))
          .arg(Utils::formatFileSize(m_userInfo.storageTotal));
  ui->label_storage_info->setText(storageInfo);

  // 设置注册时间
  ui->label_register_time_info->setText(m_userInfo.registerTime);

  // 设置最后登录时间
  ui->label_last_login_time_info->setText(m_userInfo.lastLoginTime);
}

// 保存用户信息
void UserSettingsDialog::saveUserInfo() {
  // 发送更新用户信息请求到服务器
  m_networkManager->updateUserInfoRequest(m_userInfo);

  LOG_INFO("UserSettings", "发送保存用户信息请求");
}

// 验证输入
bool UserSettingsDialog::validateInput() {
  // 这里可以添加一些验证逻辑
  // 由于当前界面中只有只读字段，所以暂时不需要验证
  return true;
}

// 修改密码响应槽函数
void UserSettingsDialog::onChangePasswordResponse(bool success,
                                                  const QString &message) {
  // 恢复按钮状态
  ui->pushButton_change_password->setEnabled(true);
  ui->pushButton_change_password->setText("修改密码");

  if (success) {
    QMessageBox::information(this, "修改密码", "密码修改成功");
    LOG_INFO("UserSettings", "密码修改成功");
  } else {
    QMessageBox::warning(this, "修改密码",
                         QString("密码修改失败: %1").arg(message));
    LOG_ERROR("UserSettings", QString("密码修改失败: %1").arg(message));
  }
}

// 更新用户信息响应槽函数
void UserSettingsDialog::onUpdateUserInfoResponse(bool success,
                                                  const QString &message) {
  if (success) {
    QMessageBox::information(this, "保存设置", "用户信息保存成功");
    LOG_INFO("UserSettings", "用户信息保存成功");

    // 重新加载用户信息显示
    loadUserInfo();
  } else {
    QMessageBox::warning(this, "保存设置",
                         QString("用户信息保存失败: %1").arg(message));
    LOG_ERROR("UserSettings", QString("用户信息保存失败: %1").arg(message));
  }
}
