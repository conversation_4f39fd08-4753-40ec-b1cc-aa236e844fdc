/********************************************************************************
** Form generated from reading UI file 'changepassworddialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CHANGEPASSWORDDIALOG_H
#define UI_CHANGEPASSWORDDIALOG_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_ChangePasswordDialog
{
public:
    QVBoxLayout *verticalLayout;
    QFormLayout *formLayout;
    QLabel *label_old_password;
    QLineEdit *lineEdit_old_password;
    QLabel *label_new_password;
    QLineEdit *lineEdit_new_password;
    QLabel *label_confirm_password;
    QLineEdit *lineEdit_confirm_password;
    QLabel *label_password_hint;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *horizontalLayout_buttons;
    QSpacerItem *horizontalSpacer;
    QPushButton *pushButton_ok;
    QPushButton *pushButton_cancel;

    void setupUi(QDialog *ChangePasswordDialog)
    {
        if (ChangePasswordDialog->objectName().isEmpty())
            ChangePasswordDialog->setObjectName("ChangePasswordDialog");
        ChangePasswordDialog->resize(350, 250);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/password.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        ChangePasswordDialog->setWindowIcon(icon);
        verticalLayout = new QVBoxLayout(ChangePasswordDialog);
        verticalLayout->setObjectName("verticalLayout");
        formLayout = new QFormLayout();
        formLayout->setObjectName("formLayout");
        formLayout->setHorizontalSpacing(20);
        formLayout->setVerticalSpacing(15);
        label_old_password = new QLabel(ChangePasswordDialog);
        label_old_password->setObjectName("label_old_password");

        formLayout->setWidget(0, QFormLayout::ItemRole::LabelRole, label_old_password);

        lineEdit_old_password = new QLineEdit(ChangePasswordDialog);
        lineEdit_old_password->setObjectName("lineEdit_old_password");
        lineEdit_old_password->setEchoMode(QLineEdit::Password);

        formLayout->setWidget(0, QFormLayout::ItemRole::FieldRole, lineEdit_old_password);

        label_new_password = new QLabel(ChangePasswordDialog);
        label_new_password->setObjectName("label_new_password");

        formLayout->setWidget(1, QFormLayout::ItemRole::LabelRole, label_new_password);

        lineEdit_new_password = new QLineEdit(ChangePasswordDialog);
        lineEdit_new_password->setObjectName("lineEdit_new_password");
        lineEdit_new_password->setEchoMode(QLineEdit::Password);

        formLayout->setWidget(1, QFormLayout::ItemRole::FieldRole, lineEdit_new_password);

        label_confirm_password = new QLabel(ChangePasswordDialog);
        label_confirm_password->setObjectName("label_confirm_password");

        formLayout->setWidget(2, QFormLayout::ItemRole::LabelRole, label_confirm_password);

        lineEdit_confirm_password = new QLineEdit(ChangePasswordDialog);
        lineEdit_confirm_password->setObjectName("lineEdit_confirm_password");
        lineEdit_confirm_password->setEchoMode(QLineEdit::Password);

        formLayout->setWidget(2, QFormLayout::ItemRole::FieldRole, lineEdit_confirm_password);

        label_password_hint = new QLabel(ChangePasswordDialog);
        label_password_hint->setObjectName("label_password_hint");
        label_password_hint->setWordWrap(true);

        formLayout->setWidget(3, QFormLayout::ItemRole::SpanningRole, label_password_hint);


        verticalLayout->addLayout(formLayout);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        horizontalLayout_buttons = new QHBoxLayout();
        horizontalLayout_buttons->setObjectName("horizontalLayout_buttons");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer);

        pushButton_ok = new QPushButton(ChangePasswordDialog);
        pushButton_ok->setObjectName("pushButton_ok");

        horizontalLayout_buttons->addWidget(pushButton_ok);

        pushButton_cancel = new QPushButton(ChangePasswordDialog);
        pushButton_cancel->setObjectName("pushButton_cancel");

        horizontalLayout_buttons->addWidget(pushButton_cancel);


        verticalLayout->addLayout(horizontalLayout_buttons);


        retranslateUi(ChangePasswordDialog);

        pushButton_ok->setDefault(true);


        QMetaObject::connectSlotsByName(ChangePasswordDialog);
    } // setupUi

    void retranslateUi(QDialog *ChangePasswordDialog)
    {
        ChangePasswordDialog->setWindowTitle(QCoreApplication::translate("ChangePasswordDialog", "\344\277\256\346\224\271\345\257\206\347\240\201", nullptr));
        label_old_password->setText(QCoreApplication::translate("ChangePasswordDialog", "\346\227\247\345\257\206\347\240\201\357\274\232", nullptr));
        lineEdit_old_password->setPlaceholderText(QCoreApplication::translate("ChangePasswordDialog", "\350\257\267\350\276\223\345\205\245\346\227\247\345\257\206\347\240\201", nullptr));
        label_new_password->setText(QCoreApplication::translate("ChangePasswordDialog", "\346\226\260\345\257\206\347\240\201\357\274\232", nullptr));
        lineEdit_new_password->setPlaceholderText(QCoreApplication::translate("ChangePasswordDialog", "\350\257\267\350\276\223\345\205\245\346\226\260\345\257\206\347\240\201", nullptr));
        label_confirm_password->setText(QCoreApplication::translate("ChangePasswordDialog", "\347\241\256\350\256\244\346\226\260\345\257\206\347\240\201\357\274\232", nullptr));
        lineEdit_confirm_password->setPlaceholderText(QCoreApplication::translate("ChangePasswordDialog", "\350\257\267\345\206\215\346\254\241\350\276\223\345\205\245\346\226\260\345\257\206\347\240\201", nullptr));
        label_password_hint->setText(QCoreApplication::translate("ChangePasswordDialog", "\345\257\206\347\240\201\350\207\263\345\260\2216\344\275\215\357\274\214\345\273\272\350\256\256\345\214\205\345\220\253\345\255\227\346\257\215\343\200\201\346\225\260\345\255\227\345\222\214\347\211\271\346\256\212\345\255\227\347\254\246", nullptr));
        label_password_hint->setStyleSheet(QCoreApplication::translate("ChangePasswordDialog", "color: gray;", nullptr));
        pushButton_ok->setText(QCoreApplication::translate("ChangePasswordDialog", "\347\241\256\345\256\232", nullptr));
        pushButton_cancel->setText(QCoreApplication::translate("ChangePasswordDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ChangePasswordDialog: public Ui_ChangePasswordDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CHANGEPASSWORDDIALOG_H
