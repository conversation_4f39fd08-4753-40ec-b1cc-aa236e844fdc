/********************************************************************************
** Form generated from reading UI file 'filesharedialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FILESHAREDIALOG_H
#define UI_FILESHAREDIALOG_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_FileShareDialog
{
public:
    QVBoxLayout *verticalLayout;
    QFormLayout *formLayout;
    QLabel *label_file_name;
    QLabel *label_file_name_value;
    QLabel *label_file_size;
    QLabel *label_file_size_value;
    QLabel *label_share_code;
    QLineEdit *lineEdit_share_code;
    QLabel *label_expire_time;
    QComboBox *comboBox_expire_time;
    QLabel *label_share_hint;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *horizontalLayout_buttons;
    QSpacerItem *horizontalSpacer;
    QPushButton *pushButton_share;
    QPushButton *pushButton_copy;
    QPushButton *pushButton_close;

    void setupUi(QDialog *FileShareDialog)
    {
        if (FileShareDialog->objectName().isEmpty())
            FileShareDialog->setObjectName("FileShareDialog");
        FileShareDialog->resize(500, 300);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/share.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        FileShareDialog->setWindowIcon(icon);
        verticalLayout = new QVBoxLayout(FileShareDialog);
        verticalLayout->setObjectName("verticalLayout");
        formLayout = new QFormLayout();
        formLayout->setObjectName("formLayout");
        formLayout->setHorizontalSpacing(20);
        formLayout->setVerticalSpacing(15);
        label_file_name = new QLabel(FileShareDialog);
        label_file_name->setObjectName("label_file_name");

        formLayout->setWidget(0, QFormLayout::ItemRole::LabelRole, label_file_name);

        label_file_name_value = new QLabel(FileShareDialog);
        label_file_name_value->setObjectName("label_file_name_value");
        label_file_name_value->setWordWrap(true);

        formLayout->setWidget(0, QFormLayout::ItemRole::FieldRole, label_file_name_value);

        label_file_size = new QLabel(FileShareDialog);
        label_file_size->setObjectName("label_file_size");

        formLayout->setWidget(1, QFormLayout::ItemRole::LabelRole, label_file_size);

        label_file_size_value = new QLabel(FileShareDialog);
        label_file_size_value->setObjectName("label_file_size_value");

        formLayout->setWidget(1, QFormLayout::ItemRole::FieldRole, label_file_size_value);

        label_share_code = new QLabel(FileShareDialog);
        label_share_code->setObjectName("label_share_code");

        formLayout->setWidget(2, QFormLayout::ItemRole::LabelRole, label_share_code);

        lineEdit_share_code = new QLineEdit(FileShareDialog);
        lineEdit_share_code->setObjectName("lineEdit_share_code");
        lineEdit_share_code->setReadOnly(true);

        formLayout->setWidget(2, QFormLayout::ItemRole::FieldRole, lineEdit_share_code);

        label_expire_time = new QLabel(FileShareDialog);
        label_expire_time->setObjectName("label_expire_time");

        formLayout->setWidget(3, QFormLayout::ItemRole::LabelRole, label_expire_time);

        comboBox_expire_time = new QComboBox(FileShareDialog);
        comboBox_expire_time->addItem(QString());
        comboBox_expire_time->addItem(QString());
        comboBox_expire_time->addItem(QString());
        comboBox_expire_time->addItem(QString());
        comboBox_expire_time->addItem(QString());
        comboBox_expire_time->setObjectName("comboBox_expire_time");

        formLayout->setWidget(3, QFormLayout::ItemRole::FieldRole, comboBox_expire_time);

        label_share_hint = new QLabel(FileShareDialog);
        label_share_hint->setObjectName("label_share_hint");
        label_share_hint->setWordWrap(true);

        formLayout->setWidget(4, QFormLayout::ItemRole::SpanningRole, label_share_hint);


        verticalLayout->addLayout(formLayout);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        horizontalLayout_buttons = new QHBoxLayout();
        horizontalLayout_buttons->setObjectName("horizontalLayout_buttons");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer);

        pushButton_share = new QPushButton(FileShareDialog);
        pushButton_share->setObjectName("pushButton_share");
        pushButton_share->setIcon(icon);

        horizontalLayout_buttons->addWidget(pushButton_share);

        pushButton_copy = new QPushButton(FileShareDialog);
        pushButton_copy->setObjectName("pushButton_copy");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/copy.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_copy->setIcon(icon1);

        horizontalLayout_buttons->addWidget(pushButton_copy);

        pushButton_close = new QPushButton(FileShareDialog);
        pushButton_close->setObjectName("pushButton_close");
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/close.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_close->setIcon(icon2);

        horizontalLayout_buttons->addWidget(pushButton_close);


        verticalLayout->addLayout(horizontalLayout_buttons);


        retranslateUi(FileShareDialog);

        QMetaObject::connectSlotsByName(FileShareDialog);
    } // setupUi

    void retranslateUi(QDialog *FileShareDialog)
    {
        FileShareDialog->setWindowTitle(QCoreApplication::translate("FileShareDialog", "\346\226\207\344\273\266\345\210\206\344\272\253", nullptr));
        label_file_name->setText(QCoreApplication::translate("FileShareDialog", "\346\226\207\344\273\266\345\220\215\357\274\232", nullptr));
        label_file_name_value->setText(QCoreApplication::translate("FileShareDialog", "-", nullptr));
        label_file_size->setText(QCoreApplication::translate("FileShareDialog", "\346\226\207\344\273\266\345\244\247\345\260\217\357\274\232", nullptr));
        label_file_size_value->setText(QCoreApplication::translate("FileShareDialog", "-", nullptr));
        label_share_code->setText(QCoreApplication::translate("FileShareDialog", "\345\210\206\344\272\253\347\240\201\357\274\232", nullptr));
        label_expire_time->setText(QCoreApplication::translate("FileShareDialog", "\346\234\211\346\225\210\346\234\237\357\274\232", nullptr));
        comboBox_expire_time->setItemText(0, QCoreApplication::translate("FileShareDialog", "\346\260\270\344\271\205\346\234\211\346\225\210", nullptr));
        comboBox_expire_time->setItemText(1, QCoreApplication::translate("FileShareDialog", "1\345\244\251", nullptr));
        comboBox_expire_time->setItemText(2, QCoreApplication::translate("FileShareDialog", "3\345\244\251", nullptr));
        comboBox_expire_time->setItemText(3, QCoreApplication::translate("FileShareDialog", "7\345\244\251", nullptr));
        comboBox_expire_time->setItemText(4, QCoreApplication::translate("FileShareDialog", "30\345\244\251", nullptr));

        label_share_hint->setText(QCoreApplication::translate("FileShareDialog", "\345\210\206\344\272\253\347\240\201\346\234\211\346\225\210\346\234\237\357\274\232\346\260\270\344\271\205\346\234\211\346\225\210\346\210\226\346\214\207\345\256\232\345\244\251\346\225\260\345\220\216\345\244\261\346\225\210", nullptr));
        pushButton_share->setText(QCoreApplication::translate("FileShareDialog", "\345\210\206\344\272\253", nullptr));
        pushButton_copy->setText(QCoreApplication::translate("FileShareDialog", "\345\244\215\345\210\266", nullptr));
        pushButton_close->setText(QCoreApplication::translate("FileShareDialog", "\345\205\263\351\227\255", nullptr));
    } // retranslateUi

};

namespace Ui {
    class FileShareDialog: public Ui_FileShareDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FILESHAREDIALOG_H
