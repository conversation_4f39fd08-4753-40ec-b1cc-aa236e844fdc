/********************************************************************************
** Form generated from reading UI file 'filesearchdialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FILESEARCHDIALOG_H
#define UI_FILESEARCHDIALOG_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_FileSearchDialog
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout_search;
    QLineEdit *lineEdit_keyword;
    QPushButton *pushButton_search;
    QListWidget *listWidget_results;
    QHBoxLayout *horizontalLayout_buttons;
    QSpacerItem *horizontalSpacer;
    QPushButton *pushButton_close;

    void setupUi(QDialog *FileSearchDialog)
    {
        if (FileSearchDialog->objectName().isEmpty())
            FileSearchDialog->setObjectName("FileSearchDialog");
        FileSearchDialog->resize(600, 400);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/search.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        FileSearchDialog->setWindowIcon(icon);
        verticalLayout = new QVBoxLayout(FileSearchDialog);
        verticalLayout->setObjectName("verticalLayout");
        horizontalLayout_search = new QHBoxLayout();
        horizontalLayout_search->setObjectName("horizontalLayout_search");
        lineEdit_keyword = new QLineEdit(FileSearchDialog);
        lineEdit_keyword->setObjectName("lineEdit_keyword");

        horizontalLayout_search->addWidget(lineEdit_keyword);

        pushButton_search = new QPushButton(FileSearchDialog);
        pushButton_search->setObjectName("pushButton_search");
        pushButton_search->setIcon(icon);

        horizontalLayout_search->addWidget(pushButton_search);


        verticalLayout->addLayout(horizontalLayout_search);

        listWidget_results = new QListWidget(FileSearchDialog);
        listWidget_results->setObjectName("listWidget_results");
        listWidget_results->setSelectionMode(QAbstractItemView::SingleSelection);
        listWidget_results->setSelectionBehavior(QAbstractItemView::SelectRows);

        verticalLayout->addWidget(listWidget_results);

        horizontalLayout_buttons = new QHBoxLayout();
        horizontalLayout_buttons->setObjectName("horizontalLayout_buttons");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer);

        pushButton_close = new QPushButton(FileSearchDialog);
        pushButton_close->setObjectName("pushButton_close");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/close.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_close->setIcon(icon1);

        horizontalLayout_buttons->addWidget(pushButton_close);


        verticalLayout->addLayout(horizontalLayout_buttons);


        retranslateUi(FileSearchDialog);

        QMetaObject::connectSlotsByName(FileSearchDialog);
    } // setupUi

    void retranslateUi(QDialog *FileSearchDialog)
    {
        FileSearchDialog->setWindowTitle(QCoreApplication::translate("FileSearchDialog", "\346\226\207\344\273\266\346\220\234\347\264\242", nullptr));
        lineEdit_keyword->setPlaceholderText(QCoreApplication::translate("FileSearchDialog", "\350\276\223\345\205\245\346\226\207\344\273\266\345\220\215\350\277\233\350\241\214\346\220\234\347\264\242", nullptr));
        pushButton_search->setText(QCoreApplication::translate("FileSearchDialog", "\346\220\234\347\264\242", nullptr));
        pushButton_close->setText(QCoreApplication::translate("FileSearchDialog", "\345\205\263\351\227\255", nullptr));
    } // retranslateUi

};

namespace Ui {
    class FileSearchDialog: public Ui_FileSearchDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FILESEARCHDIALOG_H
