/********************************************************************************
** Form generated from reading UI file 'serversettingsdialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SERVERSETTINGSDIALOG_H
#define UI_SERVERSETTINGSDIALOG_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QAbstractButton>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDialogButtonBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_ServerSettingsDialog
{
public:
    QVBoxLayout *verticalLayout;
    QFormLayout *formLayout;
    QLabel *label_host;
    QLineEdit *lineEdit_host;
    QLabel *label_port;
    QSpinBox *spinBox_port;
    QSpacerItem *verticalSpacer;
    QDialogButtonBox *buttonBox;

    void setupUi(QDialog *ServerSettingsDialog)
    {
        if (ServerSettingsDialog->objectName().isEmpty())
            ServerSettingsDialog->setObjectName("ServerSettingsDialog");
        ServerSettingsDialog->resize(400, 200);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/settings.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        ServerSettingsDialog->setWindowIcon(icon);
        verticalLayout = new QVBoxLayout(ServerSettingsDialog);
        verticalLayout->setObjectName("verticalLayout");
        formLayout = new QFormLayout();
        formLayout->setObjectName("formLayout");
        formLayout->setHorizontalSpacing(20);
        formLayout->setVerticalSpacing(15);
        label_host = new QLabel(ServerSettingsDialog);
        label_host->setObjectName("label_host");

        formLayout->setWidget(0, QFormLayout::ItemRole::LabelRole, label_host);

        lineEdit_host = new QLineEdit(ServerSettingsDialog);
        lineEdit_host->setObjectName("lineEdit_host");

        formLayout->setWidget(0, QFormLayout::ItemRole::FieldRole, lineEdit_host);

        label_port = new QLabel(ServerSettingsDialog);
        label_port->setObjectName("label_port");

        formLayout->setWidget(1, QFormLayout::ItemRole::LabelRole, label_port);

        spinBox_port = new QSpinBox(ServerSettingsDialog);
        spinBox_port->setObjectName("spinBox_port");
        spinBox_port->setMinimum(1024);
        spinBox_port->setMaximum(65535);
        spinBox_port->setValue(8888);

        formLayout->setWidget(1, QFormLayout::ItemRole::FieldRole, spinBox_port);


        verticalLayout->addLayout(formLayout);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        buttonBox = new QDialogButtonBox(ServerSettingsDialog);
        buttonBox->setObjectName("buttonBox");
        buttonBox->setOrientation(Qt::Horizontal);
        buttonBox->setStandardButtons(QDialogButtonBox::Cancel|QDialogButtonBox::Ok);

        verticalLayout->addWidget(buttonBox);


        retranslateUi(ServerSettingsDialog);
        QObject::connect(buttonBox, &QDialogButtonBox::accepted, ServerSettingsDialog, qOverload<>(&QDialog::accept));
        QObject::connect(buttonBox, &QDialogButtonBox::rejected, ServerSettingsDialog, qOverload<>(&QDialog::reject));

        QMetaObject::connectSlotsByName(ServerSettingsDialog);
    } // setupUi

    void retranslateUi(QDialog *ServerSettingsDialog)
    {
        ServerSettingsDialog->setWindowTitle(QCoreApplication::translate("ServerSettingsDialog", "\346\234\215\345\212\241\345\231\250\350\256\276\347\275\256", nullptr));
        label_host->setText(QCoreApplication::translate("ServerSettingsDialog", "\346\234\215\345\212\241\345\231\250\345\234\260\345\235\200\357\274\232", nullptr));
        lineEdit_host->setPlaceholderText(QCoreApplication::translate("ServerSettingsDialog", "\350\257\267\350\276\223\345\205\245\346\234\215\345\212\241\345\231\250IP\345\234\260\345\235\200\346\210\226\345\237\237\345\220\215", nullptr));
        label_port->setText(QCoreApplication::translate("ServerSettingsDialog", "\346\234\215\345\212\241\345\231\250\347\253\257\345\217\243\357\274\232", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ServerSettingsDialog: public Ui_ServerSettingsDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SERVERSETTINGSDIALOG_H
