#ifndef FILESHAREDIALOG_H
#define FILESHAREDIALOG_H

#include <QDialog>
#include "../Common/common.h"

namespace Ui {
class FileShareDialog;
}

class FileShareDialog : public QDialog
{
    Q_OBJECT

public:
    explicit FileShareDialog(QWidget *parent = nullptr);
    ~FileShareDialog();

    // 设置文件信息
    void setFileInfo(const FileInfo& fileInfo);

    // 获取文件信息
    FileInfo getFileInfo() const;

signals:
    // 分享文件请求信号
    void shareFileRequested(quint32 fileId, const QString& expireTime);

private slots:
    // 分享按钮点击槽函数
    void on_pushButton_share_clicked();

    // 复制按钮点击槽函数
    void on_pushButton_copy_clicked();

    // 关闭按钮点击槽函数
    void on_pushButton_close_clicked();

private:
    // 初始化UI
    void initUI();

    // 生成分享码
    QString generateShareCode() const;

    // 验证分享码
    bool validateShareCode(const QString& shareCode) const;

    Ui::FileShareDialog *ui;      // UI对象
    FileInfo m_fileInfo;         // 文件信息
};

#endif // FILESHAREDIALOG_H
