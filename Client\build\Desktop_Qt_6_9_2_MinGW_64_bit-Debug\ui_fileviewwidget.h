/********************************************************************************
** Form generated from reading UI file 'fileviewwidget.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FILEVIEWWIDGET_H
#define UI_FILEVIEWWIDGET_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QListView>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_FileViewWidget
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout_toolbar;
    QPushButton *pushButton_back;
    QFrame *line;
    QPushButton *pushButton_upload;
    QPushButton *pushButton_download;
    QPushButton *pushButton_new_folder;
    QPushButton *pushButton_delete;
    QPushButton *pushButton_rename;
    QFrame *line_2;
    QPushButton *pushButton_refresh;
    QSpacerItem *horizontalSpacer;
    QLineEdit *lineEdit_search;
    QPushButton *pushButton_search;
    QStackedWidget *stackedWidget;
    QWidget *page_list;
    QVBoxLayout *verticalLayout_list;
    QListView *listView;
    QWidget *page_icon;
    QVBoxLayout *verticalLayout_icon;
    QListView *iconView;
    QHBoxLayout *horizontalLayout_status;
    QLabel *label_path;
    QSpacerItem *horizontalSpacer_2;
    QLabel *label_count;

    void setupUi(QWidget *FileViewWidget)
    {
        if (FileViewWidget->objectName().isEmpty())
            FileViewWidget->setObjectName("FileViewWidget");
        FileViewWidget->resize(600, 500);
        verticalLayout = new QVBoxLayout(FileViewWidget);
        verticalLayout->setObjectName("verticalLayout");
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        horizontalLayout_toolbar = new QHBoxLayout();
        horizontalLayout_toolbar->setObjectName("horizontalLayout_toolbar");
        pushButton_back = new QPushButton(FileViewWidget);
        pushButton_back->setObjectName("pushButton_back");
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/back.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_back->setIcon(icon);
        pushButton_back->setFlat(true);

        horizontalLayout_toolbar->addWidget(pushButton_back);

        line = new QFrame(FileViewWidget);
        line->setObjectName("line");
        line->setFrameShape(QFrame::Shape::VLine);
        line->setFrameShadow(QFrame::Shadow::Sunken);

        horizontalLayout_toolbar->addWidget(line);

        pushButton_upload = new QPushButton(FileViewWidget);
        pushButton_upload->setObjectName("pushButton_upload");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/upload.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_upload->setIcon(icon1);
        pushButton_upload->setFlat(true);

        horizontalLayout_toolbar->addWidget(pushButton_upload);

        pushButton_download = new QPushButton(FileViewWidget);
        pushButton_download->setObjectName("pushButton_download");
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/download.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_download->setIcon(icon2);
        pushButton_download->setFlat(true);

        horizontalLayout_toolbar->addWidget(pushButton_download);

        pushButton_new_folder = new QPushButton(FileViewWidget);
        pushButton_new_folder->setObjectName("pushButton_new_folder");
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/icons/new_folder.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_new_folder->setIcon(icon3);
        pushButton_new_folder->setFlat(true);

        horizontalLayout_toolbar->addWidget(pushButton_new_folder);

        pushButton_delete = new QPushButton(FileViewWidget);
        pushButton_delete->setObjectName("pushButton_delete");
        QIcon icon4;
        icon4.addFile(QString::fromUtf8(":/icons/delete.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_delete->setIcon(icon4);
        pushButton_delete->setFlat(true);

        horizontalLayout_toolbar->addWidget(pushButton_delete);

        pushButton_rename = new QPushButton(FileViewWidget);
        pushButton_rename->setObjectName("pushButton_rename");
        QIcon icon5;
        icon5.addFile(QString::fromUtf8(":/icons/rename.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_rename->setIcon(icon5);
        pushButton_rename->setFlat(true);

        horizontalLayout_toolbar->addWidget(pushButton_rename);

        line_2 = new QFrame(FileViewWidget);
        line_2->setObjectName("line_2");
        line_2->setFrameShape(QFrame::Shape::VLine);
        line_2->setFrameShadow(QFrame::Shadow::Sunken);

        horizontalLayout_toolbar->addWidget(line_2);

        pushButton_refresh = new QPushButton(FileViewWidget);
        pushButton_refresh->setObjectName("pushButton_refresh");
        QIcon icon6;
        icon6.addFile(QString::fromUtf8(":/icons/refresh.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_refresh->setIcon(icon6);
        pushButton_refresh->setFlat(true);

        horizontalLayout_toolbar->addWidget(pushButton_refresh);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_toolbar->addItem(horizontalSpacer);

        lineEdit_search = new QLineEdit(FileViewWidget);
        lineEdit_search->setObjectName("lineEdit_search");

        horizontalLayout_toolbar->addWidget(lineEdit_search);

        pushButton_search = new QPushButton(FileViewWidget);
        pushButton_search->setObjectName("pushButton_search");
        QIcon icon7;
        icon7.addFile(QString::fromUtf8(":/icons/search.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_search->setIcon(icon7);
        pushButton_search->setFlat(true);

        horizontalLayout_toolbar->addWidget(pushButton_search);


        verticalLayout->addLayout(horizontalLayout_toolbar);

        stackedWidget = new QStackedWidget(FileViewWidget);
        stackedWidget->setObjectName("stackedWidget");
        page_list = new QWidget();
        page_list->setObjectName("page_list");
        verticalLayout_list = new QVBoxLayout(page_list);
        verticalLayout_list->setObjectName("verticalLayout_list");
        verticalLayout_list->setContentsMargins(0, 0, 0, 0);
        listView = new QListView(page_list);
        listView->setObjectName("listView");
        listView->setContextMenuPolicy(Qt::CustomContextMenu);
        listView->setEditTriggers(QAbstractItemView::NoEditTriggers);
        listView->setProperty("showDropIndicator", QVariant(true));
        listView->setDragDropMode(QAbstractItemView::DragOnly);

        verticalLayout_list->addWidget(listView);

        stackedWidget->addWidget(page_list);
        page_icon = new QWidget();
        page_icon->setObjectName("page_icon");
        verticalLayout_icon = new QVBoxLayout(page_icon);
        verticalLayout_icon->setObjectName("verticalLayout_icon");
        verticalLayout_icon->setContentsMargins(0, 0, 0, 0);
        iconView = new QListView(page_icon);
        iconView->setObjectName("iconView");
        iconView->setContextMenuPolicy(Qt::CustomContextMenu);
        iconView->setEditTriggers(QAbstractItemView::NoEditTriggers);
        iconView->setViewMode(QListView::IconMode);
        iconView->setUniformItemSizes(true);
        iconView->setWordWrap(true);
        iconView->setSelectionRectVisible(true);

        verticalLayout_icon->addWidget(iconView);

        stackedWidget->addWidget(page_icon);

        verticalLayout->addWidget(stackedWidget);

        horizontalLayout_status = new QHBoxLayout();
        horizontalLayout_status->setObjectName("horizontalLayout_status");
        label_path = new QLabel(FileViewWidget);
        label_path->setObjectName("label_path");

        horizontalLayout_status->addWidget(label_path);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_status->addItem(horizontalSpacer_2);

        label_count = new QLabel(FileViewWidget);
        label_count->setObjectName("label_count");

        horizontalLayout_status->addWidget(label_count);


        verticalLayout->addLayout(horizontalLayout_status);


        retranslateUi(FileViewWidget);

        stackedWidget->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(FileViewWidget);
    } // setupUi

    void retranslateUi(QWidget *FileViewWidget)
    {
        FileViewWidget->setWindowTitle(QCoreApplication::translate("FileViewWidget", "Form", nullptr));
#if QT_CONFIG(tooltip)
        pushButton_back->setToolTip(QCoreApplication::translate("FileViewWidget", "\350\277\224\345\233\236\344\270\212\344\270\200\347\272\247", nullptr));
#endif // QT_CONFIG(tooltip)
        pushButton_back->setText(QString());
#if QT_CONFIG(tooltip)
        pushButton_upload->setToolTip(QCoreApplication::translate("FileViewWidget", "\344\270\212\344\274\240\346\226\207\344\273\266", nullptr));
#endif // QT_CONFIG(tooltip)
        pushButton_upload->setText(QString());
#if QT_CONFIG(tooltip)
        pushButton_download->setToolTip(QCoreApplication::translate("FileViewWidget", "\344\270\213\350\275\275\346\226\207\344\273\266", nullptr));
#endif // QT_CONFIG(tooltip)
        pushButton_download->setText(QString());
#if QT_CONFIG(tooltip)
        pushButton_new_folder->setToolTip(QCoreApplication::translate("FileViewWidget", "\346\226\260\345\273\272\346\226\207\344\273\266\345\244\271", nullptr));
#endif // QT_CONFIG(tooltip)
        pushButton_new_folder->setText(QString());
#if QT_CONFIG(tooltip)
        pushButton_delete->setToolTip(QCoreApplication::translate("FileViewWidget", "\345\210\240\351\231\244", nullptr));
#endif // QT_CONFIG(tooltip)
        pushButton_delete->setText(QString());
#if QT_CONFIG(tooltip)
        pushButton_rename->setToolTip(QCoreApplication::translate("FileViewWidget", "\351\207\215\345\221\275\345\220\215", nullptr));
#endif // QT_CONFIG(tooltip)
        pushButton_rename->setText(QString());
#if QT_CONFIG(tooltip)
        pushButton_refresh->setToolTip(QCoreApplication::translate("FileViewWidget", "\345\210\267\346\226\260", nullptr));
#endif // QT_CONFIG(tooltip)
        pushButton_refresh->setText(QString());
        lineEdit_search->setPlaceholderText(QCoreApplication::translate("FileViewWidget", "\346\220\234\347\264\242\346\226\207\344\273\266...", nullptr));
#if QT_CONFIG(tooltip)
        pushButton_search->setToolTip(QCoreApplication::translate("FileViewWidget", "\346\220\234\347\264\242", nullptr));
#endif // QT_CONFIG(tooltip)
        pushButton_search->setText(QString());
        label_path->setText(QCoreApplication::translate("FileViewWidget", "\350\267\257\345\276\204: /", nullptr));
        label_count->setText(QCoreApplication::translate("FileViewWidget", "\345\205\261 0 \351\241\271", nullptr));
    } // retranslateUi

};

namespace Ui {
    class FileViewWidget: public Ui_FileViewWidget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FILEVIEWWIDGET_H
