/****************************************************************************
** Meta object code from reading C++ file 'mainwindow.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../mainwindow.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>
#include <QtCore/QList>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'mainwindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN10MainWindowE_t {};
} // unnamed namespace

template <> constexpr inline auto MainWindow::qt_create_metaobjectdata<qt_meta_tag_ZN10MainWindowE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "MainWindow",
        "on_action_upload_triggered",
        "",
        "on_action_new_folder_triggered",
        "on_action_refresh_triggered",
        "on_action_exit_triggered",
        "on_action_select_all_triggered",
        "on_action_copy_triggered",
        "on_action_cut_triggered",
        "on_action_paste_triggered",
        "on_action_delete_triggered",
        "on_action_rename_triggered",
        "on_action_list_view_triggered",
        "on_action_icon_view_triggered",
        "on_action_search_triggered",
        "on_action_share_triggered",
        "on_action_properties_triggered",
        "on_action_settings_triggered",
        "on_action_about_triggered",
        "on_action_upload_toolbar_triggered",
        "on_action_download_toolbar_triggered",
        "on_action_new_folder_toolbar_triggered",
        "on_action_delete_toolbar_triggered",
        "on_action_refresh_toolbar_triggered",
        "on_treeWidget_files_itemClicked",
        "QTreeWidgetItem*",
        "item",
        "column",
        "on_listWidget_friends_itemClicked",
        "QListWidgetItem*",
        "on_listWidget_messages_itemClicked",
        "on_tabWidget_center_tabCloseRequested",
        "index",
        "onConnectionStateChanged",
        "connected",
        "onFileListResponse",
        "success",
        "message",
        "QList<FileInfo>",
        "fileList",
        "onFriendListResponse",
        "QList<FriendInfo>",
        "friendList",
        "onFriendStatusNotify",
        "friendId",
        "online",
        "onMessageReceived",
        "MessageInfo",
        "onErrorOccurred",
        "error",
        "onFilePropertiesDialogAccepted",
        "onUploadFileResponse",
        "fileId",
        "offset",
        "onDownloadFileResponse",
        "fileName",
        "fileSize",
        "onCreateDirectoryResponse",
        "onDeleteFileResponse",
        "onRenameFileResponse",
        "onMoveFileResponse",
        "onCopyFileResponse",
        "newFileName",
        "onShareFileResponse",
        "shareCode",
        "onSearchFileResponse"
    };

    QtMocHelpers::UintData qt_methods {
        // Slot 'on_action_upload_triggered'
        QtMocHelpers::SlotData<void()>(1, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_new_folder_triggered'
        QtMocHelpers::SlotData<void()>(3, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_refresh_triggered'
        QtMocHelpers::SlotData<void()>(4, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_exit_triggered'
        QtMocHelpers::SlotData<void()>(5, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_select_all_triggered'
        QtMocHelpers::SlotData<void()>(6, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_copy_triggered'
        QtMocHelpers::SlotData<void()>(7, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_cut_triggered'
        QtMocHelpers::SlotData<void()>(8, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_paste_triggered'
        QtMocHelpers::SlotData<void()>(9, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_delete_triggered'
        QtMocHelpers::SlotData<void()>(10, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_rename_triggered'
        QtMocHelpers::SlotData<void()>(11, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_list_view_triggered'
        QtMocHelpers::SlotData<void()>(12, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_icon_view_triggered'
        QtMocHelpers::SlotData<void()>(13, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_search_triggered'
        QtMocHelpers::SlotData<void()>(14, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_share_triggered'
        QtMocHelpers::SlotData<void()>(15, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_properties_triggered'
        QtMocHelpers::SlotData<void()>(16, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_settings_triggered'
        QtMocHelpers::SlotData<void()>(17, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_about_triggered'
        QtMocHelpers::SlotData<void()>(18, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_upload_toolbar_triggered'
        QtMocHelpers::SlotData<void()>(19, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_download_toolbar_triggered'
        QtMocHelpers::SlotData<void()>(20, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_new_folder_toolbar_triggered'
        QtMocHelpers::SlotData<void()>(21, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_delete_toolbar_triggered'
        QtMocHelpers::SlotData<void()>(22, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_action_refresh_toolbar_triggered'
        QtMocHelpers::SlotData<void()>(23, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_treeWidget_files_itemClicked'
        QtMocHelpers::SlotData<void(QTreeWidgetItem *, int)>(24, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 25, 26 }, { QMetaType::Int, 27 },
        }}),
        // Slot 'on_listWidget_friends_itemClicked'
        QtMocHelpers::SlotData<void(QListWidgetItem *)>(28, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 29, 26 },
        }}),
        // Slot 'on_listWidget_messages_itemClicked'
        QtMocHelpers::SlotData<void(QListWidgetItem *)>(30, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 29, 26 },
        }}),
        // Slot 'on_tabWidget_center_tabCloseRequested'
        QtMocHelpers::SlotData<void(int)>(31, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 32 },
        }}),
        // Slot 'onConnectionStateChanged'
        QtMocHelpers::SlotData<void(bool)>(33, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 34 },
        }}),
        // Slot 'onFileListResponse'
        QtMocHelpers::SlotData<void(bool, const QString &, const QList<FileInfo> &)>(35, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 36 }, { QMetaType::QString, 37 }, { 0x80000000 | 38, 39 },
        }}),
        // Slot 'onFriendListResponse'
        QtMocHelpers::SlotData<void(bool, const QString &, const QList<FriendInfo> &)>(40, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 36 }, { QMetaType::QString, 37 }, { 0x80000000 | 41, 42 },
        }}),
        // Slot 'onFriendStatusNotify'
        QtMocHelpers::SlotData<void(quint32, bool)>(43, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::UInt, 44 }, { QMetaType::Bool, 45 },
        }}),
        // Slot 'onMessageReceived'
        QtMocHelpers::SlotData<void(const MessageInfo &)>(46, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 47, 37 },
        }}),
        // Slot 'onErrorOccurred'
        QtMocHelpers::SlotData<void(const QString &)>(48, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 49 },
        }}),
        // Slot 'onFilePropertiesDialogAccepted'
        QtMocHelpers::SlotData<void()>(50, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onUploadFileResponse'
        QtMocHelpers::SlotData<void(bool, const QString &, quint32, quint64)>(51, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 36 }, { QMetaType::QString, 37 }, { QMetaType::UInt, 52 }, { QMetaType::ULongLong, 53 },
        }}),
        // Slot 'onDownloadFileResponse'
        QtMocHelpers::SlotData<void(bool, const QString &, quint32, const QString &, quint64)>(54, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 36 }, { QMetaType::QString, 37 }, { QMetaType::UInt, 52 }, { QMetaType::QString, 55 },
            { QMetaType::ULongLong, 56 },
        }}),
        // Slot 'onCreateDirectoryResponse'
        QtMocHelpers::SlotData<void(bool, const QString &)>(57, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 36 }, { QMetaType::QString, 37 },
        }}),
        // Slot 'onDeleteFileResponse'
        QtMocHelpers::SlotData<void(bool, const QString &)>(58, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 36 }, { QMetaType::QString, 37 },
        }}),
        // Slot 'onRenameFileResponse'
        QtMocHelpers::SlotData<void(bool, const QString &)>(59, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 36 }, { QMetaType::QString, 37 },
        }}),
        // Slot 'onMoveFileResponse'
        QtMocHelpers::SlotData<void(bool, const QString &)>(60, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 36 }, { QMetaType::QString, 37 },
        }}),
        // Slot 'onCopyFileResponse'
        QtMocHelpers::SlotData<void(bool, const QString &, const QString &)>(61, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 36 }, { QMetaType::QString, 37 }, { QMetaType::QString, 62 },
        }}),
        // Slot 'onShareFileResponse'
        QtMocHelpers::SlotData<void(bool, const QString &, const QString &)>(63, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 36 }, { QMetaType::QString, 37 }, { QMetaType::QString, 64 },
        }}),
        // Slot 'onSearchFileResponse'
        QtMocHelpers::SlotData<void(bool, const QString &, const QList<FileInfo> &)>(65, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 36 }, { QMetaType::QString, 37 }, { 0x80000000 | 38, 39 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<MainWindow, qt_meta_tag_ZN10MainWindowE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject MainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10MainWindowE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10MainWindowE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN10MainWindowE_t>.metaTypes,
    nullptr
} };

void MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<MainWindow *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->on_action_upload_triggered(); break;
        case 1: _t->on_action_new_folder_triggered(); break;
        case 2: _t->on_action_refresh_triggered(); break;
        case 3: _t->on_action_exit_triggered(); break;
        case 4: _t->on_action_select_all_triggered(); break;
        case 5: _t->on_action_copy_triggered(); break;
        case 6: _t->on_action_cut_triggered(); break;
        case 7: _t->on_action_paste_triggered(); break;
        case 8: _t->on_action_delete_triggered(); break;
        case 9: _t->on_action_rename_triggered(); break;
        case 10: _t->on_action_list_view_triggered(); break;
        case 11: _t->on_action_icon_view_triggered(); break;
        case 12: _t->on_action_search_triggered(); break;
        case 13: _t->on_action_share_triggered(); break;
        case 14: _t->on_action_properties_triggered(); break;
        case 15: _t->on_action_settings_triggered(); break;
        case 16: _t->on_action_about_triggered(); break;
        case 17: _t->on_action_upload_toolbar_triggered(); break;
        case 18: _t->on_action_download_toolbar_triggered(); break;
        case 19: _t->on_action_new_folder_toolbar_triggered(); break;
        case 20: _t->on_action_delete_toolbar_triggered(); break;
        case 21: _t->on_action_refresh_toolbar_triggered(); break;
        case 22: _t->on_treeWidget_files_itemClicked((*reinterpret_cast< std::add_pointer_t<QTreeWidgetItem*>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 23: _t->on_listWidget_friends_itemClicked((*reinterpret_cast< std::add_pointer_t<QListWidgetItem*>>(_a[1]))); break;
        case 24: _t->on_listWidget_messages_itemClicked((*reinterpret_cast< std::add_pointer_t<QListWidgetItem*>>(_a[1]))); break;
        case 25: _t->on_tabWidget_center_tabCloseRequested((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 26: _t->onConnectionStateChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 27: _t->onFileListResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QList<FileInfo>>>(_a[3]))); break;
        case 28: _t->onFriendListResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QList<FriendInfo>>>(_a[3]))); break;
        case 29: _t->onFriendStatusNotify((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 30: _t->onMessageReceived((*reinterpret_cast< std::add_pointer_t<MessageInfo>>(_a[1]))); break;
        case 31: _t->onErrorOccurred((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 32: _t->onFilePropertiesDialogAccepted(); break;
        case 33: _t->onUploadFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<quint32>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<quint64>>(_a[4]))); break;
        case 34: _t->onDownloadFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<quint32>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[4])),(*reinterpret_cast< std::add_pointer_t<quint64>>(_a[5]))); break;
        case 35: _t->onCreateDirectoryResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 36: _t->onDeleteFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 37: _t->onRenameFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 38: _t->onMoveFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 39: _t->onCopyFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 40: _t->onShareFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 41: _t->onSearchFileResponse((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QList<FileInfo>>>(_a[3]))); break;
        default: ;
        }
    }
}

const QMetaObject *MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10MainWindowE_t>.strings))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 42)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 42;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 42)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 42;
    }
    return _id;
}
QT_WARNING_POP
