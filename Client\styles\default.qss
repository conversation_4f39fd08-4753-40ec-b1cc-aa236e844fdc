/* Cloud7 默认样式表 */

/* 主窗口样式 */
QMainWindow {
    background-color: #f5f5f5;
    color: #333333;
}

/* 菜单栏样式 */
QMenuBar {
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    padding: 2px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
    margin: 2px;
    border-radius: 3px;
}

QMenuBar::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

/* 工具栏样式 */
QToolBar {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    spacing: 2px;
    padding: 2px;
}

QToolButton {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 3px;
    padding: 4px;
    margin: 1px;
}

QToolButton:hover {
    background-color: #e3f2fd;
    border-color: #bbdefb;
}

QToolButton:pressed {
    background-color: #bbdefb;
}

/* 按钮样式 */
QPushButton {
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #1976d2;
}

QPushButton:pressed {
    background-color: #0d47a1;
}

QPushButton:disabled {
    background-color: #cccccc;
    color: #666666;
}

/* 输入框样式 */
QLineEdit {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 6px;
    background-color: white;
}

QLineEdit:focus {
    border-color: #2196f3;
    outline: none;
}

/* 列表视图样式 */
QListView {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    selection-background-color: #e3f2fd;
}

QListView::item {
    padding: 4px;
    border-bottom: 1px solid #f0f0f0;
}

QListView::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

/* 树视图样式 */
QTreeView {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    selection-background-color: #e3f2fd;
}

QTreeView::item {
    padding: 2px;
}

QTreeView::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

/* 标签页样式 */
QTabWidget::pane {
    border: 1px solid #e0e0e0;
    background-color: white;
}

QTabBar::tab {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-bottom: none;
    padding: 6px 12px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom: 2px solid #2196f3;
}

QTabBar::tab:hover {
    background-color: #e3f2fd;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    color: #666666;
}

/* 分割器样式 */
QSplitter::handle {
    background-color: #e0e0e0;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

/* 进度条样式 */
QProgressBar {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    text-align: center;
    background-color: #f5f5f5;
}

QProgressBar::chunk {
    background-color: #4caf50;
    border-radius: 3px;
}
