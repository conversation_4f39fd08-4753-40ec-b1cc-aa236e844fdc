#ifndef FILETRANSFERDIALOG_H
#define FILETRANSFERDIALOG_H

#include "../Common/common.h"
#include "filetransfermanager.h"
#include <QDialog>
#include <QProgressBar>
#include <QTableWidgetItem>


namespace Ui {
class FileTransferDialog;
}

class FileTransferDialog : public QDialog {
  Q_OBJECT

public:
  explicit FileTransferDialog(QWidget *parent = nullptr);
  ~FileTransferDialog();

  // 添加上传任务
  quint32 addUploadTask(const QString &localPath, quint32 parentId);

  // 添加下载任务
  quint32 addDownloadTask(quint32 fileId, const QString &localPath);

private slots:
  // 刷新按钮点击槽函数
  void on_pushButton_refresh_clicked();

  // 清理按钮点击槽函数
  void on_pushButton_cleanup_clicked();

  // 开始按钮点击槽函数
  void on_pushButton_start_clicked();

  // 暂停按钮点击槽函数
  void on_pushButton_pause_clicked();

  // 继续按钮点击槽函数
  void on_pushButton_resume_clicked();

  // 取消按钮点击槽函数
  void on_pushButton_cancel_clicked();

  // 任务表格点击槽函数
  void on_tableWidget_tasks_itemClicked(QTableWidgetItem *item);

  // 任务状态改变槽函数
  void onTaskStatusChanged(quint32 taskId, TransferStatus status);

  // 任务进度更新槽函数
  void onTaskProgressUpdated(quint32 taskId, quint64 bytesTransferred,
                             quint64 fileSize);

  // 任务速度更新槽函数
  void onTaskSpeedUpdated(quint32 taskId, quint32 speed, int remainingTime);

private:
  // 初始化UI
  void initUI();

  // 初始化表格
  void initTable();

  // 更新任务列表
  void updateTaskList();

  // 更新任务行
  void updateTaskRow(quint32 taskId);

  // 获取选中的任务ID
  quint32 getSelectedTaskId() const;

  // 格式化文件大小
  QString formatFileSize(quint64 size) const;

  // 格式化传输速度
  QString formatTransferSpeed(quint32 speed) const;

  // 格式化剩余时间
  QString formatRemainingTime(int seconds) const;

  // 获取状态字符串
  QString getStatusString(TransferStatus status) const;

  Ui::FileTransferDialog *ui;             // UI对象
  FileTransferManager *m_transferManager; // 文件传输管理器
  QMap<quint32, int> m_taskRowMap;        // 任务ID到表格行号的映射
};

#endif // FILETRANSFERDIALOG_H
