/********************************************************************************
** Form generated from reading UI file 'filetransferdialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FILETRANSFERDIALOG_H
#define UI_FILETRANSFERDIALOG_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_FileTransferDialog
{
public:
    QVBoxLayout *verticalLayout;
    QTableWidget *tableWidget_tasks;
    QHBoxLayout *horizontalLayout_buttons;
    QPushButton *pushButton_start;
    QPushButton *pushButton_pause;
    QPushButton *pushButton_resume;
    QPushButton *pushButton_cancel;
    QSpacerItem *horizontalSpacer;
    QPushButton *pushButton_refresh;
    QPushButton *pushButton_cleanup;

    void setupUi(QDialog *FileTransferDialog)
    {
        if (FileTransferDialog->objectName().isEmpty())
            FileTransferDialog->setObjectName("FileTransferDialog");
        FileTransferDialog->resize(800, 500);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/transfer.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        FileTransferDialog->setWindowIcon(icon);
        verticalLayout = new QVBoxLayout(FileTransferDialog);
        verticalLayout->setObjectName("verticalLayout");
        tableWidget_tasks = new QTableWidget(FileTransferDialog);
        if (tableWidget_tasks->columnCount() < 8)
            tableWidget_tasks->setColumnCount(8);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        tableWidget_tasks->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        tableWidget_tasks->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        tableWidget_tasks->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        tableWidget_tasks->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        tableWidget_tasks->setHorizontalHeaderItem(4, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        tableWidget_tasks->setHorizontalHeaderItem(5, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        tableWidget_tasks->setHorizontalHeaderItem(6, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        tableWidget_tasks->setHorizontalHeaderItem(7, __qtablewidgetitem7);
        tableWidget_tasks->setObjectName("tableWidget_tasks");
        tableWidget_tasks->setSelectionMode(QAbstractItemView::SingleSelection);
        tableWidget_tasks->setSelectionBehavior(QAbstractItemView::SelectRows);

        verticalLayout->addWidget(tableWidget_tasks);

        horizontalLayout_buttons = new QHBoxLayout();
        horizontalLayout_buttons->setObjectName("horizontalLayout_buttons");
        pushButton_start = new QPushButton(FileTransferDialog);
        pushButton_start->setObjectName("pushButton_start");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/start.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_start->setIcon(icon1);

        horizontalLayout_buttons->addWidget(pushButton_start);

        pushButton_pause = new QPushButton(FileTransferDialog);
        pushButton_pause->setObjectName("pushButton_pause");
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/pause.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_pause->setIcon(icon2);

        horizontalLayout_buttons->addWidget(pushButton_pause);

        pushButton_resume = new QPushButton(FileTransferDialog);
        pushButton_resume->setObjectName("pushButton_resume");
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/icons/resume.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_resume->setIcon(icon3);

        horizontalLayout_buttons->addWidget(pushButton_resume);

        pushButton_cancel = new QPushButton(FileTransferDialog);
        pushButton_cancel->setObjectName("pushButton_cancel");
        QIcon icon4;
        icon4.addFile(QString::fromUtf8(":/icons/cancel.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_cancel->setIcon(icon4);

        horizontalLayout_buttons->addWidget(pushButton_cancel);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer);

        pushButton_refresh = new QPushButton(FileTransferDialog);
        pushButton_refresh->setObjectName("pushButton_refresh");
        QIcon icon5;
        icon5.addFile(QString::fromUtf8(":/icons/refresh.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_refresh->setIcon(icon5);

        horizontalLayout_buttons->addWidget(pushButton_refresh);

        pushButton_cleanup = new QPushButton(FileTransferDialog);
        pushButton_cleanup->setObjectName("pushButton_cleanup");
        QIcon icon6;
        icon6.addFile(QString::fromUtf8(":/icons/cleanup.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButton_cleanup->setIcon(icon6);

        horizontalLayout_buttons->addWidget(pushButton_cleanup);


        verticalLayout->addLayout(horizontalLayout_buttons);


        retranslateUi(FileTransferDialog);

        QMetaObject::connectSlotsByName(FileTransferDialog);
    } // setupUi

    void retranslateUi(QDialog *FileTransferDialog)
    {
        FileTransferDialog->setWindowTitle(QCoreApplication::translate("FileTransferDialog", "\346\226\207\344\273\266\344\274\240\350\276\223", nullptr));
        QTableWidgetItem *___qtablewidgetitem = tableWidget_tasks->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("FileTransferDialog", "\344\273\273\345\212\241ID", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget_tasks->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("FileTransferDialog", "\346\226\207\344\273\266\345\220\215", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = tableWidget_tasks->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QCoreApplication::translate("FileTransferDialog", "\347\261\273\345\236\213", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = tableWidget_tasks->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QCoreApplication::translate("FileTransferDialog", "\345\244\247\345\260\217", nullptr));
        QTableWidgetItem *___qtablewidgetitem4 = tableWidget_tasks->horizontalHeaderItem(4);
        ___qtablewidgetitem4->setText(QCoreApplication::translate("FileTransferDialog", "\350\277\233\345\272\246", nullptr));
        QTableWidgetItem *___qtablewidgetitem5 = tableWidget_tasks->horizontalHeaderItem(5);
        ___qtablewidgetitem5->setText(QCoreApplication::translate("FileTransferDialog", "\351\200\237\345\272\246", nullptr));
        QTableWidgetItem *___qtablewidgetitem6 = tableWidget_tasks->horizontalHeaderItem(6);
        ___qtablewidgetitem6->setText(QCoreApplication::translate("FileTransferDialog", "\345\211\251\344\275\231\346\227\266\351\227\264", nullptr));
        QTableWidgetItem *___qtablewidgetitem7 = tableWidget_tasks->horizontalHeaderItem(7);
        ___qtablewidgetitem7->setText(QCoreApplication::translate("FileTransferDialog", "\347\212\266\346\200\201", nullptr));
        pushButton_start->setText(QCoreApplication::translate("FileTransferDialog", "\345\274\200\345\247\213", nullptr));
        pushButton_pause->setText(QCoreApplication::translate("FileTransferDialog", "\346\232\202\345\201\234", nullptr));
        pushButton_resume->setText(QCoreApplication::translate("FileTransferDialog", "\347\273\247\347\273\255", nullptr));
        pushButton_cancel->setText(QCoreApplication::translate("FileTransferDialog", "\345\217\226\346\266\210", nullptr));
        pushButton_refresh->setText(QCoreApplication::translate("FileTransferDialog", "\345\210\267\346\226\260", nullptr));
        pushButton_cleanup->setText(QCoreApplication::translate("FileTransferDialog", "\346\270\205\347\220\206\345\267\262\345\256\214\346\210\220", nullptr));
    } // retranslateUi

};

namespace Ui {
    class FileTransferDialog: public Ui_FileTransferDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FILETRANSFERDIALOG_H
