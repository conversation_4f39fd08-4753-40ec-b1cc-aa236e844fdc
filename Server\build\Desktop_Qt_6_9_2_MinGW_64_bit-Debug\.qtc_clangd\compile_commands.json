[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\errorhandler.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/errorhandler.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\exception.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/exception.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\utils.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/utils.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\configmanager.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Server/configmanager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\databasemanager.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Server/databasemanager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\filehandler.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Server/filehandler.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\logger.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Server/logger.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\main.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Server/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\server.cpp"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Server/server.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\common.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/common.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\errorhandler.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/errorhandler.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\exception.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/exception.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Common\\utils.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Common/utils.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\configmanager.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Server/configmanager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\databasemanager.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Server/databasemanager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\filehandler.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Server/filehandler.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\logger.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Server/logger.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IF:\\QT\\QT6.9.2\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtWidgets", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtGui", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtNetwork", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtSql", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\include\\QtCore", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug\\debug", "-ID:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\build\\Desktop_Qt_6_9_2_MinGW_64_bit-Debug", "-IF:\\QT\\QT6.9.2\\6.9.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "F:\\QT\\QT6.9.2\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\XINGWANG LIU\\Desktop\\Cloud\\Server\\server.h"], "directory": "D:/XINGWANG LIU/Desktop/Cloud/Server/build/Desktop_Qt_6_9_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/XINGWANG LIU/Desktop/Cloud/Server/server.h"}]