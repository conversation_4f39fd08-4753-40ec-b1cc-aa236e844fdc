/****************************************************************************
** Meta object code from reading C++ file 'filetransferdialog.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../filetransferdialog.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'filetransferdialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN18FileTransferDialogE_t {};
} // unnamed namespace

template <> constexpr inline auto FileTransferDialog::qt_create_metaobjectdata<qt_meta_tag_ZN18FileTransferDialogE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "FileTransferDialog",
        "on_pushButton_refresh_clicked",
        "",
        "on_pushButton_cleanup_clicked",
        "on_pushButton_start_clicked",
        "on_pushButton_pause_clicked",
        "on_pushButton_resume_clicked",
        "on_pushButton_cancel_clicked",
        "on_tableWidget_tasks_itemClicked",
        "QTableWidgetItem*",
        "item",
        "onTaskStatusChanged",
        "taskId",
        "TransferStatus",
        "status",
        "onTaskProgressUpdated",
        "bytesTransferred",
        "fileSize",
        "onTaskSpeedUpdated",
        "speed",
        "remainingTime"
    };

    QtMocHelpers::UintData qt_methods {
        // Slot 'on_pushButton_refresh_clicked'
        QtMocHelpers::SlotData<void()>(1, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButton_cleanup_clicked'
        QtMocHelpers::SlotData<void()>(3, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButton_start_clicked'
        QtMocHelpers::SlotData<void()>(4, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButton_pause_clicked'
        QtMocHelpers::SlotData<void()>(5, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButton_resume_clicked'
        QtMocHelpers::SlotData<void()>(6, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButton_cancel_clicked'
        QtMocHelpers::SlotData<void()>(7, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_tableWidget_tasks_itemClicked'
        QtMocHelpers::SlotData<void(QTableWidgetItem *)>(8, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 9, 10 },
        }}),
        // Slot 'onTaskStatusChanged'
        QtMocHelpers::SlotData<void(quint32, TransferStatus)>(11, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::UInt, 12 }, { 0x80000000 | 13, 14 },
        }}),
        // Slot 'onTaskProgressUpdated'
        QtMocHelpers::SlotData<void(quint32, quint64, quint64)>(15, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::UInt, 12 }, { QMetaType::ULongLong, 16 }, { QMetaType::ULongLong, 17 },
        }}),
        // Slot 'onTaskSpeedUpdated'
        QtMocHelpers::SlotData<void(quint32, quint32, int)>(18, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::UInt, 12 }, { QMetaType::UInt, 19 }, { QMetaType::Int, 20 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<FileTransferDialog, qt_meta_tag_ZN18FileTransferDialogE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject FileTransferDialog::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18FileTransferDialogE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18FileTransferDialogE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN18FileTransferDialogE_t>.metaTypes,
    nullptr
} };

void FileTransferDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<FileTransferDialog *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->on_pushButton_refresh_clicked(); break;
        case 1: _t->on_pushButton_cleanup_clicked(); break;
        case 2: _t->on_pushButton_start_clicked(); break;
        case 3: _t->on_pushButton_pause_clicked(); break;
        case 4: _t->on_pushButton_resume_clicked(); break;
        case 5: _t->on_pushButton_cancel_clicked(); break;
        case 6: _t->on_tableWidget_tasks_itemClicked((*reinterpret_cast< std::add_pointer_t<QTableWidgetItem*>>(_a[1]))); break;
        case 7: _t->onTaskStatusChanged((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<TransferStatus>>(_a[2]))); break;
        case 8: _t->onTaskProgressUpdated((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<quint64>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<quint64>>(_a[3]))); break;
        case 9: _t->onTaskSpeedUpdated((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<quint32>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        default: ;
        }
    }
}

const QMetaObject *FileTransferDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *FileTransferDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18FileTransferDialogE_t>.strings))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int FileTransferDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 10;
    }
    return _id;
}
QT_WARNING_POP
