#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include "../Common/common.h"
#include <QMutex>
#include <QMutexLocker>
#include <QObject>
#include <QSettings>
#include <QString>


// 配置管理器类（单例模式）
class ConfigManager : public QObject {
  Q_OBJECT

public:
  // 获取单例实例
  static ConfigManager *getInstance();

  // 加载配置
  void loadConfig();

  // 保存配置
  void saveConfig();

  // 获取客户端配置
  ClientConfig getClientConfig() const;

  // 设置客户端配置
  void setClientConfig(const ClientConfig &config);

  // 获取特定配置项
  QString getServerHost() const;
  quint16 getServerPort() const;
  QString getDownloadDir() const;
  int getHeartbeatInterval() const;
  int getReconnectInterval() const;
  int getMaxRetries() const;
  int getChunkSize() const;

  // 设置特定配置项
  void setServerHost(const QString &host);
  void setServerPort(quint16 port);
  void setDownloadDir(const QString &dir);
  void setHeartbeatInterval(int interval);
  void setReconnectInterval(int interval);
  void setMaxRetries(int retries);
  void setChunkSize(int size);

private:
  // 私有构造函数
  ConfigManager(QObject *parent = nullptr);

  // 私有析构函数
  ~ConfigManager();

  // 禁止拷贝构造和赋值
  ConfigManager(const ConfigManager &) = delete;
  ConfigManager &operator=(const ConfigManager &) = delete;

  // 设置默认配置
  void setDefaultConfig();

  static ConfigManager *m_instance; // 单例实例
  static QMutex m_mutex;            // 互斥锁
  QSettings *m_settings;            // 配置设置
  ClientConfig m_config;            // 客户端配置
  QString m_configPath;             // 配置文件路径
};

#endif // CONFIGMANAGER_H
