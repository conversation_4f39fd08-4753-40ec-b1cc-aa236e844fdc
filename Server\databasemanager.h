#ifndef DATABASEMANAGER_H
#define DATABASEMANAGER_H

#include "../Common/common.h"
#include <QCryptographicHash>
#include <QDateTime>
#include <QDir>
#include <QList>
#include <QMutex>
#include <QMutexLocker>
#include <QObject>
#include <QSqlDatabase>
#include <QSqlError>
#include <QSqlQuery>
#include <QVariant>

// 数据库管理器类（单例模式）
class DatabaseManager : public QObject {
  Q_OBJECT

public:
  // 获取单例实例
  static DatabaseManager *getInstance();

  // 初始化数据库
  bool initialize(const QString &dbPath);

  // 关闭数据库
  void close();

  // 用户相关操作
  bool addUser(const QString &username, const QString &passwordHash,
               const QString &salt, const QString &email);
  bool getUser(const QString &username, UserInfo &userInfo);
  bool getUserById(quint32 userId, UserInfo &userInfo);
  bool updateUser(quint32 userId, const UserInfo &userInfo);
  bool updateUserPassword(quint32 userId, const QString &newPasswordHash,
                          const QString &salt);
  bool updateUserStorage(quint32 userId, quint64 storageUsed);
  bool deleteUser(quint32 userId);
  bool checkUsernameExists(const QString &username);
  bool checkEmailExists(const QString &email);

  // 文件相关操作
  bool addFile(const FileInfo &fileInfo);
  bool getFile(quint32 fileId, FileInfo &fileInfo);
  bool getFilesByParentId(quint32 parentId, QList<FileInfo> &fileList);
  bool getFilesByUserId(quint32 userId, QList<FileInfo> &fileList);
  bool updateFile(const FileInfo &fileInfo);
  bool deleteFile(quint32 fileId);
  bool deleteFilesByParentId(quint32 parentId);
  bool checkFileExists(quint32 parentId, const QString &fileName);
  bool copyFile(quint32 fileId, quint32 newParentId, QString &newFileName);
  bool moveFile(quint32 fileId, quint32 newParentId);
  bool searchFiles(quint32 userId, const QString &keyword, quint32 parentId,
                   QList<FileInfo> &fileList);
  QString generateShareCode(quint32 fileId);
  bool shareFile(quint32 fileId, const QString &expireTime);
  bool getShareByFileId(quint32 fileId, ShareInfo &shareInfo);
  quint64 getUserStorageUsed(quint32 userId);

  // 好友相关操作
  bool addFriendRelationship(quint32 userId, quint32 friendId);
  bool removeFriendRelationship(quint32 userId, quint32 friendId);
  bool getFriendList(quint32 userId, QList<FriendInfo> &friendList);
  bool checkFriendRelationshipExists(quint32 userId, quint32 friendId);

  // 消息相关操作
  bool addMessage(const MessageInfo &messageInfo);
  bool getMessageHistory(quint32 userId, quint32 friendId, quint32 offset,
                         quint32 count, QList<MessageInfo> &messageList);
  bool getOfflineMessages(quint32 userId, QList<MessageInfo> &messageList);
  bool markMessagesAsRead(quint32 userId, quint32 friendId);

  // 会话相关操作
  bool addSession(quint32 userId, const QString &sessionId);
  bool removeSession(const QString &sessionId);
  bool getSession(const QString &sessionId, quint32 &userId);
  bool updateSessionHeartbeat(const QString &sessionId);
  bool removeExpiredSessions(int timeoutSeconds);
  bool getSessionByUserId(quint32 userId, QString &sessionId);

  // 分享相关操作
  bool addShare(const ShareInfo &shareInfo);
  bool getShare(const QString &shareCode, ShareInfo &shareInfo);
  bool removeShare(const QString &shareCode);
  bool removeExpiredShares();
  bool incrementShareDownloadCount(const QString &shareCode);

private:
  // 私有构造函数
  DatabaseManager(QObject *parent = nullptr);

  // 私有析构函数
  ~DatabaseManager();

  // 禁止拷贝构造和赋值
  DatabaseManager(const DatabaseManager &) = delete;
  DatabaseManager &operator=(const DatabaseManager &) = delete;

  // 创建表
  bool createTables();

  // 计算密码哈希
  QString calculatePasswordHash(const QString &password, const QString &salt);

  // 生成随机盐值
  QString generateSalt();

  // 执行查询并检查错误
  bool executeQuery(QSqlQuery &query, const QString &sql);

  // 格式化日期时间
  QString formatDateTime(const QDateTime &dateTime) const;

  // 解析日期时间
  QDateTime parseDateTime(const QString &dateTimeStr) const;

  static DatabaseManager *m_instance; // 单例实例
  static QMutex m_mutex;              // 互斥锁
  QSqlDatabase m_db;                  // 数据库连接
  QString m_dbPath;                   // 数据库路径
};

#endif // DATABASEMANAGER_H
