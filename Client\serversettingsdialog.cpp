#include "serversettingsdialog.h"
#include "logger.h"
#include <QHostAddress>
#include <QMessageBox>
#include <QRegularExpression>
#include <QRegularExpressionValidator>

// 构造函数
ServerSettingsDialog::ServerSettingsDialog(QWidget *parent)
    : QDialog(parent), ui(new Ui::ServerSettingsDialog),
      m_configManager(nullptr) {
  // 初始化UI
  ui->setupUi(this);

  // 初始化成员变量
  m_configManager = ConfigManager::getInstance();

  // 初始化UI
  initUI();

  // 加载配置
  loadConfig();
}

// 析构函数
ServerSettingsDialog::~ServerSettingsDialog() { delete ui; }

// 初始化UI
void ServerSettingsDialog::initUI() {
  // 设置窗口标题
  setWindowTitle("服务器设置");

  // 设置窗口图标
  setWindowIcon(QIcon(":/icons/settings.png"));

  // 设置服务器地址输入框验证器（允许IP地址和域名）
  QRegularExpression hostRegExp("^[a-zA-Z0-9.-]+$");
  QValidator *hostValidator = new QRegularExpressionValidator(hostRegExp, this);
  ui->lineEdit_host->setValidator(hostValidator);

  // 设置端口范围
  ui->spinBox_port->setRange(1024, 65535);
}

// 加载配置
void ServerSettingsDialog::loadConfig() {
  // 获取客户端配置
  ClientConfig config = m_configManager->getClientConfig();

  // 设置服务器地址和端口
  ui->lineEdit_host->setText(config.serverHost);
  ui->spinBox_port->setValue(config.serverPort);
}

// 保存配置
void ServerSettingsDialog::saveConfig() {
  // 获取客户端配置
  ClientConfig config = m_configManager->getClientConfig();

  // 设置服务器地址和端口
  config.serverHost = ui->lineEdit_host->text().trimmed();
  config.serverPort = ui->spinBox_port->value();

  // 保存配置
  m_configManager->setClientConfig(config);
}

// 验证输入
bool ServerSettingsDialog::validateInput() {
  // 获取服务器地址和端口
  QString host = ui->lineEdit_host->text().trimmed();
  quint16 port = ui->spinBox_port->value();

  // 验证服务器地址
  if (host.isEmpty()) {
    QMessageBox::warning(this, "输入错误", "请输入服务器地址");
    ui->lineEdit_host->setFocus();
    return false;
  }

  // 验证是否为有效的IP地址或域名
  QHostAddress address;
  if (!address.setAddress(host)) {
    // 如果不是IP地址，则检查是否为有效的域名
    QRegularExpression domainRegExp(
        "^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]\\.[a-zA-Z]{2,}$");
    if (!domainRegExp.match(host).hasMatch()) {
      QMessageBox::warning(this, "输入错误",
                           "请输入有效的服务器地址（IP地址或域名）");
      ui->lineEdit_host->setFocus();
      return false;
    }
  }

  // 验证端口
  if (port < 1024 || port > 65535) {
    QMessageBox::warning(this, "输入错误", "请输入有效的端口号（1024-65535）");
    ui->spinBox_port->setFocus();
    return false;
  }

  return true;
}

// 对话框接受槽函数
void ServerSettingsDialog::accept() {
  // 验证输入
  if (!validateInput()) {
    return;
  }

  // 保存配置
  saveConfig();

  LOG_INFO("ServerSettings", QString("服务器设置已更新，地址: %1:%2")
                                 .arg(ui->lineEdit_host->text().trimmed())
                                 .arg(ui->spinBox_port->value()));

  // 调用父类的accept方法
  QDialog::accept();
}
