/********************************************************************************
** Form generated from reading UI file 'filepropertiesdialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FILEPROPERTIESDIALOG_H
#define UI_FILEPROPERTIESDIALOG_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_FilePropertiesDialog
{
public:
    QVBoxLayout *verticalLayout;
    QFormLayout *formLayout;
    QLabel *label_name;
    QLabel *label_name_value;
    QLabel *label_type;
    QLabel *label_type_value;
    QLabel *label_size;
    QLabel *label_size_value;
    QLabel *label_hash;
    QLabel *label_hash_value;
    QLabel *label_create_time;
    QLabel *label_create_time_value;
    QLabel *label_modify_time;
    QLabel *label_modify_time_value;
    QLabel *label_path;
    QLabel *label_path_value;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *horizontalLayout_button;
    QSpacerItem *horizontalSpacer;
    QPushButton *pushButton_ok;

    void setupUi(QDialog *FilePropertiesDialog)
    {
        if (FilePropertiesDialog->objectName().isEmpty())
            FilePropertiesDialog->setObjectName("FilePropertiesDialog");
        FilePropertiesDialog->resize(400, 350);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/properties.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        FilePropertiesDialog->setWindowIcon(icon);
        verticalLayout = new QVBoxLayout(FilePropertiesDialog);
        verticalLayout->setObjectName("verticalLayout");
        formLayout = new QFormLayout();
        formLayout->setObjectName("formLayout");
        formLayout->setHorizontalSpacing(20);
        formLayout->setVerticalSpacing(15);
        label_name = new QLabel(FilePropertiesDialog);
        label_name->setObjectName("label_name");

        formLayout->setWidget(0, QFormLayout::ItemRole::LabelRole, label_name);

        label_name_value = new QLabel(FilePropertiesDialog);
        label_name_value->setObjectName("label_name_value");
        label_name_value->setWordWrap(true);

        formLayout->setWidget(0, QFormLayout::ItemRole::FieldRole, label_name_value);

        label_type = new QLabel(FilePropertiesDialog);
        label_type->setObjectName("label_type");

        formLayout->setWidget(1, QFormLayout::ItemRole::LabelRole, label_type);

        label_type_value = new QLabel(FilePropertiesDialog);
        label_type_value->setObjectName("label_type_value");

        formLayout->setWidget(1, QFormLayout::ItemRole::FieldRole, label_type_value);

        label_size = new QLabel(FilePropertiesDialog);
        label_size->setObjectName("label_size");

        formLayout->setWidget(2, QFormLayout::ItemRole::LabelRole, label_size);

        label_size_value = new QLabel(FilePropertiesDialog);
        label_size_value->setObjectName("label_size_value");

        formLayout->setWidget(2, QFormLayout::ItemRole::FieldRole, label_size_value);

        label_hash = new QLabel(FilePropertiesDialog);
        label_hash->setObjectName("label_hash");

        formLayout->setWidget(3, QFormLayout::ItemRole::LabelRole, label_hash);

        label_hash_value = new QLabel(FilePropertiesDialog);
        label_hash_value->setObjectName("label_hash_value");
        label_hash_value->setWordWrap(true);

        formLayout->setWidget(3, QFormLayout::ItemRole::FieldRole, label_hash_value);

        label_create_time = new QLabel(FilePropertiesDialog);
        label_create_time->setObjectName("label_create_time");

        formLayout->setWidget(4, QFormLayout::ItemRole::LabelRole, label_create_time);

        label_create_time_value = new QLabel(FilePropertiesDialog);
        label_create_time_value->setObjectName("label_create_time_value");

        formLayout->setWidget(4, QFormLayout::ItemRole::FieldRole, label_create_time_value);

        label_modify_time = new QLabel(FilePropertiesDialog);
        label_modify_time->setObjectName("label_modify_time");

        formLayout->setWidget(5, QFormLayout::ItemRole::LabelRole, label_modify_time);

        label_modify_time_value = new QLabel(FilePropertiesDialog);
        label_modify_time_value->setObjectName("label_modify_time_value");

        formLayout->setWidget(5, QFormLayout::ItemRole::FieldRole, label_modify_time_value);

        label_path = new QLabel(FilePropertiesDialog);
        label_path->setObjectName("label_path");

        formLayout->setWidget(6, QFormLayout::ItemRole::LabelRole, label_path);

        label_path_value = new QLabel(FilePropertiesDialog);
        label_path_value->setObjectName("label_path_value");
        label_path_value->setWordWrap(true);

        formLayout->setWidget(6, QFormLayout::ItemRole::FieldRole, label_path_value);


        verticalLayout->addLayout(formLayout);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        horizontalLayout_button = new QHBoxLayout();
        horizontalLayout_button->setObjectName("horizontalLayout_button");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_button->addItem(horizontalSpacer);

        pushButton_ok = new QPushButton(FilePropertiesDialog);
        pushButton_ok->setObjectName("pushButton_ok");

        horizontalLayout_button->addWidget(pushButton_ok);


        verticalLayout->addLayout(horizontalLayout_button);


        retranslateUi(FilePropertiesDialog);

        pushButton_ok->setDefault(true);


        QMetaObject::connectSlotsByName(FilePropertiesDialog);
    } // setupUi

    void retranslateUi(QDialog *FilePropertiesDialog)
    {
        FilePropertiesDialog->setWindowTitle(QCoreApplication::translate("FilePropertiesDialog", "\346\226\207\344\273\266\345\261\236\346\200\247", nullptr));
        label_name->setText(QCoreApplication::translate("FilePropertiesDialog", "\346\226\207\344\273\266\345\220\215\357\274\232", nullptr));
        label_name_value->setText(QCoreApplication::translate("FilePropertiesDialog", "-", nullptr));
        label_type->setText(QCoreApplication::translate("FilePropertiesDialog", "\347\261\273\345\236\213\357\274\232", nullptr));
        label_type_value->setText(QCoreApplication::translate("FilePropertiesDialog", "-", nullptr));
        label_size->setText(QCoreApplication::translate("FilePropertiesDialog", "\345\244\247\345\260\217\357\274\232", nullptr));
        label_size_value->setText(QCoreApplication::translate("FilePropertiesDialog", "-", nullptr));
        label_hash->setText(QCoreApplication::translate("FilePropertiesDialog", "\345\223\210\345\270\214\345\200\274\357\274\232", nullptr));
        label_hash_value->setText(QCoreApplication::translate("FilePropertiesDialog", "-", nullptr));
        label_create_time->setText(QCoreApplication::translate("FilePropertiesDialog", "\345\210\233\345\273\272\346\227\266\351\227\264\357\274\232", nullptr));
        label_create_time_value->setText(QCoreApplication::translate("FilePropertiesDialog", "-", nullptr));
        label_modify_time->setText(QCoreApplication::translate("FilePropertiesDialog", "\344\277\256\346\224\271\346\227\266\351\227\264\357\274\232", nullptr));
        label_modify_time_value->setText(QCoreApplication::translate("FilePropertiesDialog", "-", nullptr));
        label_path->setText(QCoreApplication::translate("FilePropertiesDialog", "\350\267\257\345\276\204\357\274\232", nullptr));
        label_path_value->setText(QCoreApplication::translate("FilePropertiesDialog", "-", nullptr));
        pushButton_ok->setText(QCoreApplication::translate("FilePropertiesDialog", "\347\241\256\345\256\232", nullptr));
    } // retranslateUi

};

namespace Ui {
    class FilePropertiesDialog: public Ui_FilePropertiesDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FILEPROPERTIESDIALOG_H
