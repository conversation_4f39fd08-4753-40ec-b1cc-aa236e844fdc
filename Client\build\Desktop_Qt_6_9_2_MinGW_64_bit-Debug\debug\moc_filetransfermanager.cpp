/****************************************************************************
** Meta object code from reading C++ file 'filetransfermanager.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../filetransfermanager.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'filetransfermanager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN19FileTransferManagerE_t {};
} // unnamed namespace

template <> constexpr inline auto FileTransferManager::qt_create_metaobjectdata<qt_meta_tag_ZN19FileTransferManagerE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "FileTransferManager",
        "taskStatusChanged",
        "",
        "taskId",
        "TransferStatus",
        "status",
        "taskProgressUpdated",
        "bytesTransferred",
        "fileSize",
        "taskSpeedUpdated",
        "speed",
        "remainingTime",
        "taskAdded",
        "chunkReadyToSend",
        "offset",
        "data",
        "requestNextChunk",
        "size",
        "taskRetried",
        "onSpeedCalculationTimeout",
        "sendNextChunk",
        "handleChunkSent",
        "success",
        "handleChunkReceived",
        "handleChunkError",
        "error",
        "retryTransfer"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'taskStatusChanged'
        QtMocHelpers::SignalData<void(quint32, TransferStatus)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 3 }, { 0x80000000 | 4, 5 },
        }}),
        // Signal 'taskProgressUpdated'
        QtMocHelpers::SignalData<void(quint32, quint64, quint32)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 3 }, { QMetaType::ULongLong, 7 }, { QMetaType::UInt, 8 },
        }}),
        // Signal 'taskSpeedUpdated'
        QtMocHelpers::SignalData<void(quint32, quint32, int)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 3 }, { QMetaType::UInt, 10 }, { QMetaType::Int, 11 },
        }}),
        // Signal 'taskAdded'
        QtMocHelpers::SignalData<void(quint32)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 3 },
        }}),
        // Signal 'chunkReadyToSend'
        QtMocHelpers::SignalData<void(quint32, quint64, const QByteArray &)>(13, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 3 }, { QMetaType::ULongLong, 14 }, { QMetaType::QByteArray, 15 },
        }}),
        // Signal 'requestNextChunk'
        QtMocHelpers::SignalData<void(quint32, quint64, quint32)>(16, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 3 }, { QMetaType::ULongLong, 14 }, { QMetaType::UInt, 17 },
        }}),
        // Signal 'taskRetried'
        QtMocHelpers::SignalData<void(quint32)>(18, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 3 },
        }}),
        // Slot 'onSpeedCalculationTimeout'
        QtMocHelpers::SlotData<void()>(19, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'sendNextChunk'
        QtMocHelpers::SlotData<void(quint32)>(20, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 3 },
        }}),
        // Slot 'handleChunkSent'
        QtMocHelpers::SlotData<void(quint32, bool)>(21, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 3 }, { QMetaType::Bool, 22 },
        }}),
        // Slot 'handleChunkReceived'
        QtMocHelpers::SlotData<void(quint32, const QByteArray &)>(23, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 3 }, { QMetaType::QByteArray, 15 },
        }}),
        // Slot 'handleChunkError'
        QtMocHelpers::SlotData<void(quint32, const QString &)>(24, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 3 }, { QMetaType::QString, 25 },
        }}),
        // Slot 'retryTransfer'
        QtMocHelpers::SlotData<void(quint32)>(26, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 3 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<FileTransferManager, qt_meta_tag_ZN19FileTransferManagerE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject FileTransferManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN19FileTransferManagerE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN19FileTransferManagerE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN19FileTransferManagerE_t>.metaTypes,
    nullptr
} };

void FileTransferManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<FileTransferManager *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->taskStatusChanged((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<TransferStatus>>(_a[2]))); break;
        case 1: _t->taskProgressUpdated((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<quint64>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<quint32>>(_a[3]))); break;
        case 2: _t->taskSpeedUpdated((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<quint32>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 3: _t->taskAdded((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1]))); break;
        case 4: _t->chunkReadyToSend((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<quint64>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QByteArray>>(_a[3]))); break;
        case 5: _t->requestNextChunk((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<quint64>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<quint32>>(_a[3]))); break;
        case 6: _t->taskRetried((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1]))); break;
        case 7: _t->onSpeedCalculationTimeout(); break;
        case 8: _t->sendNextChunk((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1]))); break;
        case 9: _t->handleChunkSent((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 10: _t->handleChunkReceived((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QByteArray>>(_a[2]))); break;
        case 11: _t->handleChunkError((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 12: _t->retryTransfer((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (FileTransferManager::*)(quint32 , TransferStatus )>(_a, &FileTransferManager::taskStatusChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileTransferManager::*)(quint32 , quint64 , quint32 )>(_a, &FileTransferManager::taskProgressUpdated, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileTransferManager::*)(quint32 , quint32 , int )>(_a, &FileTransferManager::taskSpeedUpdated, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileTransferManager::*)(quint32 )>(_a, &FileTransferManager::taskAdded, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileTransferManager::*)(quint32 , quint64 , const QByteArray & )>(_a, &FileTransferManager::chunkReadyToSend, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileTransferManager::*)(quint32 , quint64 , quint32 )>(_a, &FileTransferManager::requestNextChunk, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (FileTransferManager::*)(quint32 )>(_a, &FileTransferManager::taskRetried, 6))
            return;
    }
}

const QMetaObject *FileTransferManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *FileTransferManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN19FileTransferManagerE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int FileTransferManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 13)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 13)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 13;
    }
    return _id;
}

// SIGNAL 0
void FileTransferManager::taskStatusChanged(quint32 _t1, TransferStatus _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1, _t2);
}

// SIGNAL 1
void FileTransferManager::taskProgressUpdated(quint32 _t1, quint64 _t2, quint32 _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1, _t2, _t3);
}

// SIGNAL 2
void FileTransferManager::taskSpeedUpdated(quint32 _t1, quint32 _t2, int _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1, _t2, _t3);
}

// SIGNAL 3
void FileTransferManager::taskAdded(quint32 _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void FileTransferManager::chunkReadyToSend(quint32 _t1, quint64 _t2, const QByteArray & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1, _t2, _t3);
}

// SIGNAL 5
void FileTransferManager::requestNextChunk(quint32 _t1, quint64 _t2, quint32 _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1, _t2, _t3);
}

// SIGNAL 6
void FileTransferManager::taskRetried(quint32 _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1);
}
QT_WARNING_POP
