#include "loginwindow.h"
#include "logger.h"
#include <QMessageBox>
#include <QRegularExpression>
#include <QRegularExpressionValidator>

// 构造函数
LoginWindow::LoginWindow(QWidget *parent)
    : QMainWindow(parent), ui(new Ui::LoginWindow), m_networkManager(nullptr),
      m_configManager(nullptr), m_settingsDialog(nullptr),
      m_registerDialog(nullptr), m_mainWindow(nullptr) {
  // 初始化UI
  ui->setupUi(this);

  // 初始化成员变量
  m_networkManager = NetworkManager::getInstance();
  m_configManager = ConfigManager::getInstance();

  // 初始化UI
  initUI();

  // 加载配置
  loadConfig();

  // 连接信号槽
  connect(m_networkManager, &NetworkManager::connectionStateChanged, this,
          &LoginWindow::onConnectionStateChanged);
  connect(m_networkManager, &NetworkManager::loginResponse, this,
          &LoginWindow::onLoginResponse);
  connect(m_networkManager, &NetworkManager::registerResponse, this,
          &LoginWindow::onRegisterResponse);
  connect(m_networkManager, &NetworkManager::errorOccurred, this,
          &LoginWindow::onErrorOccurred);
}

// 析构函数
LoginWindow::~LoginWindow() {
  // 清理资源
  if (m_settingsDialog) {
    delete m_settingsDialog;
    m_settingsDialog = nullptr;
  }

  if (m_registerDialog) {
    delete m_registerDialog;
    m_registerDialog = nullptr;
  }

  if (m_mainWindow) {
    delete m_mainWindow;
    m_mainWindow = nullptr;
  }

  delete ui;
}

// 初始化UI
void LoginWindow::initUI() {
  // 设置窗口标题
  setWindowTitle("Cloud7 - 登录");

  // 设置窗口图标
  setWindowIcon(QIcon(":/icons/cloud.png"));

  // 设置用户名输入框验证器（允许字母、数字、下划线和@符号）
  QRegularExpression usernameRegExp("[a-zA-Z0-9_@]+");
  QValidator *usernameValidator =
      new QRegularExpressionValidator(usernameRegExp, this);
  ui->lineEdit_username->setValidator(usernameValidator);

  // 设置密码输入框验证器（不允许空格）
  QRegularExpression passwordRegExp("\\S+");
  QValidator *passwordValidator =
      new QRegularExpressionValidator(passwordRegExp, this);
  ui->lineEdit_password->setValidator(passwordValidator);

  // 设置登录按钮为默认按钮
  ui->pushButton_login->setDefault(true);

  // 设置状态栏
  ui->statusbar->showMessage("未连接到服务器");
}

// 加载配置
void LoginWindow::loadConfig() {
  // 获取客户端配置
  ClientConfig config = m_configManager->getClientConfig();

  // 如果记住用户名，则设置用户名输入框
  if (config.rememberUsername && !config.lastUsername.isEmpty()) {
    ui->lineEdit_username->setText(config.lastUsername);
    ui->checkBox_remember->setChecked(true);
  }
}

// 保存配置
void LoginWindow::saveConfig() {
  // 获取客户端配置
  ClientConfig config = m_configManager->getClientConfig();

  // 设置记住用户名选项
  config.rememberUsername = ui->checkBox_remember->isChecked();

  // 如果记住用户名，则保存用户名
  if (config.rememberUsername) {
    config.lastUsername = ui->lineEdit_username->text();
  } else {
    config.lastUsername = "";
  }

  // 保存配置
  m_configManager->setClientConfig(config);
}

// 验证输入
bool LoginWindow::validateInput() {
  // 获取用户名和密码
  QString username = ui->lineEdit_username->text().trimmed();
  QString password = ui->lineEdit_password->text();

  // 验证用户名
  if (username.isEmpty()) {
    QMessageBox::warning(this, "输入错误", "请输入用户名或邮箱");
    ui->lineEdit_username->setFocus();
    return false;
  }

  // 验证密码
  if (password.isEmpty()) {
    QMessageBox::warning(this, "输入错误", "请输入密码");
    ui->lineEdit_password->setFocus();
    return false;
  }

  // 验证密码长度（至少6位）
  if (password.length() < 6) {
    QMessageBox::warning(this, "输入错误", "密码长度至少为6位");
    ui->lineEdit_password->setFocus();
    return false;
  }

  return true;
}

// 登录按钮点击槽函数
void LoginWindow::on_pushButton_login_clicked() {
  // 验证输入
  if (!validateInput()) {
    return;
  }

  // 保存配置
  saveConfig();

  // 获取用户名和密码
  QString username = ui->lineEdit_username->text().trimmed();
  QString password = ui->lineEdit_password->text();

  // 禁用登录按钮，防止重复点击
  ui->pushButton_login->setEnabled(false);
  ui->statusbar->showMessage("正在连接服务器...");

  // 获取服务器配置
  ClientConfig config = m_configManager->getClientConfig();

  // 连接服务器
  if (!m_networkManager->isConnected()) {
    m_networkManager->connectToServer(config.serverHost, config.serverPort);
  } else {
    // 如果已经连接，直接发送登录请求
    m_networkManager->loginRequest(username, password);
  }
}

// 注册按钮点击槽函数
void LoginWindow::on_pushButton_register_clicked() {
  // 如果注册对话框不存在，则创建
  if (!m_registerDialog) {
    m_registerDialog = new RegisterDialog(this);

    // 连接信号槽
    connect(m_registerDialog, &RegisterDialog::registerRequested,
            m_networkManager, &NetworkManager::registerRequest);
  }

  // 显示注册对话框
  m_registerDialog->show();
  m_registerDialog->raise();
  m_registerDialog->activateWindow();
}

// 服务器设置按钮点击槽函数
void LoginWindow::on_pushButton_settings_clicked() {
  // 如果服务器设置对话框不存在，则创建
  if (!m_settingsDialog) {
    m_settingsDialog = new ServerSettingsDialog(this);
  }

  // 显示服务器设置对话框
  m_settingsDialog->show();
  m_settingsDialog->raise();
  m_settingsDialog->activateWindow();
}

// 网络连接状态改变槽函数
void LoginWindow::onConnectionStateChanged(bool connected) {
  // 更新状态栏
  if (connected) {
    ui->statusbar->showMessage("已连接到服务器");

    // 如果已经输入了用户名和密码，则自动发送登录请求
    if (!ui->lineEdit_username->text().isEmpty() &&
        !ui->lineEdit_password->text().isEmpty()) {
      QString username = ui->lineEdit_username->text().trimmed();
      QString password = ui->lineEdit_password->text();
      m_networkManager->loginRequest(username, password);
    } else {
      // 启用登录按钮
      ui->pushButton_login->setEnabled(true);
    }
  } else {
    ui->statusbar->showMessage("未连接到服务器");

    // 启用登录按钮
    ui->pushButton_login->setEnabled(true);
  }
}

// 登录响应槽函数
void LoginWindow::onLoginResponse(bool success, const QString &message,
                                  const UserInfo &userInfo) {
  // 启用登录按钮
  ui->pushButton_login->setEnabled(true);

  // 处理登录响应
  if (success) {
    LOG_INFO("Login", "登录成功");

    // 保存配置
    saveConfig();

    // 隐藏登录窗口
    hide();

    // 如果主窗口不存在，则创建
    if (!m_mainWindow) {
      m_mainWindow = new MainWindow();
    }

    // 设置当前用户信息
    m_mainWindow->setCurrentUser(userInfo);

    // 显示主窗口
    m_mainWindow->show();
  } else {
    LOG_ERROR("Login", QString("登录失败: %1").arg(message));
    QMessageBox::warning(this, "登录失败", message);
    ui->statusbar->showMessage("登录失败");
  }
}

// 注册响应槽函数
void LoginWindow::onRegisterResponse(bool success, const QString &message) {
  // 处理注册响应
  if (success) {
    LOG_INFO("Login", QString("注册成功: %1").arg(message));
    QMessageBox::information(this, "注册成功", message);

    // 关闭注册对话框
    if (m_registerDialog) {
      m_registerDialog->close();
    }
  } else {
    LOG_ERROR("Login", QString("注册失败: %1").arg(message));
    QMessageBox::warning(this, "注册失败", message);
  }
}

// 错误处理槽函数
void LoginWindow::onErrorOccurred(const QString &error) {
  LOG_ERROR("Login", QString("发生错误: %1").arg(error));
  QMessageBox::critical(this, "错误", error);

  // 启用登录按钮
  ui->pushButton_login->setEnabled(true);

  // 更新状态栏
  ui->statusbar->showMessage("发生错误: " + error);
}
